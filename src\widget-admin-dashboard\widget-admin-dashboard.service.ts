import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateWidgetAdminDashboardDto } from './dto/create-widget-admin-dashboard.dto';
import { SendPurchaseOrderNumberDto, UpdateWidgetAdminDashboardDto, saveSafeImgixImageKit,NotificationEventDto, UpdateResalesCertificateDto, EmailValidDto, UpdateSecurityKeyDto, MobileReleaseUrlDto, VideoLibraryDto, updateVideoLibraryDto, VideoTagsDto, UpdateVideoTagsDto, HideRecordSellerInvoiceDto, PurchaseOrderNumberDto, AchCreditRequestDto, UserStatusDto, IncreaseCreditLimitDataDto, HolidayCalendarDto, ExternalApiKeyMappingDto, VideoUploadIdDto, VideoUploadMultipartUrlDto, VideoCompleteMultipartUploadUrlDto, ChangeGameStateDto, ConfidenceLevelConfigDto, SubscriptionPricingDto } from './dto/update-widget-admin-dashboard.dto';
import { DataBaseService, BaseLibraryService, ReferenceDataSettings, AwsUtilityV3, Constants as libConstants, CacheManagerService } from '@bryzos/base-library';
import { Repository, Connection, Compressor } from 'typeorm';
import { UserPurchaseOrder, UserPurchaseOrderLine, UserPurchaseOrderLedger, AdminLogUpdateOrderQuantity, UserResaleCertificate, CompanyBuyNowPayLater, LogAuthAmount,ReferenceDataStates, ReferenceDataSalesTax, ReferenceDataOrderStatus, LogBryzosCreditLimit, AdminLogNotification, ReferenceDataDesktopNotification, AdminLogInvoiceEmail,  User, UserBuyingPreference, AdminLogFlipAchToBnpl,ReferenceDataUserOnboardedAppVersion, HomepageSafeUploads, HomepageSafeConfig, TaxExemptedPurchaseOrders,ReferenceDataSpreadDefaultValues,OrderLineSnapshotPricing, AdminLogUpdateOrderDetails, UserViewedPurchaseOrder, ReferenceDataRecipientEmailSettings, BryzosLogger, ReferenceDataUiReleaseUrls,ReferenceDataBryzosTermsConditions, HomepageSafeUploadComments, ExtendedWidgetVideoLibrary, ReferenceDataVideoLibraryTag, SellerInvoiceDump,  AdminLogAchCreditPoRequest, AdminLogUserUpdate, ExceptionService, UserRequestIncreaseCredits, Constants as LibConstants, ExtendedWidgetHolidayCalendar, SignupUtility, SecretManagerUtility, ReferenceDataExternalApiEndpoint, ExternalApiKey, AwsUtility, SandboxExternalApiKey, ReferenceDataBomProductConfidence, ReferenceDataSubscriptionPricingTiers, ReferenceDataProductsExcelLogVersion} from '@bryzos/extended-widget-library';

import { InjectRepository } from '@nestjs/typeorm';
import { Constants } from 'src/Constants';
import { HttpService } from '@nestjs/axios';
import { AwsQueue } from 'src/AwsQueue';
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import { Balance } from 'src/Balance';
import { Utils } from 'src/utils';
import { v4 as uuidv4 } from 'uuid';
import * as moment from 'moment';
import { HideHomePageCarouselDto, SafeUploadCommentsDto } from 'src/homepage/dto/homepage-safe-uploads.dto';
import { ElasticBeanstalkClient, RestartAppServerCommand } from "@aws-sdk/client-elastic-beanstalk"; // ES Modules import
import { ResaleCertificateService } from './resale-certificate/resale-certificate.service';
import { createHash } from 'crypto';
import { UserAchCreditService } from './user-ach-credit/user-ach-credit.service';
import { UserService } from '../user/user.service';


const responseTag = Constants.RESPONSE_TAG;
const responseErrorTag = Constants.ERROR_TAG;
@Injectable()
export class WidgetAdminDashboardService {
  private dbServiceObj = new DataBaseService()
  private cacheManager = new CacheManagerService(process.env.CACHE_SERVER, process.env.MEMCACHED_SERVER_ENDPOINT)
  constructor(
    private readonly baseLibraryService : BaseLibraryService,
    private readonly httpService : HttpService,
    private readonly awsQueue : AwsQueue,
    private readonly awsUtility: AwsUtilityV3,
    private readonly balance: Balance,
    private readonly utils: Utils,
    private readonly resaleCertificateService: ResaleCertificateService,
    private readonly userAchCreditService: UserAchCreditService,
    private readonly userService: UserService,
    private readonly signupUtility: SignupUtility,
    private readonly secretManager : SecretManagerUtility,
    private readonly extendedLibAwsUtility: AwsUtility,


    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(UserPurchaseOrderLedger) private readonly userPurchaseOrderLedgerRepository: Repository<UserPurchaseOrderLedger>,
    @InjectRepository(AdminLogUpdateOrderQuantity) private readonly adminLogUpdateOrderQuantityRepository: Repository<AdminLogUpdateOrderQuantity>,
    @InjectRepository(UserResaleCertificate) private readonly userReasaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(LogAuthAmount) private readonly logAuthAmountRepository: Repository<LogAuthAmount>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly userReferenceDataOrderStatusRepository: Repository<ReferenceDataOrderStatus>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(LogBryzosCreditLimit) private readonly logBryzosCreditLimitRepository: Repository<LogBryzosCreditLimit>,
    @InjectRepository(AdminLogNotification) private readonly adminLogNotificationRepository: Repository<AdminLogNotification>,
    @InjectRepository(ReferenceDataDesktopNotification) private readonly referenceDataDesktopNotificationRepository : Repository<ReferenceDataDesktopNotification>,
    @InjectRepository(AdminLogInvoiceEmail) private readonly adminLogInvoiceEmailRepository : Repository<AdminLogInvoiceEmail>,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    @InjectRepository(AdminLogFlipAchToBnpl) private readonly adminLogFlipAchToBnplRepository: Repository<AdminLogFlipAchToBnpl>,
    @InjectRepository(ReferenceDataUserOnboardedAppVersion) private readonly referenceDataUserOnboardedAppVersionRepository: Repository<ReferenceDataUserOnboardedAppVersion>,
    @InjectRepository(HomepageSafeUploads) private readonly homepageSafeUploadsRepository: Repository<HomepageSafeUploads>,
    @InjectRepository(HomepageSafeConfig) private readonly homepageSafeConfigRepository: Repository<HomepageSafeConfig>,
    @InjectRepository(TaxExemptedPurchaseOrders) private readonly taxExemptedPoRepository: Repository<TaxExemptedPurchaseOrders>,
    @InjectRepository(ReferenceDataSpreadDefaultValues) private readonly referenceDataSpreadDefaultValuesRepository: Repository<ReferenceDataSpreadDefaultValues>,
    @InjectRepository(OrderLineSnapshotPricing) private readonly orderLineSnapshotPricingRepository: Repository<OrderLineSnapshotPricing>,
    @InjectRepository(AdminLogUpdateOrderDetails) private readonly adminLogUpdateOrderDetailsRepository: Repository<AdminLogUpdateOrderDetails>,
    @InjectRepository(UserViewedPurchaseOrder) private readonly userViewedPurchaseOrderRepository: Repository<UserViewedPurchaseOrder>,
    @InjectRepository(ReferenceDataRecipientEmailSettings) private readonly referenceDataRecipientEmailSettingsRepository: Repository<ReferenceDataRecipientEmailSettings>,
    @InjectRepository(ReferenceDataUiReleaseUrls) private readonly referenceDataUiReleaseUrlsRepository: Repository<ReferenceDataUiReleaseUrls>,
    @InjectRepository(ReferenceDataBryzosTermsConditions) private readonly referenceDataBryzosTermsConditionsRepository: Repository<ReferenceDataBryzosTermsConditions>,
    @InjectRepository(HomepageSafeUploadComments) private readonly homepageSafeUploadCommentsRepo: Repository<HomepageSafeUploadComments>,
    @InjectRepository(ExtendedWidgetVideoLibrary) private readonly extendedWidgetVideoLibraryRepository: Repository<ExtendedWidgetVideoLibrary>,
    @InjectRepository(ReferenceDataVideoLibraryTag) private readonly referenceDataVideoLibraryTagRepository: Repository<ReferenceDataVideoLibraryTag>,
    @InjectRepository(SellerInvoiceDump) private readonly sellerInvoiceDumpRepository: Repository<SellerInvoiceDump>,
    @InjectRepository(AdminLogAchCreditPoRequest) private readonly adminLogAchCreditPoRequestRepository: Repository<AdminLogAchCreditPoRequest>,
    @InjectRepository(AdminLogUserUpdate) private readonly adminLogUserUpdateRepository: AdminLogUserUpdate,
    @InjectRepository(UserRequestIncreaseCredits) private readonly userRequestIncreaseCreditsRepository: Repository<UserRequestIncreaseCredits>,
    @InjectRepository(ExtendedWidgetHolidayCalendar) private readonly extendedWidgetHolidayCalendarRepository: Repository<ExtendedWidgetHolidayCalendar>,
    @InjectRepository(ReferenceDataExternalApiEndpoint) private readonly refExternalApiEndpointRepository: Repository<ReferenceDataExternalApiEndpoint>,
    @InjectRepository(ExternalApiKey) private readonly externalApiKeyRepository: Repository<ExternalApiKey>,
    @InjectRepository(SandboxExternalApiKey) private readonly sandboxExternalApiKeyRepository: Repository<SandboxExternalApiKey>,
    @InjectRepository(ReferenceDataBomProductConfidence) private readonly referenceDataBomProductConfidenceRepository: Repository<ReferenceDataBomProductConfidence>,
    @InjectRepository(ReferenceDataSubscriptionPricingTiers) private readonly referenceDataSubscriptionPricingTiersRepository: Repository<ReferenceDataSubscriptionPricingTiers>,
    @InjectRepository(ReferenceDataProductsExcelLogVersion) private readonly refDataProductsExcelLogVersionRepository: Repository<ReferenceDataProductsExcelLogVersion>,

  
  ){}
    
  create(createWidgetAdminDashboardDto: CreateWidgetAdminDashboardDto) {
    return 'This action adds a new widgetAdminDashboard';
  }

  findAll() {
    return `This action returns all widgetAdminDashboard`;
  }

  findOne(id: number) {
    return `This action returns a #${id} widgetAdminDashboard`;
  }

  update(id: number, updateWidgetAdminDashboardDto: UpdateWidgetAdminDashboardDto) {
    return `This action updates a #${id} widgetAdminDashboard`;
  }

  remove(id: number) {
    return `This action removes a #${id} widgetAdminDashboard`;
  }

  async save_widget_order_actual_qty_admin_logs(data,admin_id){
    const insert = {};
    insert['user_id'] = data['buyer_id'];
    insert['sales_tax'] = data['sales_tax'];
    insert['po_number']= data['buyer_po_number'];
    insert['line_id'] = data['line_id'];
    insert['orig_qty'] = data['orig_qty'];
    insert['actual_qty'] = data['actual_qty'];
    insert['actual_buyer_extended'] = data['actual_buyer_line_total'];
    insert['actual_seller_extended'] = data['actual_seller_line_total'];
    insert['purchase_order_line_id'] = data['purchase_order_line_id'];
    insert['old_sales_tax'] = data['old_sales_tax'];
    insert['admin_id'] = admin_id;
    await this.dbServiceObj.saveOrUpdateReferenceData(insert,this.adminLogUpdateOrderQuantityRepository);
  }

  async updateSellerExtendedPrice(payload, adminId) {

    const poNumber = payload.po_number;
    const poLineId = payload.po_line_id;
    const updatedExtendedPrice = payload.actual_seller_extended_price;

   
    //fetch line data

    const selectConditions = {};
    selectConditions['buyer_po_number'] = poNumber;
    selectConditions['line_id'] = poLineId;
    //order_status_id

    const poLineData = await this.dbServiceObj.findOneByMultipleWhere(
      this.userPurchaseOrderLineRepository,
      selectConditions,
    );

      if(!poLineData)
        return {"error_message":"Unable to fetch data!"}


    let oldExtPrice = poLineData.actual_seller_line_total;

    const priceDiff = Number(updatedExtendedPrice) - Number(oldExtPrice); //this price will get add in potable

      if(priceDiff == 0)
      return {"error_message":"Actual seller extended price updated and old cannot be same!"}


    //update in user_purchase_order_line
    const poLineUpdateResult = await this.updateExtPrice(
      poNumber,
      poLineId,
      updatedExtendedPrice,
    );

   
    if (poLineUpdateResult.error_message) return poLineUpdateResult;

    //2. update in purchase order

    const poData = await this.dbServiceObj.findOne(
      this.userPurchaseOrderRepository,
      'buyer_po_number',
      poNumber,
    );

    if(!poData)
    return {"error_message":"Unable to fetch data!"}

    //check if actual_seller_po_price is present if not take seller_po_price
    
    let actual_seller_po_price = poData.actual_seller_po_price? poData.actual_seller_po_price : poData.seller_po_price ;

   
    //updation in po table
    let priceToUpdatePo = Number(actual_seller_po_price) + Number(priceDiff); // check negative and positive cases

    let poTableExtPriceDto = {
      actual_seller_po_price: priceToUpdatePo,
    };

    let poUpdateRes = await this.dbServiceObj.updateByColumnId(
      this.userPurchaseOrderRepository,
      poTableExtPriceDto,
      'buyer_po_number',
      poNumber,
    );

      if(!poUpdateRes)
        return{"error_message":"unable to update database!"}


    ////step 3 update in ledger table

   
    let newEscrow = priceDiff; // sum of all esrow will be equal to letest actual_seller_line_total.
    // and new_actual_seller_line_total - old = new esrow to add

    let saveLedger = {
      user_id: poData.buyer_id,
      purchase_order_line_id: poLineData.id,
      escrow: newEscrow,
      transaction_type: Constants.TRANSACTION_TYPE_BA_RCDC,
    };
    
    let ledgerEntryResp;
    //adding entry in ledger table
    
     ledgerEntryResp = await this.dbServiceObj.saveData(
      saveLedger,
      this.userPurchaseOrderLedgerRepository,
    );
    if(!ledgerEntryResp)
    return {"error_message":"unable to add entry in ledger!"}
    

    if(!ledgerEntryResp)
      console.log("cannot add entry in ledger! new seller extended price and old seller extended price is same.");

    const insert = {};

    insert['user_id'] = poData.buyer_id; 
    insert['po_number'] = poNumber;
    insert['line_id'] = poLineId;
    insert['actual_seller_extended'] = oldExtPrice;
    insert['admin_id'] = adminId;
    insert['purchase_order_line_id'] = poLineData.id;

    let adminQtyLogResp; 

     adminQtyLogResp = await this.dbServiceObj.saveData(
      insert,
      this.adminLogUpdateOrderQuantityRepository,
    );
    
    if(!adminQtyLogResp)
    console.log("Insert in admin log update qty failed");

     const successResponse = "Seller extended price updated successfully"
    return successResponse;
  }

  async getUserPurchaseOrderData(poNumber: string) {
    return this.dbServiceObj.findOne(this.userPurchaseOrderRepository, 'buyer_po_number', poNumber);
  }

  async getUserPurchaseOrderLineData(userPurchaseOrderId: string) {
    return this.dbServiceObj.findMany(this.userPurchaseOrderLineRepository, 'purchase_order_id', userPurchaseOrderId);
  }

  async getUserPurchaseOrderLedgerData(userPurchaseOrderLineData) {
    const purchaseOrderLineIds = userPurchaseOrderLineData.map((obj) => obj.id);

    return this.dbServiceObj.findManyByWhereIn(this.userPurchaseOrderLedgerRepository, 'purchase_order_line_id', purchaseOrderLineIds);
  }

  async getCompanyBuyNowPayLaterData(buyerId: string) {
    return this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository, 'user_id', buyerId);
  }

  async getUserData(buyerId) {
    return this.dbServiceObj.findOne(this.userRepository, 'id', buyerId);
  }

  async getReferenceDataSetting() {
    return (await this.dbServiceObj.findAll(this.referenceDataSettingsRepository));
  }

  async transferAchToBnpl(poNumber: string) {
    try {
      let response = null;
      const logEvent = 'Convert ACH_CREDIT to BNPL';

      let userPurchaseOrderData = await this.getUserPurchaseOrderData(poNumber);
      if (!userPurchaseOrderData) {
        return { [responseErrorTag]: 'Po not found' };
      } else if (userPurchaseOrderData.payment_method !== Constants.PAYMENT_METHOD_ACH_CREDIT) {
        return { [responseErrorTag]: `Payment method must be ${Constants.PAYMENT_METHOD_ACH_CREDIT}` };
      } else if (+userPurchaseOrderData.is_ach_po_approved !== 1) {
        return { [responseErrorTag]: 'Ach po is not approved' }
      }

      let buyerId = userPurchaseOrderData.buyer_id;

      let userPurchaseOrderLineData = await this.getUserPurchaseOrderLineData(userPurchaseOrderData.id);
      if (!userPurchaseOrderLineData.length) {
        return { [responseErrorTag]: 'Order not found' };
      }

      let userPurchaseOrderLedgerData = await this.getUserPurchaseOrderLedgerData(userPurchaseOrderLineData);
      if (!userPurchaseOrderLedgerData) {
        return { [responseErrorTag]: 'Purchase order line not found' };
      }

      const totalPrice = await this.calculateTotalPrice(userPurchaseOrderLedgerData);

      const companyBuyNowPayLaterData = await this.getCompanyBuyNowPayLaterData(buyerId);
      if (!companyBuyNowPayLaterData) {
        return { [responseErrorTag]: 'Your bnpl is not setup!' };
      }

      if (companyBuyNowPayLaterData.is_approved !== 1) {
        return { [responseErrorTag]: 'User not approved for bnpl payment!' };
      }

      const referenceDataSettings = await this.getReferenceDataSetting();

      const addColumnValueForSellerFid = referenceDataSettings.find(data => data.name === Constants.ADD_COLUMN_VALUE_FOR_SELLER_FID)?.value; // delivery_date
      const pgGatewayBnplSellerFidAddDays = Number(referenceDataSettings.find(data => data.name === Constants.PG_GATEWAY_BNPL_SELLER_FID_ADD_DAYS)?.value); // 30
      const deliveryDate = userPurchaseOrderData[addColumnValueForSellerFid];
      const sellerFundingDate = moment(deliveryDate).add(pgGatewayBnplSellerFidAddDays, 'days').format('YYYY-MM-DD HH:mm:ss');

      const authAmountPercentage = Number(companyBuyNowPayLaterData.auth_amount_percentage);
      let authAmount = null;
      let chargeDate = null;

      const userData = await this.getUserData(buyerId);
      if (!userData) {
        return { [responseErrorTag]: 'User not found' };
      }

      let transactionId = null;

      const checkoutViaBalance = referenceDataSettings.find(data => data.name === Constants.CHECKOUT_VIA_BALANCE)?.value;
      if (checkoutViaBalance === Constants.ON) {
        authAmount = totalPrice + (authAmountPercentage > 0 ? totalPrice * authAmountPercentage / 100 : 0);
        chargeDate = moment().utc().add(companyBuyNowPayLaterData.charges_date, 'days').format('YYYY-MM-DD HH:mm:ss');

        let s3Key = `${userData.unique_key}-${Constants.BALANCE_BUYER_ID}`;

        let s3BuyerId = await this.awsUtility.getS3Element(s3Key, process.env.PAYMENT_BUCKET);
        if (!s3BuyerId) {
          return { [responseErrorTag]: 'Unable to found buyer id!' };
        }

        let s3BuyerBalanceId = JSON.parse(s3BuyerId).balance_buyer_id;

        let buyerCreditData = await this.balance.getBalanceCreditLimit(s3BuyerBalanceId);
        let buyerCredits = buyerCreditData.balance_available_credit_limit;

        this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Step 1', message_attribute: JSON.stringify(buyerCreditData) }, info: 'Buyer Balance Credit Limit Response' });

        if (buyerCredits < authAmount) {
          this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Exception 1', message_attribute: JSON.stringify({ totalPrice: totalPrice, auth_amount: authAmount }), info: 'Insufficient Balance!' } });
          return { [responseErrorTag]: "Insufficient balance" };
        }

        if (buyerCreditData.status?.toLocaleLowerCase() !== Constants.BALANCE_ACCOUNT_APPROVED.toLocaleLowerCase()) {
          this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Exception 1.2', message_attribute: JSON.stringify(buyerCreditData), info: 'Balance not approved' } });
          return { [responseErrorTag]: "Balance not approved" };
        }

        let userBuyingPreferenceData = await this.dbServiceObj.findOne(this.userBuyingPreferenceRepository, 'user_id', buyerId);
        if (!userBuyingPreferenceData) {
          return { [responseErrorTag]: "Unable to find user buying preference data" };
        }

        let statementDescriptor = this.cleanStatementDescriptor(`${poNumber} ${userBuyingPreferenceData.company_name}`)?.substring(0, 22);

        let toEmail = referenceDataSettings.find(data => data.name === Constants.COMMUNICATION_CONFIG_TO_EMAIL)?.value;
        let bccEmail = referenceDataSettings.find(data => data.name === Constants.COMMUNICATION_CONFIG_BCC_EMAIL)?.value;
        let planType = referenceDataSettings.find(data => data.name === Constants.BALANCE_PLAN_TYPE)?.value;
        let autoPayoutVal = referenceDataSettings.find(data => data.name === Constants.BALANCE_AUTO_PAYOUT)?.value;

        if (!(toEmail || bccEmail || planType || autoPayoutVal)) {
          return { [responseErrorTag]: 'Sorry unable to fetch data from database!' };
        }

        let balanceVendorId = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.DEFAULT_VENDOR);
        if (!balanceVendorId) {
          return { [responseErrorTag]: 'Vendor balance key is not present!' };
        }

        const createTransactionPayload = {
          financingConfig: { financingNetDays: Number(companyBuyNowPayLaterData.net_terms_days) },
          communicationConfig: { emailsTo: toEmail, emailsCc: bccEmail },
          statementDescriptor: { charge: statementDescriptor },
          buyer: { email: userData.email_id, id: s3BuyerBalanceId },
          plan: { planType: planType, chargeDate: chargeDate },
          lines: [{ lineItems: [{ itemType: Constants.BALANCE_LINE_ITEM_TYPE, price: totalPrice, quantity: 1, title: poNumber }], shippingPrice: 0, vendorId: balanceVendorId }],
          externalReferenceId: poNumber,
          notes: poNumber,
          autoPayouts: autoPayoutVal === Constants.ON,
          currency: 'USD',
          authAmount: authAmount,
        };

        this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Step 2', message_attribute: JSON.stringify(createTransactionPayload) }, info: 'Charges Data' });

        let transactionResults = await this.balance.createTransaction(createTransactionPayload);

        const createTransactionTag = (!transactionResults || !transactionResults.id || transactionResults[responseErrorTag]) ? "NODE_AD_CREATE_TRANSACTION_ERROR_TAG" : "NODE_AD_CREATE_TRANSACTION_TAG";
        this.awsQueue.balanceLog(JSON.stringify({ create_transaction_payload: createTransactionPayload }), Buffer.from((JSON.stringify({ transaction_results: transactionResults })), "utf8").toString("base64"), 'node_admin_dashboard', buyerId, Buffer.from(process.env.BALANCE_TRANSACTION_URL, "utf8").toString("base64"), Buffer.from(createTransactionTag, "utf8").toString("base64"));

        if (!transactionResults || !transactionResults.id || transactionResults[responseErrorTag]) {
          this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Exception 2', message_attribute: transactionResults, }, info: 'Create Transaction Error' });

          return { [responseErrorTag]: 'Something went wrong. Failed to create transaction!' };
        }

        this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Step 3', message_attribute: 'Create Transaction.' }, info: 'Create Transaction' });

        transactionId = transactionResults.id;


        const balanceKey = await this.balance.getBalanceKey();
        if (!balanceKey || balanceKey.error_message) {
          return { [responseErrorTag]: 'Something went wrong. Unable to fetch balance key!' };
        }

        const financeTransactionResults = await this.balance.financeTransaction(transactionId);

        const financeTransactionTag = (!financeTransactionResults || financeTransactionResults.error_message) ? "NODE_AD_FINANCE_TRANSACTION_ERROR_TAG" : "NODE_AD_FINANCE_TRANSACTION_TAG";
        this.awsQueue.balanceLog(JSON.stringify({ transaction_id: transactionId }), Buffer.from((JSON.stringify({ finance_transaction_results: financeTransactionResults })), "utf8").toString("base64"), 'node_admin_dashboard', buyerId, Buffer.from(process.env.BALANCE_TRANSACTION_URL + '/' + transactionId + '/finance', "utf8").toString("base64"), Buffer.from(financeTransactionTag, "utf8").toString("base64"));

        if (!financeTransactionResults || financeTransactionResults.error_message) {
          this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Exception 3', message_attribute: financeTransactionResults }, info: 'Finance Transaction Error' });

          let cancelTransactionResp = await this.balance.cancelBalanceTransaction(transactionId, balanceKey);
          this.awsQueue.balanceLog(JSON.stringify({ transaction_id: transactionId, balance_key: balanceKey }), Buffer.from((JSON.stringify({ cancel_transaction_response: cancelTransactionResp })), "utf8").toString("base64"), 'node_admin_dashboard', buyerId, Buffer.from(process.env.BALANCE_TRANSACTION_URL + '/' + transactionId + '/cancel', "utf8").toString("base64"), Buffer.from('NODE_AD_CANCEL_FINANCE_TRANSACTION_TAG', "utf8").toString("base64"));

          await this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Cancel', message_attribute: 'Cancel Transaction', }, info: 'Cancel Transaction', });

          return { [responseErrorTag]: 'Failed to finance the transaction. Transaction canceled!!' };
        }

        this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Step 4', message_attribute: 'Finance Transaction', }, info: 'Finance Transaction' });

        let confirmTrasactionPayload = { paymentMethodType: 'invoice', isFinanced: true, isAuth: true };
        let confirmTransactionResults = await this.balance.confirmTransaction(transactionId, confirmTrasactionPayload);

        const confirmTransactionTag = (!confirmTransactionResults || confirmTransactionResults.error_message) ? "NODE_AD_CONFIRM_TRANSACTION_ERROR_TAG" : "NODE_AD_CONFIRM_TRANSACTION_TAG";
        this.awsQueue.balanceLog(JSON.stringify({ confirm_trasaction_payload: confirmTrasactionPayload }), Buffer.from((JSON.stringify({ confirm_transaction_results: confirmTransactionResults })), "utf8").toString("base64"), 'node_admin_dashboard', buyerId, Buffer.from(process.env.BALANCE_TRANSACTION_URL + '/' + transactionId + '/confirm', "utf8").toString("base64"), Buffer.from(confirmTransactionTag, "utf8").toString("base64"));

        if (!confirmTransactionResults || confirmTransactionResults.error_message) {
          this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Exception 4', message_attribute: confirmTransactionResults, }, info: 'Confirm Transaction Error' });

          let cancelTransactionResp = await this.balance.cancelBalanceTransaction(transactionId, balanceKey);
          this.awsQueue.balanceLog(JSON.stringify({ transaction_id: transactionId, balance_key: balanceKey }), Buffer.from((JSON.stringify({ cancel_transaction_resp: cancelTransactionResp })), "utf8").toString("base64"), 'node_admin_dashboard', buyerId, Buffer.from(process.env.BALANCE_TRANSACTION_URL + '/' + transactionId + '/cancel', "utf8").toString("base64"), Buffer.from('NODE_AD_CANCEL_CONFIRM_TRANSACTION_TAG', "utf8").toString("base64"));

          this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Cancel', message_attribute: 'Cancel Transaction' }, info: 'Cancel Transaction' });

          return { [responseErrorTag]: 'Failed to confirm transaction. Transaction canceled!' };
        }

        this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Step 5', message_attribute: 'Confirm Transaction', }, info: 'Confirm Transaction', });
      } else if (checkoutViaBalance === Constants.OFF) {
        const availableCreditLimit = +companyBuyNowPayLaterData.bryzos_available_credit_limit;

        if (availableCreditLimit < totalPrice) {
          return { [responseErrorTag]: "Insufficient balance" };
        }
      }

      response = await this.updateinDB(poNumber, authAmount, totalPrice, chargeDate, transactionId, buyerId, companyBuyNowPayLaterData, checkoutViaBalance, sellerFundingDate);
      if (response?.[responseErrorTag]) {
        this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Exception 5', message_attribute: 'Update DB Queries Error' }, info: 'Update DB Queries Error' });
        return response;
      }

      this.utils.createTransactionLogData(__filename, buyerId, poNumber, logEvent, { message: { Step: 'Step 6', message_attribute: 'DB Queries', }, info: 'DB Queries' });

      const adminLogFlipAchToBnplDto = { admin_id: buyerId, po_number: poNumber, auth_amount: authAmount, total_price: totalPrice, charge_date: chargeDate, transaction_id: transactionId, seller_funding_date: sellerFundingDate };
      this.dbServiceObj.saveData(adminLogFlipAchToBnplDto, this.adminLogFlipAchToBnplRepository);

      response = 'Transaction successfull!';
      return response;

    } catch (error) {
      return { [responseErrorTag]: error.message };
    }
  }

  async calculateTotalPrice(ledgerItemLineData) {
    const totPrice = ledgerItemLineData.reduce((sum, val) => {
      const extended = Number(val.extended) ?? 0;
      const salesTax = Number(val.sales_tax) ?? 0;
      const bryzosFees = Number(val.bryzos_fees) ?? 0;

      return sum + extended + salesTax + bryzosFees;
    }, 0);

    return totPrice;
  }

  async updateExtPrice(poNumber, poLineId, extPrice) {
    let updationResult = null;

    try {
      const selectConditions = {};
      selectConditions['buyer_po_number'] = poNumber;
      selectConditions['line_id'] = poLineId;

      let extPriceDto = {
        actual_seller_line_total: extPrice,
      };

      updationResult = await this.dbServiceObj.updateByMultipleWhere(
        extPriceDto,
        selectConditions,
        this.userPurchaseOrderLineRepository,
      );

      if (!updationResult)
        return {
          error_message: 'Something went wrong! unable to update database! ',
        };
      else updationResult = 'db updated!';
    } catch (error) {
      return {
        error_message: 'Something went wrong! unable to update database! ',
      };
    }

    return updationResult;
  }
  async checkBuyerCreditLimit(price,user_id){
    let response = null;
    const companyBuyNowPayLater = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository,{"user_id" : user_id});
    if(companyBuyNowPayLater && companyBuyNowPayLater.is_approved == true){
      const currentBuyerCreditAvailableLimit = Number(companyBuyNowPayLater.bryzos_available_credit_limit);
      const bryzosCreditLimit = Number(companyBuyNowPayLater.bryzos_credit_limit);
      if(currentBuyerCreditAvailableLimit > price){
        await this.updateBryzosCreditLimit(currentBuyerCreditAvailableLimit,price,bryzosCreditLimit,user_id);
        response = true;
      }else{
        response = {"error_message" : "The user's available credit limit is insufficient to cover the price."};
      }
    }else{
      response = {"error_message" : "The buyer's BNPL is not setup."};
    }
    return response;
  }

  async updateBryzosCreditLimit(availableCreditLimit,price,bryzosCreditLimit,buyer_id){
    const updateCBNPLData = {};
    const newAvailableCreditLimit = Number(availableCreditLimit) - Number(price);
    updateCBNPLData['bryzos_available_credit_limit'] = newAvailableCreditLimit
    updateCBNPLData['bryzos_outstanding_credit_limit'] =  Number(bryzosCreditLimit) - newAvailableCreditLimit;
    await this.dbServiceObj.updateByMultipleWhere(updateCBNPLData, {"user_id":buyer_id}, this.companyBuyNowPayLaterRepository);
  }

  async logBryzosCreditLimit(buyerId,adminId,event, poNumber = null){
    const companyBuyNowPayLater = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository,{"user_id" : buyerId});
    if(companyBuyNowPayLater){
      let logData = {};
      logData["admin_id"]=adminId
      logData["buyer_id"]=buyerId
      logData["credit_limit"]=parseFloat(companyBuyNowPayLater.bryzos_credit_limit)
      logData["available_credit_limit"]=parseFloat(companyBuyNowPayLater.bryzos_available_credit_limit)
      logData["outstanding_credit_limit"]=parseFloat(companyBuyNowPayLater.bryzos_outstanding_credit_limit)
      logData["reason"]=event
      logData["po_number"]= poNumber

      await this.dbServiceObj.saveData(logData,this.logBryzosCreditLimitRepository);
    }
  }

  async restartWebsocketServer()
  {
    let response = "server started!";
    const config = {
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_RESTART_APP_SERVER_CREDENTIAL_KEY,
        secretAccessKey: process.env.AWS_RESTART_APP_SERVER_CREDENTIAL_SECRET,
      },
    };

    const client = new ElasticBeanstalkClient(config);
    const input = { // RestartAppServerMessage
      EnvironmentId: process.env.WEBSOCKET_ENVIRONMENT_ID,
      EnvironmentName: process.env.WEBSOCKET_ENVIRONMENT_NAME,
    };
    try {
      const command = new RestartAppServerCommand(input);
      const restartAppResponse = await client.send(command);
      console.log("Restarting websocket app server: ", restartAppResponse);
    } catch (error) {
      console.error("Error restarting websocket app server:", error);
      response = "Something went wrong, please contact Dev team";
    }
    return response;
  }

  async restartNodeMessageCronServer()
  {
    let response = "server started!";

     const config = {
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_RESTART_APP_SERVER_CREDENTIAL_KEY,
        secretAccessKey: process.env.AWS_RESTART_APP_SERVER_CREDENTIAL_SECRET,
      },
    };
    const client = new ElasticBeanstalkClient(config);
    const input = { // RestartAppServerMessage
      EnvironmentId: process.env.MESSAGE_NODE_ENVIRONMENT_ID,
      EnvironmentName: process.env.MESSAGE_NODE_ENVIRONMENT_NAME,
    };
    try {
      const command = new RestartAppServerCommand(input);
      const restartAppResponse = await client.send(command);
      console.log("Restarting message cron node app server: ", restartAppResponse);
    } catch (error) {
      console.error("Error restarting message cron node app server:", error);
      response = "Something went wrong, please contact Dev team";
    }
    
    return response;
  }

  async callNotificationService(payload: NotificationEventDto, adminId, accessToken) {
 
    const apiUrl = process.env.NOTIFICATION_SERVER_URL + "/sendAdminDashboardNotification";
    if (payload.notification_event === Constants.CUSTOM_NOTIFICATION) {
      const message = payload.message;
      const sendTo = payload?.send_to;
      const sendToUsers = payload?.send_to_users;

      const isMessageValid = message ? message.trim().length > 0 : false;

      if (!isMessageValid) {
        return { [responseErrorTag]: "Input value is invalid, please set proper values." };
      }
      if (sendTo === Constants.CUSTOM_NOTIFICATION_SEND_TO[3] && sendToUsers?.length < 1) {
        return { [responseErrorTag]: "Invalid user selections for send to option." };
      }
    }

    const securityData = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository,"name",libConstants.SECURITY_SECRET_KEY);
    let securityHashKey : string;
    if(securityData){
      securityHashKey = createHash('sha256').update(securityData.value).digest('hex');
    }

    const headers = { 
      accesstoken: accessToken,
      origin: process.env.AD_ORIGIN,
      referer: process.env.AD_REFERER,
      security: securityHashKey 
    };
    let response = null;

    try {
      response = (await this.httpService.axiosRef.post(apiUrl, { "data": payload }, { headers, params: { adminId } })).data;
    }
    catch (error) {
      console.log("Unable to call notification service");
      return { [responseErrorTag]: "Unable to call notification service" }
    }

    if (!response || response?.data?.error_message)
      return { [responseErrorTag]: "Unable to call notification service" };

    let adminLog = {
      'admin_id': adminId,
      'notification_event': payload.notification_event,
      'message': payload.message ?? null,
      'send_to': payload.send_to ?? null,
      'expiry_date' : payload.expiry_date ?? null
    }
    await this.dbServiceObj.saveData(adminLog, this.adminLogNotificationRepository);

    return response?.data;
  }

  async getDesktopNotificationEvents() {

    let reference_notification_events = [];

    let data = await this.dbServiceObj.findMany(this.referenceDataDesktopNotificationRepository,'show_on_admin_dashboard', 'true');

    for (let obj of data) {
      let respData = {};
      respData["id"] = obj.id;
      respData["notification_event"] = obj.notification_event;
      respData["notiifcation_name"] = obj.notification_title;
      respData["notification_type"] = obj.notification_type;
      respData["notification_params"] = obj.admin_dashboard_notification_params;
      respData["is_active"] = obj.is_active;
      respData["created_date"] = obj.created_date;
      respData["time_stamp"] = obj.time_stamp;

      reference_notification_events.push(respData);
    }
    let response = { "reference_notification_events": reference_notification_events };

    return response;
  }

  async getAllPo(){
    const getOrderCompleteStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,"value",Constants.ORDER_COMPLETED);
    const getOrderActiveStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,"value",Constants.ACTIVE);
    const getOrderResolvedStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,"value",Constants.RESOLVED);
        
        const order_status = [getOrderCompleteStatus.id,getOrderActiveStatus.id,getOrderResolvedStatus.id];

        const selectedFields = ['table2.buyer_po_number as buyer_po_number','table1.buyer_internal_po as buyer_internal_po','SUM(table2.sales_tax) as sales_tax', 'payment_method'];

        let conditions = {};

        const condition1_attributes = {"operator":"IN","value":order_status,"table":"user_purchase_order_line"};
        conditions["order_status_id"] = condition1_attributes;

        const combineAndConditions = {conditions};

        const leftJoin = { "user_purchase_order_line" : "purchase_order_id" };

        const orderBy = {"table1.created_date":"DESC"};

        const getAllPos = await this.dbServiceObj.FindByMultipleWhereComparisonANDLeftJoinById(this.userPurchaseOrderRepository,leftJoin,combineAndConditions,"buyer_po_number",selectedFields,orderBy);

        return getAllPos;
  }


  async getPoOrderList(options: { page: number, limit: number, search: string }){
    
    const paginationApplied = options.page > 0 && options.limit > 0;
    let meta;

    const activeStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,'value',Constants.ACTIVE);
    const resolvedStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,'value',Constants.RESOLVED);
    const completedStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,'value',Constants.ORDER_STATUS_COMPLETED);
    const canceledStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,'value',Constants.ORDERCANCELLED);

    const canceledStatusId = canceledStatus.id;

    let orderStatus = [activeStatusId.id, completedStatus.id, resolvedStatusId.id, canceledStatus.id];

    const selectedFields = [
      'table1.id AS purchase_order_id',
      'table1.buyer_id AS buyer_id',
      'table1.seller_id AS seller_id',
      'table1.buyer_po_number AS buyer_po_number',
      'table1.payment_method AS payment_method',
      'table1.claimed_by AS claimed_by',
      'table1.buyer_email AS buyer_email',
      'table1.is_closed_buyer AS is_closed_buyer',
      'table1.is_closed_seller AS is_closed_seller',
      'CASE WHEN table1.is_closed_buyer = 0 AND table1.is_closed_seller = 0 THEN true ELSE false END AS show_add_edit_btn',
      'DATE_FORMAT(CONVERT_TZ(table1.last_invoice_sent, "UTC", "America/Chicago"), "%c/%e/%y %h:%i %p") as last_invoice_sent',
      'CASE WHEN actual_buyer_po_price IS NOT NULL THEN actual_buyer_po_price ELSE buyer_po_price END AS buyer_po_price',
      'CASE WHEN actual_seller_po_price IS NOT NULL THEN actual_seller_po_price ELSE seller_po_price END AS seller_po_price',
      'CASE WHEN table1.actual_total_weight IS NOT NULL THEN table1.actual_total_weight ELSE table1.total_weight END AS total_weight',
      'table1.sales_tax as sales_tax',
      'table1.deposit_amount as deposit_amount',
      'table1.buyer_internal_po as buyer_internal_po',
      'table1.line1 as line1',
      'table1.city as city',
      'table1.state_id as state_id',
      'table1.zip as zip',
      'CASE WHEN user_buying_preference.send_invoices_to IS NULL THEN user_buying_preference.email_id ELSE user_buying_preference.send_invoices_to END AS send_invoice_to'
    ];

    const mapperFields = { 'selectFields' : selectedFields };
    
    const leftJoin = [];
    leftJoin.push( { table: 'user_buying_preference', joinColumn: 'user_id', mainTableColumn: 'buyer_id'})

    const conditions = [];
    const condition1 = {column: "is_active", operator:"=", value: true};
    conditions.push(condition1);
    const condition2 = {column: "is_active", operator:"=", value: true, table: "user_buying_preference"};
    conditions.push(condition2);
    const condition3 = { column: ['buyer_internal_po', 'buyer_po_number', 'buyer_email', 'claimed_by', 'last_invoice_sent', 'buyer_po_price', 'actual_buyer_po_price', 'seller_po_price', 'actual_seller_po_price', 'total_weight', 'actual_total_weight', 'sales_tax'], operator: 'LIKE', value: options.search };
    conditions.push(condition3);

    const orderBy = {"table1.created_date":"DESC"};
    let purchaseOrders = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository,leftJoin,conditions,mapperFields,orderBy,null,options);

    if (paginationApplied) {
      if (Array.isArray(purchaseOrders.items) && purchaseOrders.items.length < 1) {
        return {[responseErrorTag]: 'No data found'};
      } else {
        meta = purchaseOrders.meta;
        purchaseOrders = purchaseOrders.items;
      }
    } else {
      if (Array.isArray(purchaseOrders) && purchaseOrders.length < 1) {
        return {[responseErrorTag]: 'No data found'};
      } else {
        purchaseOrders = purchaseOrders;
      }
    }
    
    const dataArray = [];
    const poList = [];
    for(const purchaseOrder of purchaseOrders){
      const poObject = {};
      const shipping_details = {
        line1: purchaseOrder.line1,
        city: purchaseOrder.city,
        zip: purchaseOrder.zip,
        state_id: Number(purchaseOrder.state_id)
      };
      poObject["id"] = purchaseOrder.purchase_order_id;
      poObject["buyer_po_number"] = purchaseOrder.buyer_po_number;
      poObject["buyer_internal_po"] = purchaseOrder.buyer_internal_po;
      poObject["payment_method"] = purchaseOrder.payment_method;
      poObject["buyer_id"] = purchaseOrder.buyer_id;
      poObject["buyer_email"] = purchaseOrder.buyer_email;
      poObject["seller_id"] = purchaseOrder.seller_id || null;
      poObject["claimed_by"] = purchaseOrder.claimed_by;
      poObject["buyer_po_price"] = purchaseOrder.buyer_po_price;
      poObject["sales_tax"] = purchaseOrder.sales_tax || 0;
      poObject["total_weight"] = purchaseOrder.total_weight;
      poObject["deposit_amount"] = purchaseOrder.deposit_amount || 0;
      poObject["seller_po_price"] = purchaseOrder.seller_po_price;
      poObject["last_invoice_sent"] = purchaseOrder.last_invoice_sent;
      poObject["is_closed_seller"] = purchaseOrder.is_closed_seller;
      poObject["is_closed_buyer"] = purchaseOrder.is_closed_buyer;      
      poObject["show_add_edit_button"] = (purchaseOrder.show_add_edit_btn === 1 || purchaseOrder.show_add_edit_btn === '1' || purchaseOrder.show_add_edit_btn === true || purchaseOrder.show_add_edit_btn === 'true') ? 1 : 0;
      poObject["send_invoice_to"] = purchaseOrder.send_invoice_to;
      poObject["shipping_details"] = shipping_details;
      poObject["purchase_order_lines"] = [];
      poList.push(purchaseOrder.buyer_po_number);
      dataArray.push(poObject);
    }
    
    const lineConditions = [];
    lineConditions.push({ columnName: 'buyer_po_number', operator: 'IN', value: poList });
    lineConditions.push({ columnName: 'order_status_id', operator: 'IN', value: orderStatus });

    const lineOrderBy = {"po_line":"ASC"};
    const poLinesByPoNumber = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.userPurchaseOrderLineRepository,lineConditions,undefined,lineOrderBy);

    for(const poLine of poLinesByPoNumber){
      dataArray.forEach(obj => {
        if (obj.buyer_po_number === poLine.buyer_po_number) {
          let lineDescription = {};
          lineDescription["id"]=poLine.id;
          lineDescription["po_line"]=poLine.po_line;
          lineDescription["description"]=poLine.description;
          lineDescription["product_id"]=poLine.product_id;
          lineDescription["reference_product_id"]=poLine.reference_product_id;
          lineDescription["price_unit"]=poLine.price_unit;
          lineDescription["qty_unit"]=poLine.qty_unit;
          lineDescription["qty"]=poLine.actual_qty;
          lineDescription["buyer_extended"]=poLine.actual_buyer_line_total;
          lineDescription["sales_tax"]=poLine.sales_tax || 0;
          lineDescription["total_weight"]=poLine.actual_total_weight;
          lineDescription["seller_extended"]=poLine.actual_seller_line_total;
          lineDescription["domestic_material_only"]=poLine.domestic_material_only;
          lineDescription["order_status_id"]=poLine.order_status_id;
          lineDescription["is_buyer_order_open"]=poLine.is_buyer_order_open;
          lineDescription["is_seller_order_open"]=Number(poLine.order_status_id) === Number(completedStatus.id) ? 0 : 1;
          lineDescription["is_canceled"] = Number(poLine.order_status_id) === Number(canceledStatusId) ? true : false;
          lineDescription["buyer_price"]=poLine.buyer_price_per_unit;
          lineDescription["seller_price"]=poLine.seller_price_per_unit;
          lineDescription["buyer_calculation_price_per_unit"]=poLine.buyer_calculation_price_per_unit;
          lineDescription["seller_calculation_price_per_unit"]=poLine.seller_calculation_price_per_unit;
          lineDescription["shape"]=poLine.shape;
          lineDescription["buyer_pricing_lb"]=poLine.buyer_pricing_lb;

          obj.purchase_order_lines.push(lineDescription);
        }
      });
    }
    return { items: dataArray, meta : meta};
  }

  async sendEmailInvoice(payload: PurchaseOrderNumberDto, adminId: string) {

    let poNumber = payload.po_number.replace(/[a-zA-Z]/, libConstants.BUYER_PURCHASE_ORDER_INITIAL);
    let poData = await this.dbServiceObj.findOne(this.userPurchaseOrderRepository, 'buyer_po_number', poNumber);
    let source = payload?.source ?? null;

    if (!poData) {
      return { "error_message": "Unable to found provided po details!" }
    }

    let refSettingsInvoiceToAchOrder = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository, 'name', Constants.INVOICE_FOR_ACH_ORDER);

    if (!refSettingsInvoiceToAchOrder || refSettingsInvoiceToAchOrder.value != Constants.INVOICE_FOR_ACH_ORDER_YES) {
      return { "error_message": "Sorry,You cannot send invoice email to Cash In Advance order!" }
    }
    const response = await this.recalculateSalesTax(poData, adminId);

    if(response && response.hasOwnProperty('error_message')){
      return {error_message : response.error_message};
    }

    await this.awsQueue.sendPoToGeneratePdfData(poNumber, Constants.ADMIN_DASHBOARD_BUYER_INVOICE);
    await this.saveAdminLogInvoiceEmail(poNumber, adminId, source);

    //update last_invoice_sent date in table
    let currentDate = new Date();
    const utcDate = utcToZonedTime(currentDate, 'UTC');
    let invoiceSentDate = format(utcDate, 'yyyy-MM-dd HH:mm:ss');

    let updateDto = {
      "last_invoice_sent":invoiceSentDate
    }

    await this.dbServiceObj.updateByColumnId(this.userPurchaseOrderRepository,updateDto,'buyer_po_number',poNumber);

    return "Email sent!";
  }

  async saveAdminLogInvoiceEmail(poNumber: string, adminId: string, source :string) {
    await this.dbServiceObj.updateByColumnId(this.adminLogInvoiceEmailRepository, { 'is_active': false, 'is_email_sent': true }, 'po_number', poNumber);
    let saveDataDto = {
      "po_number": poNumber,
      "admin_id": adminId
    }
    if(source){ saveDataDto['source'] = source };
    await this.dbServiceObj.saveData(saveDataDto, this.adminLogInvoiceEmailRepository);
  }

  async getAllOpenPo(){
   
    const getOrderActiveStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,"value",Constants.ACTIVE);
    const getOrderResolvedStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,"value",Constants.RESOLVED);
    const getOrderCompleteStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,"value",Constants.COMPLETED);
        
        const order_status = [getOrderActiveStatus.id,getOrderResolvedStatus.id,getOrderCompleteStatus.id];

        const selectedFields = ['table2.buyer_po_number as buyer_po_number','table1.buyer_internal_po as buyer_internal_po','SUM(table2.sales_tax) as sales_tax', 'payment_method'];

        let conditions = {};

        const condition1_attributes = {"operator":"IN","value":order_status,"table":"user_purchase_order_line"};
        conditions["order_status_id"] = condition1_attributes;

        const condition2_attributes = {"operator":"=","value":false};
        conditions["is_closed_buyer"] = condition2_attributes;

        const condition3_attributes = {"operator":"=","value":true,"table":"user_purchase_order_line"};
        conditions["is_buyer_order_open"] = condition3_attributes;

        const combineAndConditions = {conditions};

        const leftJoin = { "user_purchase_order_line" : "purchase_order_id" };

        const orderBy = {"table1.created_date":"DESC"};

        const getAllPos = await this.dbServiceObj.FindByMultipleWhereComparisonANDLeftJoinById(this.userPurchaseOrderRepository,leftJoin,combineAndConditions,"buyer_po_number",selectedFields,orderBy);

        return getAllPos;
  }


  async updateinDB(poNumber: string, authAmount: number, totalPrice: number, chargeDate: string, transactionId: string | null, buyerId: string, companyBnplData: any, checkoutViaBalance: string, sellerFundingDate: string) {
    try {
      let paymentMethodDto = { payment_method: 'bryzos_pay' };
      let paymentMethodUpdatedResult = await this.dbServiceObj.updateByColumnId(this.userPurchaseOrderRepository, paymentMethodDto, 'buyer_po_number', poNumber);
      if (!paymentMethodUpdatedResult) {
        return { [responseErrorTag]: 'updation in user-purchase-order table failed' };
      }

      let lineUpdateDto = { auth_amount: authAmount, charge_date: chargeDate, payment_token: transactionId, seller_funding_date: sellerFundingDate };
      let userLineUpdatedResult = await this.dbServiceObj.updateByColumnId(this.userPurchaseOrderLineRepository, lineUpdateDto, 'buyer_po_number', poNumber);
      if (!userLineUpdatedResult) {
        return { [responseErrorTag]: 'updation in user-purchase-order-line table failed!' };
      }

      let _amount = 0;
      if (checkoutViaBalance === Constants.ON) {
        _amount = authAmount;
      } else {
        _amount = totalPrice;
      }

      if (checkoutViaBalance === Constants.OFF) {
        const creditLimit = Number(companyBnplData.bryzos_credit_limit);
        const availableCreditLimit = Number(companyBnplData.bryzos_available_credit_limit) - _amount;
        const outstandingCreditLimit = Number(companyBnplData.bryzos_outstanding_credit_limit) + _amount;

        const companyBuyNowPayLaterDto = { bryzos_available_credit_limit: availableCreditLimit, bryzos_outstanding_credit_limit: outstandingCreditLimit };
        const companyBuyNowPayLaterUpdateResult = await this.dbServiceObj.updateByMultipleWhere(companyBuyNowPayLaterDto, { user_id: buyerId, is_active: true, is_approved: true }, this.companyBuyNowPayLaterRepository);
        if (!companyBuyNowPayLaterUpdateResult) {
          return { [responseErrorTag]: 'updation in company-buy-now-pay-later table failed' };
        }

        const logBryzosCreditLimitDto = { "id": uuidv4(), "buyer_id": buyerId, "credit_limit": creditLimit, "available_credit_limit": availableCreditLimit, "outstanding_credit_limit": outstandingCreditLimit, "reason": "CHECKOUT", po_number: poNumber };
        this.dbServiceObj.saveData(logBryzosCreditLimitDto, this.logBryzosCreditLimitRepository);
      }
    } catch (error) {
      return { [responseErrorTag]: error.message };
    }

    return 'Database updated!';
  }

  cleanStatementDescriptor(descriptor: string) {
    return descriptor?.replace('<', ' ')?.replace('\\', ' ')?.replace("'", ' ')?.replace('"', ' ')?.replace('*', ' ');
  }

  async getAdminReferenceData(){
    const onboardSource = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.referenceDataUserOnboardedAppVersionRepository,{},{ selectFields: ["*", "buyer_spread_percentage AS disc_discount_percentage", "base_pricing_column AS disc_discount_pricing_column", "deprecated_disc_discount_period AS disc_discount_period", "deprecated_disc_discount_phaseout_period AS disc_discount_phaseout_period", "deprecated_disc_discount_phaseout_startdate AS disc_discount_phaseout_startdate"] },"created_date","DESC");
 
    const pricingColumns = [Constants.BUYER_PRICING,Constants.NEUTRAL_PRICING,Constants.SELLER_PRICING];
    const spreadDefaultValues = await this.dbServiceObj.findAll(this.referenceDataSpreadDefaultValuesRepository);
    const emailsExtraRecipients = await this.getExtraRecipients();
    const externalApiEndpoints = await this.dbServiceObj.findAll(this.refExternalApiEndpointRepository, { "created_date": "ASC" });
    const refBomConfidence = await this.dbServiceObj.findAllWithCustomSelectFields(this.referenceDataBomProductConfidenceRepository,"*",{min_match_count: "ASC"},true);
    const groupedEndpoints = {};

    // Iterate over each endpoint
    for (const item of externalApiEndpoints) {
      if (!item.type) continue; // Skip null types

      // If type doesn't exist in the object, initialize it as an empty array
      if (!groupedEndpoints[item.type]) {
        groupedEndpoints[item.type] = [];
      }

      // Push the name into the corresponding type array
      groupedEndpoints[item.type].push({"endpoint":item.endpoint, "value": item.name});
    }
    
    const response = {
      'onboard_source': onboardSource,
      'pricing_columns':pricingColumns ,
      'discount_default_values':spreadDefaultValues,
      'email_extra_recipients':emailsExtraRecipients,
      'external_api_endpoint': groupedEndpoints,
      'bom_product_confidence': refBomConfidence
    };
    return response;
  }

  async getExtraRecipients(){
    let events = {};
    const eventsAndRecipients = await this.dbServiceObj.findAll(this.referenceDataRecipientEmailSettingsRepository);
    if(!eventsAndRecipients) return events;
    for(const eventData of eventsAndRecipients){
      if(events.hasOwnProperty(eventData.email_event)){
        if(eventData.is_cc) events[eventData.email_event]["cc_email"] =  events[eventData.email_event]["cc_email"]+";"+eventData.email_id+";";
        if(eventData.is_bcc) events[eventData.email_event]["bcc_email"] =  events[eventData.email_event]["bcc_email"]+";"+eventData.email_id+";";

        events[eventData.email_event]["cc_email"] = events[eventData.email_event]["cc_email"].replace(/^;+|;+$/g, '')
        events[eventData.email_event]["bcc_email"] = events[eventData.email_event]["bcc_email"].replace(/^;+|;+$/g, '')

      }else{
        events[eventData.email_event] = {};
        events[eventData.email_event]["cc_email"] = eventData.is_cc ? eventData.email_id: "";
        events[eventData.email_event]["bcc_email"] = eventData.is_bcc ? eventData.email_id: "";
      }
    }
    return events;
  }

  async getSafeUploads() {
    let response = null;

    const conditions = [{ column: 'is_active', operator: '=', value: true }];
    const orderBy = { 'table1.created_date': 'DESC' };

    response = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.homepageSafeUploadsRepository, null, conditions, null, orderBy, null);

    if (response === null || response === undefined) {
      return { [responseErrorTag]: 'Something went wrong!' };
    } else if (Array.isArray(response) && response.length < 1) {
      return { [responseErrorTag]: 'No data found' };
    }
    return response;
  }

  async saveHideCarouselImage(payload: HideHomePageCarouselDto[]) {
    let response = null;

    const updateArray = [];

    payload.forEach(element => {
      updateArray.push({
        id: element.id,
        show_on_carousel: element.value,
        user_name: element.user_name,
        caption: element.caption || null,
        company_name: element.company_name || null,
      });
    });

    if (updateArray.length) {
      const updateDto = updateArray.map(item => ({
        id: item.id,
        show_on_carousel: item.show_on_carousel,
        user_name: item.user_name,
        caption: item.caption || null,
        company_name: item.company_name || null,
      }));
   
      try {
        const affectedRows = await this.dbServiceObj.saveData(updateDto , this.homepageSafeUploadsRepository)
      } catch (error) {
        console.error("Error updating records:", error);
        response = { error_message: "Something went wrong" };
      }
    }

    response = "Successfully updated";
    return response;
  }

  async getSafeImgixImageKit() {
    let response = null;

    response = await this.dbServiceObj.findAll(this.homepageSafeConfigRepository);
    if (response === null || response === undefined) {
      return { [responseErrorTag]: 'Something went wrong!' };
    } else if (Array.isArray(response) && response.length < 1) {
      return { [responseErrorTag]: 'No data found' };
    }

    return response;
  }

  async saveSafeImgixImageKit(payload: saveSafeImgixImageKit[]) {
    let response = null;

    if (payload.length >= 1) {
      const updateDto = payload.map(item => ({ id: item.id, config_value: item.value }));
      response = await this.dbServiceObj.saveData(updateDto, this.homepageSafeConfigRepository);
      if (response === null || response === undefined) {
        return { [responseErrorTag]: 'Something went wrong!' };
      }
    }

    response = "Successfully updated";
    return response;
  }

  async getBuyerIncreaseCreditRequest(params: { page: number, limit: number, search: string }) {
    let response = null;

    const checkoutViaBalance = (await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSettingsRepository, { name: Constants.CHECKOUT_VIA_BALANCE, is_active: true },))?.value;

    response = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(
      this.companyBuyNowPayLaterRepository,
      [
        { table: 'user_request_increase_credits', joinColumn: 'id', mainTableColumn: 'user_request_increase_credit_id',},
        { table: 'user_buying_preference', joinColumn: 'user_id', mainTableColumn: 'user_id', },
      ],
      [
        { column: 'is_active', operator: '=', value: true, table: 'user_buying_preference', },
        { column: 'is_approved', operator: '=', value: true, },
        { column: 'is_active', operator: '=', value: true, },
        { column: 'is_active', operator: '=', value: true, table: 'user_request_increase_credits', },
        { column: 'user_request_increase_credit_id', operator: 'NOT NULL', },
      ],
      {
        selectFields: [
          'table1.user_id AS user_id',
          'user_buying_preference.company_name AS company_name',
          'table1.duns AS duns',
          'user_buying_preference.company_address_line1 AS company_address_line1',
          'user_buying_preference.company_address_line2 AS company_address_line2',
          'user_buying_preference.company_address_city AS company_address_city',
          'user_buying_preference.company_address_state_id AS company_address_state_id',
          'user_buying_preference.company_address_zip AS company_address_zip',
          'user_buying_preference.first_name AS first_name',
          'user_buying_preference.last_name AS last_name',
          'user_buying_preference.client_company AS client_company',
          'table1.requested_increase_credit AS requested_increase_credit',
          'table1.is_requested_increase_credit_approved AS is_requested_increase_credit_approved',
          'table1.credit_status AS credit_status',
          'table1.user_request_increase_credit_id AS user_request_increase_credit_id',
          'user_request_increase_credits.status AS status',
          (checkoutViaBalance === 'ON' ? 'table1.balance_available_credit_limit' : 'table1.bryzos_available_credit_limit') + ' AS bryzos_available_credit_limit',
          (checkoutViaBalance === 'ON' ? 'table1.balance_credit_limit' : 'table1.bryzos_credit_limit') + ' AS bryzos_credit_limit',
        ],
      },
      { 'user_request_increase_credits.created_date': 'DESC', 'table1.id': 'DESC' },
      null,
      params
    );

    if (+params.limit > 0 && +params.page > 0) {
      if (response.items < 1) {
        return { [responseErrorTag]: 'No data found' };
      }
    } else {
      if (response === null) {
        return { [responseErrorTag]: 'Something went wrong!' };
      } else if (Array.isArray(response) && response.length < 1) {
        return { [responseErrorTag]: 'No data found' };
      }
    }
    return response;
  }

  async getResaleCertificate(params: { page: number, limit: number, search: string }) {
    let resales = null;
    resales = await this.resaleCertificateService.getResaleCertificate(params);

    if (+params.limit > 0 && +params.page > 0) {
      if (resales.items < 1) {
        return { [responseErrorTag]: 'No data found' };
      }
    } else {
      if (resales === null) {
        return { [responseErrorTag]: 'Something went wrong!' };
      } else if (Array.isArray(resales) && resales.length < 1) {
        return { [responseErrorTag]: 'No data found' };
      }
    }
    return resales;
  }

  async getAchCreditOrder(params: { page: number, limit: number, search: string }) {
    let response = null;


    const resolvedStatus = (await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository, "value", Constants.ORDER_STATUS_RESOLVED))?.id;
    const activeStatusId = (await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository, "value", Constants.ORDER_STATUS_ACTIVE))?.id;

    response = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository,
      [
        { table: 'user_purchase_order_line', joinColumn: 'purchase_order_id', mainTableColumn: 'id', }
      ],
      [
        { column: 'payment_method', operator: '=', value: Constants.PAYMENT_METHOD_ACH_CREDIT, },
        { column: 'order_status_id', operator: 'IN', value: [activeStatusId, resolvedStatus], table: 'user_purchase_order_line' },
      ],
      {
        selectFields: [
          'table1.buyer_po_number AS buyer_po_number',
          'table1.buyer_id AS buyer_id',
          'CASE WHEN table1.actual_buyer_po_price IS NOT NULL THEN table1.actual_buyer_po_price ELSE table1.buyer_po_price  END AS buyer_po_price',
          'table1.sales_tax AS sales_tax',
          'table1.delivery_date AS delivery_date',
          'table1.buyer_internal_po AS buyer_internal_po',
          'table1.freight_term AS freight_term',
          'table1.is_active AS is_active',
          'table1.claimed_by AS claimed_by',
          'table1.is_ach_po_approved AS is_ach_po_approved',
          'table1.buyer_company_name AS buyer_company_name',
        ],
      },
      { 'table1.created_date': 'DESC' },
      "table1.buyer_po_number",
      params,
    );

    if (+params.limit > 0 && +params.page > 0) {
      if (response.items < 1) {
        return { [responseErrorTag]: 'No data found' };
      }
    } else {
      if (response === null) {
        return { [responseErrorTag]: 'Something went wrong!' };
      } else if (Array.isArray(response) && response.length < 1) {
        return { [responseErrorTag]: 'No data found' };
      }
    }
    return response;
  }

  async getBuyNowPayLater(options: { page: number, limit: number, search: string }) {
    let response = null;

    const checkoutViaBalance = (await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSettingsRepository, { name: Constants.CHECKOUT_VIA_BALANCE, is_active: true }))?.value;
    if (checkoutViaBalance === null || checkoutViaBalance === undefined) {
      return { [responseErrorTag]: 'Something went wrong!' };
    }

    response = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(
      this.companyBuyNowPayLaterRepository,
      [{ table: 'user_buying_preference', joinColumn: 'user_id', mainTableColumn: 'user_id' }],
      [{ column: 'is_active', operator: '=', value: true, table: 'user_buying_preference' }, { column: 'is_active', operator: '=', value: true }],
      {
        selectFields: [
          'table1.auth_amount_percentage AS auth_amount_percentage',
          'table1.charges_date AS charges_date',
          'table1.duns AS duns',
          'table1.is_approved AS is_approved',
          'table1.net_terms_days AS net_terms_days',
          'table1.requested_credit_limit AS requested_credit_limit',
          'table1.user_id AS user_id',
          (checkoutViaBalance === 'ON' ? 'table1.balance_available_credit_limit' : 'table1.bryzos_available_credit_limit') + ' AS bryzos_available_credit_limit',
          (checkoutViaBalance === 'ON' ? 'table1.balance_credit_limit' : 'table1.bryzos_credit_limit') + ' AS bryzos_credit_limit',
          'user_buying_preference.company_name AS company_name',
          'user_buying_preference.company_address_line1 AS company_address_line1',
          'user_buying_preference.company_address_line2 AS company_address_line2',
          'user_buying_preference.company_address_city AS company_address_city',
          'user_buying_preference.company_address_state_id AS company_address_state_id',
          'user_buying_preference.company_address_zip AS company_address_zip',
          'user_buying_preference.first_name AS first_name',
          'user_buying_preference.last_name AS last_name',
          'user_buying_preference.client_company AS client_company',
        ],
      },
      { 'table1.created_date': 'DESC' },
      null,
      options,
    );

    if (response === null || response === undefined) {
      return { [responseErrorTag]: 'Something went wrong!' };
    } else if (Array.isArray(response) && response.length < 1) {
      return { [responseErrorTag]: 'No data found!' };
    }

    return response;
  }
  
  async getWidgetMenuIndicator() {
    const bnplIndicator = await this.bnplIndicator();
    const increaseBnplCreditIndicator = await this.increaseBnplCreditIndicator();
    const reSaleCertificateIndicator = await this.reSaleCertificateIndicator();
    const getWidgetMenuIndicatiorObj: any = {
      BNPL: bnplIndicator,
      IncreaseBnplCredit: increaseBnplCreditIndicator,
      ReSaleCertificate: reSaleCertificateIndicator
    };
    return getWidgetMenuIndicatiorObj;
  }

  private async bnplIndicator() {
    const bnplIndicatorObj: any = {
      is_indicator: false,
      admin_email: null
    }
    const bnplApprovalLeftJoins = []
    const newBnplApprovalConditions = [
      {column: "is_active", operator:"=", value: true},
      {column: "is_approved", operator:"NULL"},
    ]
    const newBnplApprovalRequest = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.companyBuyNowPayLaterRepository, bnplApprovalLeftJoins, newBnplApprovalConditions)
    if(newBnplApprovalRequest.length > 0){
      bnplIndicatorObj.is_indicator = true
    }
    const getBnplAdminEmailColumn = {name: Constants.BRYZOS_PAY_ADMIN_EMAIL}
    const bnplAdminEmailData = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSettingsRepository, getBnplAdminEmailColumn )
    if(bnplAdminEmailData){
      bnplIndicatorObj.admin_email = bnplAdminEmailData.value
    }
    return bnplIndicatorObj;
  }

  private async increaseBnplCreditIndicator() {
    const increaseBnplCreditIndicatorObj: any = { is_indicator: false }
    const bnplApprovalLeftJoins = []
    const increaseBnplCreditApprovalConditions = [
      {column: "is_requested_increase_credit_approved", operator:"=", value: 0},
      {column: "credit_status", operator:"=", value: Constants.REQUESTED_CREDIT_PENDING_INCREASE},
      {column: "is_active", operator:"=", value: true},
    ]
    const newIncreaseBnplCreditApprovalData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.companyBuyNowPayLaterRepository, bnplApprovalLeftJoins, increaseBnplCreditApprovalConditions)
    if(newIncreaseBnplCreditApprovalData.length > 0){
      increaseBnplCreditIndicatorObj.is_indicator = true
    }
    return increaseBnplCreditIndicatorObj;
  }

  private async reSaleCertificateIndicator() {
    const reSaleCertificateIndicatorObj: any = { is_indicator: false }
    const resaleCertificateLeftJoins = [{table: "user", joinColumn: "id", mainTableColumn: "user_id"}]
    const resaleCertificateConditions = [
      {column: "status", operator:"=", value: Constants.PENDING},
      {column: "is_active", operator:"=", value: true},
      {column: "is_active", operator:"=", value: true, table: "user" },
    ]

    const newResaleCertificate = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userReasaleCertificateRepository, resaleCertificateLeftJoins, resaleCertificateConditions)
    if(newResaleCertificate.length > 0){
      reSaleCertificateIndicatorObj.is_indicator = true
    }
    return reSaleCertificateIndicatorObj;
  }


  async recalculateSalesTax(orderData, adminId){

    if(orderData){

      const buyerId = orderData.buyer_id;
      const sellerId = orderData.seller_id;
      const buyerPoNumber = orderData.buyer_po_number;
      const stateId = orderData.state_id;
      const zipCode = orderData.zip;
      const paymentMethod = orderData.payment_method;

      const orderStatusResolved = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,'value',Constants.RESOLVED);
      const orderStatusActive= await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,'value',Constants.ACTIVE);
      const orderStatusComplete= await this.dbServiceObj.findOne(this.userReferenceDataOrderStatusRepository,'value',Constants.COMPLETED);
    
      const orderStatusIds = [orderStatusActive.id, orderStatusResolved.id, orderStatusComplete.id];
    
      // Fetch ressale certificate
      const resaleCertData = await this.dbServiceObj.findOneByMultipleWhere(this.userReasaleCertificateRepository,{user_id:buyerId,state_id:stateId});

      // Calculate sales tax rate
      const salesTaxData = await this.baseLibraryService.calculateSalesTaxRate(stateId, zipCode, resaleCertData, this.userPurchaseOrderRepository);

      let salesTaxRate = null;
      if(salesTaxData){
        salesTaxRate = salesTaxData.sales_tax_rate;
      }
      // Fetch purchase order line data
      const purchaseOrderLineData = await this.dbServiceObj.findManyByWhereAndWhereIn(this.userPurchaseOrderLineRepository, { buyer_po_number: buyerPoNumber }, 'order_status_id', orderStatusIds);

      if (purchaseOrderLineData && purchaseOrderLineData.length > 0 && salesTaxRate != null){
        // Calculate sales tax for each purchase order line

        // const activePoLineSalesTaxData = purchaseOrderLineData.map(item => ({
        //   id: item.id,
        //   re_calculated_sales_tax: (item.actual_buyer_line_total && salesTaxRate) ? (salesTaxRate * item.actual_buyer_line_total).toFixed(2) : 0,
        //   old_sales_tax: item.sales_tax || 0,
        // }));

        const activePoLineSalesTaxData = purchaseOrderLineData
        .filter(item => item.is_buyer_order_open == true) // Filter only items where is_buyer_order_open is true
        .map(item => ({
          id: item.id,
          re_calculated_sales_tax: (item.actual_buyer_line_total && salesTaxRate) ? (salesTaxRate * item.actual_buyer_line_total).toFixed(2) : 0,
          old_sales_tax: item.sales_tax || 0,
        }));
    
        if(activePoLineSalesTaxData.length == 0){
           return false;
        }

        // Calculate total sales tax
        let recalculatedPoSalesTax = activePoLineSalesTaxData.reduce((total, item) => total + parseFloat(item.re_calculated_sales_tax), 0).toFixed(2);
        const oldPoSalesTax = activePoLineSalesTaxData.reduce((total, item) => total + parseFloat(item.old_sales_tax), 0).toFixed(2);

        const recalculatedSalesTaxData = {"totalPoTax" : recalculatedPoSalesTax, "purchaseOrderLines" : activePoLineSalesTaxData};
        
        const recalculatedPOLineTax = recalculatedSalesTaxData ? recalculatedSalesTaxData.purchaseOrderLines : [];

        const purchaseOrderLineIdsArr = recalculatedSalesTaxData.purchaseOrderLines.map(item => item.id);

        if(recalculatedPOLineTax && recalculatedPOLineTax.length > 0){

          const hasDifferentSalesTax = recalculatedPOLineTax.filter(pol =>
              Number(pol.old_sales_tax) !== Number(pol.re_calculated_sales_tax)
          ).length > 0;


          //At least one item has re-calculated sales tax different from old sales tax.
          if (hasDifferentSalesTax) {

            const poLineSalesTaxData = activePoLineSalesTaxData.map(item => ({
              id: item.id,
              sales_tax: item.re_calculated_sales_tax,
            }));

          
            const cbnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository,'user_id',buyerId);

            const selectFields = ["IFNULL(SUM(escrow),0) as total_seller_price","IFNULL(SUM(buyer_purchase),0) as total_buyer_price","IFNULL(SUM(sales_tax),0) as total_sales_tax", "purchase_order_line_id"];

            const conditions = { purchase_order_line_id: { operator: "IN", value: purchaseOrderLineIdsArr } };

            const orderLedgerData = await this.dbServiceObj.FindByMultipleWhereComparisonANDLeftJoinById(this.userPurchaseOrderLedgerRepository,[],{conditions},'purchase_order_line_id',selectFields);

            if(orderLedgerData){
              let insertOrderLedgerData = [];
              let insertCancelLog = [];

              orderLedgerData.forEach(item => {
                const insertOrderLedger: any = {};
                const insertLog: any = {};
    
                const previousSalesTax = item.total_sales_tax || 0;
                const purchaseOrderLineId = item.purchase_order_line_id;
    
                const updatedPoLineSalesTaxData = recalculatedSalesTaxData.purchaseOrderLines.find(item => item.id === purchaseOrderLineId);
                const recalculatedSalesTax = updatedPoLineSalesTaxData ? updatedPoLineSalesTaxData.re_calculated_sales_tax : 0;
                const ledgerSalesTaxDiff = Number(recalculatedSalesTax) - Number(previousSalesTax);


                insertOrderLedger.sales_tax = ledgerSalesTaxDiff;
                insertOrderLedger.transaction_type = Constants.TRANSACTION_TYPE_BA_RCDC;
                insertOrderLedger.purchase_order_line_id = purchaseOrderLineId;
                insertOrderLedger.user_id = buyerId;
          
                if (Number(recalculatedSalesTax) !== Number(previousSalesTax)) {
                  insertOrderLedgerData.push(insertOrderLedger);
                }
              });
    
              if(paymentMethod === Constants.PAYMENT_METHOD_BRYZOS_PAY){
                
                if(cbnplData)
                {
                  let bnplInsertLog = {};

                  let poSalesTaxDiff = Number(recalculatedPoSalesTax) - Number(oldPoSalesTax);
          
                  if(Number(cbnplData.bryzos_available_credit_limit) < Number(poSalesTaxDiff) && Number(oldPoSalesTax) < Number(recalculatedPoSalesTax)){ 
                    return { "error_message" : "Recalculate sales tax failed, insufficient buyer available credit limit!" };
                  }

                  let availableBryzosCreditLimit = Number(cbnplData.bryzos_available_credit_limit) - Number(poSalesTaxDiff);
                  let outsatndingBryzosCreditLimit = Number(cbnplData.bryzos_credit_limit) - Number(availableBryzosCreditLimit);
          
                  let cbnpl_update_set = {bryzos_available_credit_limit: availableBryzosCreditLimit, bryzos_outstanding_credit_limit: outsatndingBryzosCreditLimit};

                  await this.dbServiceObj.updateByMultipleWhere(cbnpl_update_set, { user_id: buyerId }, this.companyBuyNowPayLaterRepository);

                    
                  bnplInsertLog["available_credit_limit"] = availableBryzosCreditLimit;
                  bnplInsertLog["outstanding_credit_limit"] = outsatndingBryzosCreditLimit;
                  bnplInsertLog['po_number'] = buyerPoNumber;
                  bnplInsertLog["credit_limit"] = cbnplData.bryzos_credit_limit;
                  bnplInsertLog["buyer_id"] = buyerId;
                  bnplInsertLog["admin_id"] = adminId;
                  bnplInsertLog["reason"] = 'SEND_INVOICE_VIA_AD';

                  await this.dbServiceObj.saveData(bnplInsertLog,this.logBryzosCreditLimitRepository);
            
                }
                  
              }
        
              if(poLineSalesTaxData && poLineSalesTaxData.length>0){
                await this.dbServiceObj.saveData(poLineSalesTaxData,this.userPurchaseOrderLineRepository);
              }

              if(insertOrderLedgerData && insertOrderLedgerData.length > 0){
                await this.dbServiceObj.saveData(insertOrderLedgerData,this.userPurchaseOrderLedgerRepository);
              }


              if(recalculatedPoSalesTax){
                //get total updated sales tax
                const mapper = { selectFields: ["IFNULL(SUM(sales_tax),0) as total_sales_tax"] };

                const orderLineSalesTaxData = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.userPurchaseOrderLineRepository, { buyer_po_number: buyerPoNumber}, mapper, 'created_date', 'ASC');

                if (orderLineSalesTaxData?.length > 0) {
                  recalculatedPoSalesTax = orderLineSalesTaxData[0].total_sales_tax;

                  await this.dbServiceObj.updateByMultipleWhere({sales_tax: recalculatedPoSalesTax},{ buyer_po_number: buyerPoNumber },this.userPurchaseOrderRepository);
                }               
              }
          
              if(recalculatedPoSalesTax && Number(recalculatedPoSalesTax) == 0)
              {
                const taxExemptLog: any = {};

                if(salesTaxData){
                  if(salesTaxData.is_threshold_met_cert_approved){
                    taxExemptLog.resale_certificate_url = resaleCertData.cerificate_url_s3;
                    taxExemptLog.resale_certificate_id = resaleCertData.id;
                  }
                  taxExemptLog.po_number = buyerPoNumber;
                  taxExemptLog.event = 'AD_SEND_INVOICE';
                  taxExemptLog.annual_total_transaction_amount = salesTaxData.annual_transaction_amount;
                  taxExemptLog.annual_total_transaction = salesTaxData.annual_transaction;
                  taxExemptLog.combined_rate_number = salesTaxData.ref_combined_rate_number;
                  taxExemptLog.economic_nexus_threshold = salesTaxData.ref_economic_nexus_threshold;
                  taxExemptLog.economic_nexus_threshold_number_transactions = salesTaxData.ref_economic_nexus_threshold_number_transactions;
                }

                await this.dbServiceObj.saveData(taxExemptLog,this.taxExemptedPoRepository);    
              }

            }

          }

        }
      }
    }
  }

  async updateSecurityKey(payload:UpdateSecurityKeyDto){
    try{
      const securityKey = payload.security_key;
      const encryptionKey = await this.baseLibraryService.encryptString(securityKey);

      const udpateDto = { value : securityKey }
      const condtitions = { name : libConstants.SECURITY_SECRET_KEY}
      await this.dbServiceObj.updateByMultipleWhere(udpateDto,condtitions,this.referenceDataSettingsRepository);

      const url = 'updateSecurityKey';
      this.utils.sendDataToWebsocket({ security_key : encryptionKey },url);

      const secretHashKey :string = createHash('sha256').update(securityKey).digest('hex');
      await this.cacheManager.setCache(process.env.SECURITY_CACHE_KEY,secretHashKey,0);
      return 'Security key updated successfully!';

    }catch(err){
      return {error_message:"Something went wrong"}
    }

  }

  async approveRejectResalesCert(adminId: string, data: UpdateResalesCertificateDto) {
    const id = data.id;
    const approve = data.approve;

    const updateResult = await this.resaleCertificateService.updateWidgetResaleCertificate(adminId, id, approve);
    const errorMessage = updateResult?.[responseErrorTag];
    if (errorMessage) {
      return { [responseErrorTag]: errorMessage }
    }

    const response = await this.resaleCertificateService.getWidgetCertificateData(id);
    return response;
  }
  
  async getAchCreditPayementApprovalList() {
    let response = await this.userAchCreditService.getAchCreditList();

    if (!response) {
      return { [responseErrorTag]: 'Something went wrong!' };
    } else if (!response?.length) {
      return { [responseErrorTag]: 'No data found' };
    }

    return response;
  }

  async checkEmailValid(emailData: EmailValidDto) {
    let isEmailValid = await this.signupUtility.checkIfEmailExists(emailData.email,'WIDGET_AD_CREATE_USER');
    if (!isEmailValid) {
      return { [responseErrorTag]: 'Invalid email!' };
    }

    return isEmailValid;
  }

  async actionOnIncreaseCreditLimitRequest(increaseCreditLimitDataDto: IncreaseCreditLimitDataDto, adminId: string) {
    let response = '';
    const cbnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository, 'user_id', String(increaseCreditLimitDataDto.user_id));
    if (cbnplData && cbnplData.is_approved) {
      let cbnplDataUpdate = {};
      let update = {};
      if (increaseCreditLimitDataDto.is_approved === true) {
        cbnplDataUpdate['is_requested_increase_credit_approved'] = increaseCreditLimitDataDto.is_approved;
        cbnplDataUpdate['credit_status'] = LibConstants.REQUESTED_INCREASE_CREDIT_APPROVED;
        update['status'] = LibConstants.REQUESTED_INCREASE_CREDIT_APPROVED;
        update['action_performed_by'] = adminId;

        const requestedIncreaseCreditLimit = await this.dbServiceObj.findOne(this.userRequestIncreaseCreditsRepository, 'user_id', String(increaseCreditLimitDataDto.user_id))
        if (requestedIncreaseCreditLimit) {
          const requestedCredit = requestedIncreaseCreditLimit.requested_credit;
          let bryzosAvailableCreditLimit = requestedCredit;
          if (cbnplData.bryzos_available_credit_limit && cbnplData.bryzos_credit_limit) {
            bryzosAvailableCreditLimit = requestedCredit - (cbnplData.bryzos_credit_limit - cbnplData.bryzos_available_credit_limit);
          }
          const outstandAmount = requestedCredit - bryzosAvailableCreditLimit;
          cbnplDataUpdate['bryzos_credit_limit'] = requestedCredit;
          cbnplDataUpdate['bryzos_available_credit_limit'] = bryzosAvailableCreditLimit;
          cbnplDataUpdate['bryzos_outstanding_credit_limit'] = outstandAmount;
        }
        this.awsQueue.sendNotificationBryzosPayApprovalRequest(increaseCreditLimitDataDto.user_request_increase_credit_id);

      } else if (increaseCreditLimitDataDto.is_approved == false) {
        cbnplDataUpdate['is_requested_increase_credit_approved'] = increaseCreditLimitDataDto.is_approved;
        cbnplDataUpdate['credit_status'] = LibConstants.REQUESTED_INCREASE_CREDIT_APPROVED;
        update['status'] = LibConstants.REQUESTED_INCREASE_CREDIT_REJECTED;
        update['action_performed_by'] = adminId;
      }
      const id = await this.dbServiceObj.updateByMultipleWhere(cbnplDataUpdate, { 'user_id': increaseCreditLimitDataDto.user_id }, this.companyBuyNowPayLaterRepository)
      this.logBryzosCreditLimit(increaseCreditLimitDataDto.user_id, adminId, LibConstants.APPROVED_CREDIT_LIMIT_INCREASE_VIA_AD)
      this.dbServiceObj.updateByMultipleWhere(update, { 'id': increaseCreditLimitDataDto.user_request_increase_credit_id, 'user_id': increaseCreditLimitDataDto.user_id }, this.userRequestIncreaseCreditsRepository)

      this.awsQueue.sendWidgetIncreaseBnplCreditResponse(increaseCreditLimitDataDto.user_request_increase_credit_id)
      if (id)
        response = "Successfully updated";

    } else {
      response = "Action already taken!";
    }

    return response;
  }
  
  async getMobileReleaseUrls(){
    const urls = await this.dbServiceObj.findAll(this.referenceDataUiReleaseUrlsRepository);
    if(urls){ return urls };
    return { error_message : "No urls found" }
  }
  
  async setMobileReleaseUrl(payload:MobileReleaseUrlDto){
    const selectedUrl = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataUiReleaseUrlsRepository,{"is_selected":true})
    let updateDto = [];
    if(selectedUrl){
      if(selectedUrl.id === payload.id){ return "The selected URL has already been set to release." }  
      updateDto.push({ id : selectedUrl.id, is_selected : false });
    }
    updateDto.push( { id : payload.id, is_selected : true } );
    await this.dbServiceObj.saveData(updateDto,this.referenceDataUiReleaseUrlsRepository);
    return "URL updated successfully";
  }

  async updateTncVersion(accessToken: string) {
    let lastBuyerTnc = null;
    let lastSellerTnc = null;

    let currentBuyerTncVersion = "v1";
    let currentSellerTncVersion = "v1";

    const termsConditions = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.referenceDataBryzosTermsConditionsRepository, [], []);
    const termsConditionsLen = termsConditions?.length;
    if (termsConditionsLen) {

      for (let i = (termsConditionsLen - 1); i >= 0; i--) {
        if (lastBuyerTnc !== null && lastSellerTnc !== null) {
          break;
        }

        const tncObj = termsConditions[i];

        if (tncObj.type === Constants.BUYER && tncObj.is_active) {
          lastBuyerTnc = tncObj;
          const lastBuyerTncVersion = Number(lastBuyerTnc.terms_conditions_version.replace("v", ""));
          currentBuyerTncVersion = 'v' + (lastBuyerTncVersion + 1);

        } else if (tncObj.type === Constants.SELLER && tncObj.is_active) {
          lastSellerTnc = tncObj;
          const lastSellerTncVersion = Number(lastSellerTnc.terms_conditions_version.replace("v", ""));
          currentSellerTncVersion = 'v' + (lastSellerTncVersion + 1);
        }
      }
    }

    try {
      const environment = process.env.ENVIRONMENT.toLocaleLowerCase();
      const url = `${process.env.CLOUD_FRONT_TNC_BASE_URL}/${environment}/latest/backup_tnc.txt`;

      const response = (await this.httpService.axiosRef.get(url)).data;
      if (response) {
        const responseArr = response.split("\n");

        if (responseArr.length < 2) {
          return { [responseErrorTag]: "Error occured while getting buyer or seller backup file from aws" };
        }

        const buyerCloudfrontUrl = `${process.env.CLOUD_FRONT_TNC_BASE_URL}/${environment}${responseArr[0].replace("buyer:", "")}`;
        const sellerCloudfrontUrl = `${process.env.CLOUD_FRONT_TNC_BASE_URL}/${environment}${responseArr[1].replace("seller:", "")}`;

        if (!buyerCloudfrontUrl || !sellerCloudfrontUrl) {
          return { [responseErrorTag]: "Buyer or Seller backup tnc file not found" };
        }

        // check duplicate buyer tnc
        if (termsConditions.some(tnc => tnc.cloudfront_url === buyerCloudfrontUrl && tnc.type === Constants.BUYER)) {
          return { [responseErrorTag]: "T&C already updated" };
        }

        // check duplicate seller tnc
        if (termsConditions.some(tnc => tnc.cloudfront_url === sellerCloudfrontUrl && tnc.type === Constants.SELLER)) {
          return { [responseErrorTag]: "T&C already updated" };
        }

        if (lastBuyerTnc) {
          // update cloudfront_url to store backup tnc url
          await this.dbServiceObj.updateByColumnId(this.referenceDataBryzosTermsConditionsRepository, { is_active: false, cloudfront_url: buyerCloudfrontUrl }, "id", lastBuyerTnc.id);
        }
        const cloudFrontBuyerTncUrl = `${process.env.CLOUD_FRONT_TNC_BASE_URL}/${environment}/latest/buyer_widget_tnc.html`;
        const buyerSaveDto = { cloudfront_url: cloudFrontBuyerTncUrl, terms_conditions_version: currentBuyerTncVersion, type: Constants.BUYER };
        await this.dbServiceObj.saveData(buyerSaveDto, this.referenceDataBryzosTermsConditionsRepository);

        // check duplicate
        if (lastSellerTnc) {
          // update cloudfront_url to store backup tnc url
          await this.dbServiceObj.updateByColumnId(this.referenceDataBryzosTermsConditionsRepository, { is_active: false, cloudfront_url: sellerCloudfrontUrl }, "id", lastSellerTnc.id);
        }
        const cloudFrontSellerTncUrl = `${process.env.CLOUD_FRONT_TNC_BASE_URL}/${environment}/latest/seller_widget_tnc.html`;
        const sellerSaveDto = { cloudfront_url: cloudFrontSellerTncUrl, terms_conditions_version: currentSellerTncVersion, type: Constants.SELLER };
        await this.dbServiceObj.saveData(sellerSaveDto, this.referenceDataBryzosTermsConditionsRepository);

        const updateCacheUrl = `${process.env.BRYZOS_WIDGET_SERVICE_URL}/reference-data/updateCache`;

        const response1 = await this.updateCache(updateCacheUrl, accessToken, Constants.WIDGET_SERVICE_REFERENCE_DATA);
        if (!response1) {
          return { [responseErrorTag]: "Unable to clear cache" };
        }

        const response2 = await this.updateCache(updateCacheUrl, accessToken, Constants.HOMEPAGE_WIDGET_SERVICE_REFERENCE_DATA);
        if (!response2) {
          return { [responseErrorTag]: "Unable to clear homepage cache" };
        }

        return 'Successfully updated!';
      } else {
        return { [responseErrorTag]: "Backup file not found in aws" };
      }
    } catch (error) {
      return { [responseErrorTag]: "Unable to clear homepage cache" };
    }
  }

  async updateCache(url: string, accessToken: string, eventType: any) {
    const securityData = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository, "name", libConstants.SECURITY_SECRET_KEY);
    if (securityData) {
      const securityHashKey = createHash('sha256').update(securityData.value).digest('hex');

      const headers = {
        accesstoken: accessToken,
        origin: process.env.AD_ORIGIN,
        referer: process.env.AD_REFERER,
        security: securityHashKey,
        'User-Agent': process.env.SM_BASE_URL_USER_AGENT
      };

      const payload = {
        data: {
          type: eventType
        }
      };

      try {
        const response = (await this.httpService.axiosRef.post(url, payload, { headers })).data;
        return response;
      } catch (error) {
        BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG);
      }
    }
  }

  async getSafeUploadComments(id) {
    let response = null;

    if(id){
      const mapper = { selectFields: ['id', 'homepage_safe_uploads_id', 'user_name', 'comment', 'show_comment', 'is_active'] };
      response = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.homepageSafeUploadCommentsRepo, {"homepage_safe_uploads_id": id},mapper, "created_date", "DESC");
    }

    if (response === null || response === undefined) {
      return { [responseErrorTag]: 'Something went wrong!' };
    } else if (Array.isArray(response) && response.length < 1) {
      return { [responseErrorTag]: 'No data found' };
    }
    return response;
  }

  async updateSafeUploadComments(payload: SafeUploadCommentsDto[]) {
    let response = null;

    const updateArray = [];

    for (const element of payload) {
        updateArray.push({
            id: element.comment_id,
            show_comment: element.show_comment,
            comment: element.comment || null,
            user_name: element.user_name || null,
        });
    }

    if (updateArray.length) {
        const updateDto = updateArray.map(item => ({
            id: item.id,
            show_comment: item.show_comment,
            comment: item.comment || null,
            user_name: item.user_name || null,
        }));

        try {
            const affectedRows = await this.dbServiceObj.saveData(updateDto , this.homepageSafeUploadCommentsRepo);
            response = "Successfully updated";
        } catch (error) {
            console.error("Error updating records:", error);
            response = { error_message: "Something went wrong" };
        }
    }

    return response;
  }

  async saveVideoInLibrary(adminId:string,payload:VideoLibraryDto,viaEdit:boolean=false){
    try{
      let tags = payload.tags.map(tag => tag.trim()) // Remove unwanted spaces from tags
      const maxSequenceContditions = [ 
        { columnName: 'is_active', operator: '=', value: true },
        { columnName: 'tags', operator: 'IN', value: tags },
        { columnName: 'show_on_ui', operator: '=', value: true },
      ];
      const selectedFields = ["tags","MAX(sequence) AS max_sequence","MIN(sequence) AS min_sequence"];

      let saveArray = [];
      let maxSequencesOfTags = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.extendedWidgetVideoLibraryRepository,maxSequenceContditions,selectedFields,null,"tags");

      let tagData = await this.dbServiceObj.findManyByWhereAndWhereIn(this.referenceDataVideoLibraryTagRepository,{'add_at_top': true,'is_internal_tag' :false}, 'query_param', tags);

      for(let key in tags) {
        let tag = tags[key];
        let findTagObj = maxSequencesOfTags.find(item => item.tags === tag);

        let addAtTop = tagData.find(item => item.query_param === tag)?.add_at_top;
        
        let sequence = 1;
        if(addAtTop && findTagObj) {
          sequence = findTagObj?.min_sequence;
          await this.dbServiceObj.updateWithoutMapper({ sequence: () => 'sequence + 1' }, 'tags', tag, this.extendedWidgetVideoLibraryRepository);
        } else {
          sequence = findTagObj?.max_sequence ? Number(findTagObj?.max_sequence) + 1 : 1;
        }

        let isLargeFile = payload.hasOwnProperty('is_large_file') && payload.is_large_file ? true : false;

        let saveDto = {
          video_id : uuidv4(),
          admin_id : adminId,
          title : payload.title,
          caption : payload?.caption ?? null,
          description : payload?.description ?? null,
          video_s3_url : payload.video_s3_url,
          thumbnail_s3_url_map : payload.thumbnail_s3_url,
          thumbnail_s3_url : payload.thumbnail_s3_url.electron_player,
          subtitle_s3_url : payload?.subtitle_s3_url ?? null,
          tags : tag,
          sequence : sequence,
          share_video_url : payload?.share_video_url ?? null,
          is_large_file : isLargeFile,
          internal_tags : payload?.internal_tags ?? null
        }
        saveArray.push(saveDto);
      }
      await this.dbServiceObj.saveData(saveArray,this.extendedWidgetVideoLibraryRepository);
      if(!viaEdit){ this.notifyNewVideoAdded(); } 
      return "Video uploaded successfully";
    }catch(error){
      BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG);
      return { error_message : "Something went wrong!!" };
    }
  }

  async updateVideoInLibrary(adminId:string,payload:updateVideoLibraryDto[]){
    let newTagsObjects = payload.filter(obj => !obj.hasOwnProperty('id'));
    let newTags = newTagsObjects.map(obj => obj.tags);
    let isLargeFile = payload[0].hasOwnProperty('is_large_file') ? payload[0].is_large_file : false;

    let saveNewTags = {};
    if(newTags.length > 0){
      let savePayload : VideoLibraryDto = {
        "title": payload[0].title,
        "caption": payload[0].caption,
        "description": payload[0].description,
        "video_s3_url": payload[0].video_s3_url,
        "thumbnail_s3_url": payload[0].thumbnail_s3_url,
        "subtitle_s3_url": payload[0]?.subtitle_s3_url ?? null,
        "tags": newTags,
        "share_video_url" : payload[0]?.share_video_url ?? null,
        "is_large_file" : isLargeFile,
        "internal_tags" : payload[0]?.internal_tags
      }
      saveNewTags = await this.saveVideoInLibrary(adminId,savePayload,true);
    }
    payload = payload.filter(obj => obj.hasOwnProperty('id'))
    .map(obj => ({
      ...obj,
      sequence: !obj.show_on_ui ? obj.sequence * 10000 : obj.sequence,
    }));

    try{
      let saveArray = [];
      let idArray = payload.map(obj=>obj.id);

      const tags = [...new Set(payload.map(obj => obj.tags.trim()))];

      const tagscount = tags.length;
      const allEntriesOfTags = await this.dbServiceObj.findManyByWhereIn(this.extendedWidgetVideoLibraryRepository,"tags",tags);

      let tagErrorMessage = "";
      let i = 1
      for(let key in tags){
        let tag = tags[key];

        let tagEntriesInPayload = payload.filter(obj => obj.tags === tag)
        .reduce((acc, obj) => {
            acc[obj.id] = obj.sequence.toFixed(2);
            return acc;
        }, {});

        let tagEntriesInDb = allEntriesOfTags.filter(obj => obj.tags === tag)
        .reduce((acc, obj) => {
            acc[obj.id] = obj.sequence;
            return acc;
        }, {});

        let mergeBothObject = {...tagEntriesInDb,...tagEntriesInPayload};
        const valuesArray = Object.values(mergeBothObject);
        const hasDuplicates = valuesArray.some((value, index) => valuesArray.indexOf(value) !== index);

        if(hasDuplicates){
          tagErrorMessage = i + 1 === tagscount && i != 1 ? tagErrorMessage+tag+" and " : tagErrorMessage+tag+", ";
        }
        i++;
      }
      
      if(tagErrorMessage != ""){
        tagErrorMessage = tagErrorMessage.replace(/, $/, ''); 
        return {
          error_message : "Duplicate entries of sequence found in the following tags : "+tagErrorMessage
        }
      }

      const allEntriesOfIds = await this.dbServiceObj.findManyByWhereIn(this.extendedWidgetVideoLibraryRepository,"id",idArray);

      for(let item of payload){
        let id = item.id;

        let existingRecord = allEntriesOfIds.find(obj => obj.id === id);
        let viewCount =  existingRecord.view_count ;
        let createdDate = existingRecord.created_date ;

        let sequence = item.sequence;

        let saveDto = {
          video_id : item.video_id,
          admin_id : adminId,
          title : item.title,
          caption : item?.caption ?? null,
          description : item?.description ?? null,
          video_s3_url : item.video_s3_url,
          thumbnail_s3_url_map : item.thumbnail_s3_url,
          thumbnail_s3_url : item.thumbnail_s3_url.electron_player,
          subtitle_s3_url : item?.subtitle_s3_url ?? null,
          tags : item.tags.trim(),
          sequence : sequence,
          show_on_ui : item.show_on_ui ? true : false,
          view_count : viewCount,
          share_video_url : item?.share_video_url ?? null,
          is_large_file : isLargeFile ? true : false,
          created_date : createdDate,
          internal_tags : item?.internal_tags ?? null
        }
        saveArray.push(saveDto);
      }
      await this.dbServiceObj.saveData(saveArray,this.extendedWidgetVideoLibraryRepository);
      await this.dbServiceObj.updateByMultipleWhereIn({ is_active : false },{id:idArray},this.extendedWidgetVideoLibraryRepository);

      //Notify websocket about edit
      this.notifyNewVideoAdded();

      if(saveNewTags.hasOwnProperty('error_message')){
        return { error_message : "Data have been updated successfully but something went wrong while adding new tags" };
      }

      return "Video updated successfully";
    }catch(error){
      BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG);
      if(!saveNewTags.hasOwnProperty('error_message')){
        return { error_message : "New tags where added successfully but something went wrong while updating data for existing tags" };
      }
      return { error_message : "Something went wrong!!" };
    }
  }

  async removeVideo(id:string) {
    let updateResponse = await this.dbServiceObj.markInActive('id', id, this.extendedWidgetVideoLibraryRepository);
    if(updateResponse) { return "Video removed successfully"; } 
    else { return "Unable to delete video"; }
  }

  async getVideosFromLibrary(options: { page: number, limit: number, search: string }){
    try{
      const conditions = [
        {column: "is_active", operator:"=", value: true},
        { column: ['title', 'caption', 'description', 'video_s3_url', 'thumbnail_s3_url', 'tags', 'internal_tags'], operator: 'LIKE', value: options.search }
      ]
      const orderBy = { 'table1.created_date' : 'DESC' };
      const mapperFields = {
        selectFields : [
          'table1.id as id',
          'table1.video_id as video_id',
          'table1.admin_id as admin_id',
          'CASE WHEN table1.show_on_ui = FALSE THEN  ROUND(table1.sequence/10000,2) ELSE table1.sequence END AS sequence',
          'table1.title as title',
          'table1.caption as caption',
          'table1.description as description',
          'table1.video_s3_url as video_s3_url',
          'table1.thumbnail_s3_url_map as thumbnail_s3_url',
          'table1.subtitle_s3_url as subtitle_s3_url',
          'table1.tags as tags',
          'table1.share_video_url as share_video_url',
          'table1.view_count as view_count',
          'table1.show_on_ui as show_on_ui',
          'table1.is_large_file as is_large_file',
          'table1.created_date as created_date',
          'table1.internal_tags as internal_tags'
        ]
      }
      const videos = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.extendedWidgetVideoLibraryRepository,[],conditions,mapperFields,orderBy,null,options);

      let items = videos.hasOwnProperty('items') ? videos.items : videos;
      let meta = videos.hasOwnProperty('meta') ? videos.meta : null;

      if(items.length > 0){
        return { items : items, meta :meta }
      }
      return { error_message : "No Data found" };
    }catch(error){
      BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG);
      return { error_message : "Something went wrong!!" };
    }
  }

  async getVideoTags(videoFileName:string){
    let columnContditions = [
      { columnName: 'is_active', operator: '=', value: true },
      { columnName: ['video_s3_url'], operator: 'Like', value: `%${videoFileName}` },
    ]
    let orderBy = { 'created_date' : 'DESC' };
    const videoTags = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.extendedWidgetVideoLibraryRepository,columnContditions,"*",orderBy);
    if(videoTags.length > 0 ){
      let tagsArray = videoTags.map(obj => obj.tags);
      return [...new Set(tagsArray)];
    };
    return null;
  }

  async notifyNewVideoAdded() {
    let videoList = [];
    let data = {};
    let allTags = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.referenceDataVideoLibraryTagRepository, { is_internal_tag: false}, { selectFields: ['*'] });

    allTags = allTags.filter(obj => obj.show_on_app || obj.show_on_safe);
    // Extract query_param values into an array
    const queryParams = allTags.map(item => item.query_param);

    let columnContditions = [
      { columnName: 'tags', operator: 'IN', value: queryParams },
      { columnName: 'is_active', operator: '=', value: true },
      { columnName: 'show_on_ui', operator: '=', value: true },
    ]
    let orderBy = {
      sequence  : "ASC"
    }
    videoList = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.extendedWidgetVideoLibraryRepository,columnContditions,"*",orderBy);
    const tagShowOnSafe = allTags.filter(obj=> obj.show_on_app === false && obj.show_on_safe === true);
    const queryParamsShowOnSafe =tagShowOnSafe.map(item => item.query_param);
    // sorting 'safe-intro' videos with created_date
    const safeIntroVideos = videoList.filter(video => queryParamsShowOnSafe.includes(video.tags));
    safeIntroVideos.sort((a, b) => new Date(b.created_date).getTime() - new Date(a.created_date).getTime());
    // Combine sorted 'safe-intro' videos with the rest of the videos
    const otherVideos = videoList.filter(video => !queryParamsShowOnSafe.includes(video.tags));
    const finalVideoList = [...safeIntroVideos, ...otherVideos];

    for (const tag of queryParams) {
      const filteredRows = finalVideoList.filter(row => row.tags === tag);
      // Only add to response if there are matching rows
      if (filteredRows.length > 0) {
        data[tag] = filteredRows;
      }
    }

    const url = 'notifyaddOrEditVideoInLibrary';
    this.utils.sendDataToWebsocket({ data },url);
  }

  async saveVideoTag(payload:VideoTagsDto){
    try{
      payload.show_on_app = payload.show_on_app ? true : false;
      payload.show_on_safe = payload.show_on_safe ? true : false;
      payload.add_at_top = payload.add_at_top ? true : false;
      payload.shuffle_sequence = payload.shuffle_sequence ? true : false;
      payload.query_param = payload.query_param.trim();
      const checkDuplicateParam = await this.dbServiceObj.findOne(this.referenceDataVideoLibraryTagRepository,"query_param",payload.query_param);
      if(checkDuplicateParam){  
        return { error_message : "query param '"+payload.query_param+"' already exist" }
      }
      await this.dbServiceObj.saveData(payload, this.referenceDataVideoLibraryTagRepository);
      return "Tag added successfully";
    }catch(error){
      console.log(error);
      return { error_message : "Something went wrong!!" };
    }
  }

  async updateVideoLibraryTag(payload:UpdateVideoTagsDto[]){
    try{
      const updatedData = payload.map(item => ({
        ...item,
        show_on_app: Boolean(item.show_on_app),
        show_on_safe: Boolean(item.show_on_safe),
        add_at_top: Boolean(item.add_at_top),
        shuffle_sequence: Boolean(item.shuffle_sequence),
      }));

      await this.dbServiceObj.saveData(updatedData, this.referenceDataVideoLibraryTagRepository);
      const url = 'notifyEditVideoTag';
      this.utils.sendDataToWebsocket({ data : updatedData },url);
      return "Tag Updated Successfully";
    }catch(error){
      return { error_message : "Something went wrong!!" };
    }
  }

  async getExtractDataofSellerInvoice(options: { page: number, limit: number, search: string, sort: string, isShowAll: boolean, emailSent: boolean|string }){
    let meta;
    let items;
    let conditions = [
      {column: "is_active", operator:"=", value: true},
      { column: ['from_email', 'buyer_email', 'invoice_total', 'original_file_name', 'vendor_name', 'extracted_po_number', 'po_number'], operator: 'LIKE', value: options.search }
    ]
    if(!options.isShowAll){
      conditions.push({column: "is_hidden", operator:"=", value: false})
    }
    
    if(options.emailSent != "all"){
      if(options.emailSent === true){
        conditions.push({column: "is_email_sent", operator:"=", value: true})
        conditions.push({column: "po_number", operator:"NOT NULL", value: null})
      }else if(options.emailSent === false){
        conditions.push({column: "is_email_sent", operator:"NULL", value: null})
      }
    }

    const leftJoin = [
      {"table":"user_purchase_order","joinColumn":"seller_po_number","mainTableColumn":"po_number"}
    ];

    const selectedFields = [
      'table1.id AS id',
      'table1.extracted_po_number AS extracted_po_number',
      'table1.po_number AS po_number',
      'table1.vendor_name AS vendor_name',
      'table1.item_count AS item_count',
      'table1.invoice_date AS invoice_date',
      'table1.invoice_total AS invoice_total',
      'table1.total_tax AS total_tax',
      'table1.s3_url AS seller_invoice_s3_url',
      'table1.file_name AS file_name',
      'table1.invoice_line_items AS invoice_line_items',
      'table1.buyer_email AS buyer_email',
      'table1.from_email AS from_email',
      'table1.original_file_name AS original_file_name',
      'table1.buyer_invoice_s3_url AS buyer_invoice_s3_url',
      'table1.is_po_line_matched AS is_po_line_matched',
      'table1.is_email_sent AS is_email_sent',
      'table1.created_date AS created_date',
      'table1.is_hidden AS is_hidden',
      'table1.is_cancelled AS is_cancelled',
      'DATE_FORMAT(CONVERT_TZ(user_purchase_order.last_invoice_sent, "UTC", "America/Chicago"), "%c/%e/%y  %h:%i %p") as last_invoice_sent',
      'user_purchase_order.buyer_invoice_name AS buyer_invoice_file_name'
    ]

    const mapperFields = { 'selectFields' : selectedFields };

    const sort = options.sort.trim() !== "" ? options.sort.split(",") : ["created_date:desc","id:desc"];
    
    let orderBy = {};
    const amountColumnsArray = ["invoice_total","total_tax"];
    if (sort && sort.length > 0) {
      sort.forEach(sortParam => {
          const [column, direction] = sortParam.split(':');
          if(amountColumnsArray.includes(column)){
            orderBy[`CAST(REGEXP_REPLACE(table1.${column}, '[^0-9.]', '') AS DECIMAL(10,2))`] = direction.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
          }else{
            orderBy[`table1.${column}`] = direction.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
          }
      });
    }

    let getExtractedDataOfSellerInvoice = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.sellerInvoiceDumpRepository,leftJoin,conditions,mapperFields,orderBy,null,options);
    
    if(getExtractedDataOfSellerInvoice?.items?.length == 0){
      options.page=1; 
      getExtractedDataOfSellerInvoice=await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.sellerInvoiceDumpRepository,leftJoin,conditions,mapperFields,orderBy,null,options);
    }
    
    meta = getExtractedDataOfSellerInvoice?.meta ?? null 
    items = getExtractedDataOfSellerInvoice.hasOwnProperty('items') ? getExtractedDataOfSellerInvoice.items : getExtractedDataOfSellerInvoice;
    return { items : items,  meta : meta};
  }

  async hideUnhideSellerInvoice(payload:HideRecordSellerInvoiceDto){
    try{
      (payload.is_hidden as any) === 1 ? payload.is_hidden=true : (payload.is_hidden as any) === 0 ? payload.is_hidden=false : payload.is_hidden;
      await this.dbServiceObj.saveData(payload,this.sellerInvoiceDumpRepository)
      
      if(payload.is_hidden){
        return "The record has been successfully concealed."
      }
      else{
        return "The record has been restored."
      }
    }
    catch(error){
      BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG);
      return { error_message : "Something went wrong" }
    }
  }

  async saveAchCreditRequest(data: AchCreditRequestDto, adminid: string) {
    let isAchPoApproved = data.is_ach_po_approved;
    let poNumber = data.po_number;
    let userId = data.user_id;
    let response = null;
  
    const purchaseOrder = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository, { buyer_id: userId, buyer_po_number: poNumber });
    if (!purchaseOrder) {
      return "Sorry purchase order number not found!!!";
    }
  
    const purchaseOrderId = purchaseOrder.id;
  
    if (purchaseOrder.is_ach_po_approved) {
      return { 'error_message': "Action already performed" };
    } else if (!purchaseOrder.is_active) {
      return { 'error_message': "Order is Inactive" };
    }
  
    if (!isAchPoApproved) {
      await this.rejectPurchaseOrder(poNumber, userId);
      response = "Order Rejected";
    } else if (isAchPoApproved) {
      await this.dbServiceObj.updateByMultipleWhere({ 'claimed_by': Constants.CLAMIED_BY_PENDING_STATUS, 'is_ach_po_approved': true, 'ach_po_approved_date': moment.utc().format("YYYY-MM-DD HH:mm:ss") }, { buyer_po_number: poNumber, buyer_id: userId }, this.userPurchaseOrderRepository);
      this.awsQueue.sendAchCreditOrderApprovalResponse(purchaseOrderId);
  
      const poNum = await this.sendDataForPoBroadcast(poNumber, userId);
      if (poNum?.[responseErrorTag]) {
        response = poNum;
      } else if (poNum) {
        response = "Order Successfully Approved";
      }
    }
    this.saveWidgetLogAchCredit(data, adminid);
    return response;
  }
  
  async rejectPurchaseOrder(poNumber: string, userId: string) {
    await this.dbServiceObj.updateByMultipleWhere({ is_active: false, is_ach_po_approved: false }, { buyer_po_number: poNumber, buyer_id: userId }, this.userPurchaseOrderRepository);
    await this.dbServiceObj.updateByMultipleWhere({ is_active: false }, { buyer_po_number: poNumber, buyer_id: userId }, this.userPurchaseOrderLineRepository);
  }
  
  async saveWidgetLogAchCredit(data, adminId: string) {
    const updateDto = { user_id: data.user_id, po_number: data.po_number, is_ach_po_approved: data.is_ach_po_approved, admin_id: adminId };
    this.dbServiceObj.saveData(updateDto, this.adminLogAchCreditPoRequestRepository)
  }
  
  async sendDataForPoBroadcast(poNum, userId) {
    const purchaseOrder = await this.dbServiceObj.findOneByMultipleWhereAndCustomSelectFields(
      this.userPurchaseOrderRepository,
      { 'buyer_id': userId, 'buyer_po_number': poNum },
      { selectFields: ['id', 'line1', 'line2', 'city', 'state_id', 'zip', 'buyer_po_number', 'freight_term', 'payment_method', 'claimed_by', 'sales_tax', 'DATE_FORMAT(created_date, "%m/%d/%Y %h:%i %p") as created_date', 'DATE_FORMAT(ach_po_approved_date, "%m/%d/%Y %h:%i %p") as ach_po_approved_date', 'DATE_FORMAT(delivery_date, "%m/%d/%Y %h:%i %p") as delivery_date', 'IFNULL(actual_seller_po_price, seller_po_price) as seller_po_price', 'IFNULL(actual_total_weight, total_weight) as total_weight', 'seller_sales_tax', 'buyer_email', 'buyer_company_id'] }
    );
    if (!purchaseOrder) {
      return { [responseErrorTag]: "Sorry purchase order number not found!!!" };
    }
  
    const purchaseLines = await this.dbServiceObj.findManyByMultipleWhereAndSelect(
      this.userPurchaseOrderLineRepository,
      { 'purchase_order_id': purchaseOrder.id },
      { selectFields: ['id', 'purchase_order_id', 'line_id', 'description', 'reference_product_id', 'qty_unit', 'seller_price_per_unit', 'price_unit', 'buyer_po_number', 'po_line', 'shape', 'DATE_FORMAT(created_date, "%m/%d/%Y %h:%i %p") as created_date', 'actual_qty as qty', 'actual_seller_line_total as seller_line_total', 'actual_total_weight as total_weight', 'domestic_material_only', 'seller_sales_tax', 'product_id'] }
    );
  
    const buyerMainCompanyId = purchaseOrder.buyer_company_id;
    purchaseOrder.items = purchaseLines;
    purchaseOrder.is_order_view = false;
    const poNumber = purchaseOrder.buyer_po_number;
    const url = process.env.GISS_WS_SERVER + '/purchase';
  
    try {
      const payload = { 'buyer_po_number': purchaseOrder.buyer_po_number, 'po_data': purchaseOrder, 'event': 'ACH_CREDIT_PO_APPROVED', 'buyer_company_id': buyerMainCompanyId }
      const headers = { 'Content-Type': 'application/json', 'gissToken': process.env.GISS_UI_TOKEN }
      const response = (await this.httpService.axiosRef.post(url, payload, { headers })).data;
      BryzosLogger.log(JSON.stringify(response), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
    } catch (err) {
      BryzosLogger.log(JSON.stringify(err), process.env.LOGGLY_ERROR_TAG);
    }
    return poNumber;
  }
  
  async getBuyerCreditLimit(userId: string) {
    let response = null;
    let status = null;
  
    const creditLimitData = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository, { user_id: userId, is_active: true });
    if (!creditLimitData) {
      return response;
    }
  
    const outstandingAmt = Number(creditLimitData.bryzos_credit_limit) - Number(creditLimitData.bryzos_available_credit_limit);
    if (creditLimitData.is_approved === 1) {
      status = Constants.BALANCE_ACCOUNT_APPROVED;
    } else if (creditLimitData.is_approved === 0) {
      status = Constants.BALANCE_ACCOUNT_REJECTED;
    } else {
      status = Constants.BALANCE_ACCOUNT_PENDING;
    }
  
    response = {
      balance_credit_limit: +creditLimitData.bryzos_credit_limit,
      balance_available_credit_limit: +creditLimitData.bryzos_available_credit_limit,
      outstanding_amount: outstandingAmt,
      status: status,
    };
    return response;
  }
  
  async updateUserStatus(data: UserStatusDto, adminId: number, accessToken: string) {
    const id = data.id;
    const isActive = data.is_active;
  
    const userData = await this.dbServiceObj.findOne(this.userRepository, "id", id.toString(), false);
    if (!userData) {
      return { [responseErrorTag]: "Sorry user not found" };
    }
    if (userData.status === Constants.USER_DELETED) {
      return { [responseErrorTag]: "This account was deleted by the user. Please contact support for assistance" };
    }
  
    const updateResult = await this.dbServiceObj.updateByColumnId(this.userRepository, { is_active: !!isActive }, "id", id);
    if (!updateResult) {
      return { [responseErrorTag]: "Sorry unable to update user" };
    }
  
    if (!isActive) {
      const emailId = userData.email_id;
      this.logUserStatus(data, adminId);
  
      const securityData = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository, "name", Constants.SECURITY_SECRET_KEY);
      if (securityData) {
        const securityHashKey = createHash('sha256').update(securityData.value).digest('hex');
  
        const pusherSendNotificationResponse = await this.forceLogoutSendPusherNotification(emailId, accessToken, securityHashKey);
        if (!pusherSendNotificationResponse) {
          return { [responseErrorTag]: "Sorry unable to force logout user" };
        }
  
        const forceLogoutUserResponse = await this.utils.sendWebsocketEvent({ 'email_id': emailId }, 'forceLogoutUser');
        if (!forceLogoutUserResponse) {
          return { [responseErrorTag]: "Sorry unable to force logout user" };
        }
      } else {
  
      }
    }
    return "Successfully updated";
  }
  
  logUserStatus(data, adminId) {
    const saveDto = {
      user_id: data.id,
      user_status: data.is_active,
      admin_user_id: adminId,
    }
    this.dbServiceObj.saveData(saveDto, this.adminLogUserUpdateRepository);
  }
  
  async forceLogoutSendPusherNotification(emailId: string, accessToken: string, securityHashKey: string) {
    const url = process.env.NOTIFICATION_SERVER_URL + '/logoutPusherNotification';
    const payload = { [responseTag]: { 'email_id': emailId } };
    const headers = {
      'Content-Type': 'application/json',
      'AccessToken': accessToken,
      'origin': process.env.AD_ORIGIN,
      'referer': process.env.AD_REFERER,
      'security': securityHashKey
    };
  
    try {
      const response = (await this.httpService.axiosRef.post(url, payload, { headers })).data;
      BryzosLogger.log(JSON.stringify({ response_type: "Notification response", response }), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
      return response;
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error_type: "Notification error", error }), process.env.LOGGLY_ERROR_TAG);
      ExceptionService.log(error);
    }
  }
  
  async getHolidayCalendar(){
    const data = await this.dbServiceObj.findAll(this.extendedWidgetHolidayCalendarRepository, { "created_date": "DESC" });
    return data;
  }

  async saveHolidayCalendar(holidayCalendarDto: HolidayCalendarDto[], adminId: string) {
    let response = null;

    try {
      // Fetch the admin's email based on adminId
      const adminEmail = ( await this.dbServiceObj.findOne(this.userRepository, 'id', adminId) )?.email_id;
  
      // Fetch existing holidays
      const existingHolidays = await this.dbServiceObj.findAll(this.extendedWidgetHolidayCalendarRepository);
  
      // Use a Set for efficient lookups
      const existingHolidayDates = new Set(existingHolidays.map((holiday) => holiday.holiday_date));

      // Remove duplicate dates within the payload
      const uniquePayloadHolidays = Array.from(
        new Map(
          holidayCalendarDto.map((holiday) => [holiday.holiday_date, holiday]) // Map by holiday_date
        ).values()
      );

      // Filter holidays that are not in the existing database and are not weekends or past dates
      const holidaysInsert = uniquePayloadHolidays
      .filter((holiday) => {
        const holidayDate = new Date(holiday.holiday_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        // Check if the date is a weekend (Saturday or Sunday) or in the past
        const isWeekend = holidayDate.getDay() === 0 || holidayDate.getDay() === 6; // 0 = Sunday, 6 = Saturday
        const isPastDate = holidayDate < today;
        return !existingHolidayDates.has(holiday.holiday_date) && !isWeekend && !isPastDate;
      })
      .map((holiday) => ({
        holiday_date: holiday.holiday_date,
        description: holiday.description || null,
        added_by: adminEmail,
        days_to_skip: holiday.days_to_skip,
        is_day_before: holiday.is_day_before ? true : false,
        is_day_after: holiday.is_day_after ? true : false,
        holiday_start_time: holiday.holiday_start_time,
      }));

      // Insert new holidays if there are any
      if (holidaysInsert.length > 0) {
        await this.dbServiceObj.saveData(holidaysInsert, this.extendedWidgetHolidayCalendarRepository);
        let holidayWord = holidaysInsert.length === 1 ? "Holiday" : "Holidays";
        response = `${holidaysInsert.length} ${holidayWord} saved successfully.`;
      } else {
        response = {error_message: "Oops! Holiday not added: Either it's a repeat, or a weekend, or a blast from the past!"};
      }
      
    } catch (error) {
      console.log("Error saving holiday calendar:", error);
      response = { error_message: "Something went wrong" };
    }
  
    return response;
  }

  async deleteHolidayEntry(payloadData: string[], adminId: string) {
    try {
      
      // Fetch the admin's email based on adminId
      const adminEmail = ( await this.dbServiceObj.findOne(this.userRepository, 'id', adminId) )?.email_id;
  
      const columnContditions = [
        { columnName: 'id', operator: 'IN', value: payloadData},
        { columnName: 'is_active', operator: '=', value: true },
      ];

      const existingHolidays = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.extendedWidgetHolidayCalendarRepository,columnContditions,['*']);
 
      if (existingHolidays?.length === 0) {
        return { error_message: "Holiday has already been removed or No data exist." };
      }
  
      const discardedHolidays = existingHolidays.map((holiday) => ({
        id: holiday.id,
        is_active: false,
        added_by: adminEmail,
      }));

      await this.dbServiceObj.saveData(discardedHolidays, this.extendedWidgetHolidayCalendarRepository);
  
      return "Holiday removed successfully.";
    } catch (error) {
      console.log("Error in deleteHolidayEntry:", error);
      return { error_message: "Something went wrong!" };
    }
  }
  
  async getInternalTags(){
    const getAllInternalTags = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.referenceDataVideoLibraryTagRepository, { is_internal_tag: true}, { selectFields: ['*'] },'created_date', 'DESC')
    return getAllInternalTags;
  }

  async getExternalApiKeys(params: { page: number, limit: number, search: string }) {

    const leftJoins = [
      { "table": "user_main_company", "joinColumn": "id", "mainTableColumn": "company_id" },
    ];
    const conditions = [
      { column: 'is_active', operator: '=', value: true },
      { column: 'status', operator: '=', value: true },
      { column: 'is_active', operator: '=', value: true, table: "user_main_company" },
      { column: ['company_name'], operator: 'LIKE', value: params.search, table: "user_main_company" },
    ];
    const mapperFields = {
      'selectFields': [
        'user_main_company.company_name AS company_name',
        'table1.name AS api_key_name',
        'table1.api_key_id AS api_key_id',
        'table1.api_key_value AS api_key',
        'table1.description AS api_key_purpose',
        'table1.status AS api_key_status',
        'table1.mapped_endpoint AS endpoint',
        'table1.user_slots AS user_slots',
        'table1.created_date AS created_date'
      ],
    };
    const orderBy = { 'table1.created_date': 'DESC' };

    const externalApiKeyData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin( this.externalApiKeyRepository, leftJoins, conditions, mapperFields, orderBy, null, params );

    if (!externalApiKeyData) {
      return { error: 'No data found!' };
    }

    // Ensure endpoint is always an array
    externalApiKeyData.items = externalApiKeyData.items.map(item => ({
      ...item,
      endpoint: item.endpoint ? (typeof item.endpoint === 'string' ? JSON.parse(item.endpoint) : []) : [],      
    }));

    return externalApiKeyData;
  }

  async externalApiKeyMapping(externalApiKeyMapping: ExternalApiKeyMappingDto) {
    const apiKeyId = externalApiKeyMapping.api_key_id.trim();
    const mappedEndpoint = externalApiKeyMapping.endpoint;
    let apiKeyValue = null;

    // Check if API key exists in DB
    const checkApiKeyExist = await this.dbServiceObj.findOne(this.externalApiKeyRepository, "api_key_id", apiKeyId);
    if (!checkApiKeyExist || checkApiKeyExist.status != true) {
      return { error_message: 'API key Id not found.' };
    }

    // fetch api key from AWS API gateway
    try {
      const apiKey = await this.extendedLibAwsUtility.getApiKey(apiKeyId);
      if (!apiKey) {
        return { error_message: 'API key not found.' };
      }
      apiKeyValue = apiKey?.value;
    } catch (error) {
      console.log('Error fetching API key : ', error.message);
      return { error_message: 'Something went wrong!' };
    }

    // update api key & endpoint in Secret manager
    try {
      const smUpdateResponse = await this.extendedLibAwsUtility.addOrUpdateSmApiKey(process.env.EXTERNAL_SM_ENV, apiKeyValue, mappedEndpoint);
      if (smUpdateResponse['error_message']) {
        return smUpdateResponse;
      }

    } catch (error) {
      console.log('Error adding/updating api key in SM: ', error.message);
      return { error_message: 'Something went wrong!' };
    }

    // update DB mapped_endpoint
    await this.dbServiceObj.updateByMultipleWhere({ 'mapped_endpoint': mappedEndpoint, 'user_slots': externalApiKeyMapping.user_slots }, { 'api_key_id': apiKeyId, 'status': true }, this.externalApiKeyRepository);

    return "API key mapping updated successfully.";

  }

  //Sandbox API
  async getSandboxExternalApiKeys(params: { page: number, limit: number, search: string }) {

    const leftJoins = [
      { "table": "user_main_company", "joinColumn": "id", "mainTableColumn": "company_id" },
    ];
    const conditions = [
      { column: 'is_active', operator: '=', value: true },
      { column: 'status', operator: '=', value: true },
      { column: 'is_active', operator: '=', value: true, table: "user_main_company" },
      { column: ['company_name'], operator: 'LIKE', value: params.search, table: "user_main_company" },
    ];
    const mapperFields = {
      'selectFields': [
        'user_main_company.company_name AS company_name',
        'table1.name AS api_key_name',
        'table1.api_key_id AS api_key_id',
        'table1.api_key_value AS api_key',
        'table1.description AS api_key_purpose',
        'table1.status AS api_key_status',
        'table1.mapped_endpoint AS endpoint',
        'table1.user_slots AS user_slots',
        'table1.created_date AS created_date'
      ],
    };
    const orderBy = { 'table1.created_date': 'DESC' };

    const externalApiKeyData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.sandboxExternalApiKeyRepository, leftJoins, conditions, mapperFields, orderBy, null, params );

    // Ensure endpoint is always an array
    externalApiKeyData.items = externalApiKeyData.items.map(item => ({
      ...item,
      endpoint: item.endpoint ? (typeof item.endpoint === 'string' ? JSON.parse(item.endpoint) : []) : [],      
    }));

    return externalApiKeyData;
  }

  async externalApiSandboxKeyMapping(externalApiKeyMapping: ExternalApiKeyMappingDto) {
    const apiKeyId = externalApiKeyMapping.api_key_id.trim();
    const mappedEndpoint = externalApiKeyMapping.endpoint;
    let apiKeyValue = null;

    // Check if API key exists in DB
    const checkApiKeyExist = await this.dbServiceObj.findOne(this.sandboxExternalApiKeyRepository, "api_key_id", apiKeyId);  
    if (!checkApiKeyExist || checkApiKeyExist.status != true) {
      return { error_message: 'API key Id not found.' };
    }

    // fetch api key from AWS API gateway
    try {
      const apiKey = await this.extendedLibAwsUtility.getApiKey(apiKeyId);
      if (!apiKey) {
        return { error_message: 'API key not found.' };
      }
      apiKeyValue = apiKey?.value;
    } catch (error) {
      console.log('Error fetching API key : ', error.message);
      return { error_message: 'Something went wrong!' };
    }

    // update api key & endpoint in Secret manager
    try {
      const smUpdateResponse = await this.extendedLibAwsUtility.addOrUpdateSmApiKey(process.env.SANDBOX_EXTERNAL_SM_ENV, apiKeyValue, mappedEndpoint);
      if (smUpdateResponse['error_message']) {
        return smUpdateResponse;
      }

    } catch (error) {
      console.log('Error adding/updating api key in SM: ', error.message);
      return { error_message: 'Something went wrong!' };
    }

    // update DB mapped_endpoint
    await this.dbServiceObj.updateByMultipleWhere({ 'mapped_endpoint': mappedEndpoint, 'user_slots': externalApiKeyMapping.user_slots }, { 'api_key_id': apiKeyId, 'status': true }, this.sandboxExternalApiKeyRepository);

    return "API key mapping updated successfully.";

  }

  async initiateMultipartUpload(videoUploadIdDto: VideoUploadIdDto) {
    
    try {
      const fileKey = process.env.S3_BUCKET_ENVIRONMENT+ '/'+ videoUploadIdDto.file_name;
      let videoUploadId = await this.extendedLibAwsUtility.initiateMultipartUpload(videoUploadIdDto.bucket_name, fileKey);
      if (videoUploadId) {
        return videoUploadId;
      }
      return {error_message: "Something went wrong!"}
    } catch (error) {
      return {error_message: "Something went wrong!!"}
    } 
  }

  async generateMultipartPresignedUrls(videoUploadMultipartUrlDto: VideoUploadMultipartUrlDto) {
    
    try {
      videoUploadMultipartUrlDto['file_key'] = process.env.S3_BUCKET_ENVIRONMENT+ '/'+ videoUploadMultipartUrlDto.file_name;
      let videoUploadPartUrl = await this.extendedLibAwsUtility.generateMultipartPresignedUrls(videoUploadMultipartUrlDto);      
      if (videoUploadPartUrl) {
        return videoUploadPartUrl;
      }
      return {error_message: "Something went wrong!"}
    } catch (error) {
      return {error_message: "Something went wrong!!"}
    }
  }

  async completeMultipartUpload(videoCompleteMultipartUploadUrlDto: VideoCompleteMultipartUploadUrlDto) {
    
    try {
      videoCompleteMultipartUploadUrlDto['file_key'] = process.env.S3_BUCKET_ENVIRONMENT+ '/'+ videoCompleteMultipartUploadUrlDto.file_name;
      let videoUploadPartUrl = await this.extendedLibAwsUtility.completeMultipartUpload(videoCompleteMultipartUploadUrlDto);
      if (videoUploadPartUrl && videoUploadPartUrl?.Location) {
        const finalS3Url = decodeURIComponent(videoUploadPartUrl.Location);
        return finalS3Url;
      }
      return {error_message: "Something went wrong!"}
    } catch (error) {
      return {error_message: "Something went wrong!!"}
    }
  }

  async updateGameInitialSettings(changeGameStateDto: ChangeGameStateDto) {
    try {
      // Create an array of updates to process
      const updates = [
        { name: 'initial_ball_speed', value: changeGameStateDto.initial_ball_speed },
        { name: 'initial_basket_speed', value: changeGameStateDto.initial_basket_speed },
        { name: 'speed_increment_factor', value: changeGameStateDto.speed_increment_factor }
      ];

      // Process each update individually
      let updated = 0;
      for (const update of updates) {
        const result = await this.dbServiceObj.updateByMultipleWhere(
          { value: update.value },
          { name: update.name },
          this.referenceDataSettingsRepository
        );
        updated += result;
      }
      if (!updated) {
        return { error_message: "Game Settings not found!" };
      }

      return  "Game Settings updated successfully!";
    } catch (error) {
      return { error_message: "Something went wrong!" }
    }
  }

  async updateBomConfidenceConfig(confidenceLevelConfigs: ConfidenceLevelConfigDto[]) {
    try {
      // validate the ranges
      let invalidRanges = [];
      let overlappingRanges = [];
      let confidenceLevelError = [];
      let insertConfidenceRanges = [];
      for (let i = 0; i < confidenceLevelConfigs.length; i++) {
        // check if the range is valid that min_match_count is less than max_match_count
        if(confidenceLevelConfigs[i].min_match_count >= confidenceLevelConfigs[i].max_match_count && confidenceLevelConfigs[i].max_match_count !== null) {
          invalidRanges.push(`${confidenceLevelConfigs[i].min_match_count}-${confidenceLevelConfigs[i].max_match_count}`);
        }
        // check if the range is overlapping with the other ranges
        for (let j = i + 1; j < confidenceLevelConfigs.length; j++) {
          const range1 = confidenceLevelConfigs[i];
          const range2 = confidenceLevelConfigs[j];
          
          // Check if ranges overlap
          // Case 1: Both ranges have specific max_match_count values
          // Case 2: One range has null max_match_count (unbounded upper limit)
          const isOverlapping = 
            // When both have specific max values
            ((range1.max_match_count !== null && range2.max_match_count !== null) &&
              ((range1.min_match_count <= range2.max_match_count && range1.max_match_count >= range2.min_match_count) ||
               (range2.min_match_count <= range1.max_match_count && range2.max_match_count >= range1.min_match_count))) ||
            // When range1 has null max (unbounded upper)
            (range1.max_match_count === null && range1.min_match_count <= range2.max_match_count) ||
            // When range2 has null max (unbounded upper)
            (range2.max_match_count === null && range2.min_match_count <= range1.max_match_count);
          
          if (isOverlapping) {
            overlappingRanges.push(`(${range1.min_match_count}-${range1.max_match_count === null ? '∞' : range1.max_match_count}) overlaps with (${range2.min_match_count}-${range2.max_match_count === null ? '∞' : range2.max_match_count})`);
          }

          const isConfidenceLevelError = 
            (range1.min_match_count < range2.min_match_count && range1.confidence < range2.confidence) ||
            (range1.min_match_count > range2.min_match_count && range1.confidence > range2.confidence);

          if(isConfidenceLevelError) {
            confidenceLevelError.push(`${range1.min_match_count}-${range1.max_match_count} has lower confidence level than ${range2.min_match_count}-${range2.max_match_count}`);
          }
        }

        insertConfidenceRanges.push({
          min_match_count: confidenceLevelConfigs[i].min_match_count,
          max_match_count: confidenceLevelConfigs[i].max_match_count,
          confidence: confidenceLevelConfigs[i].confidence
        });
      }
      
      if(invalidRanges.length > 0) {
        return { error_message: `Invalid ranges: ${invalidRanges.join(', ')}` };
      }

      if(overlappingRanges.length > 0) {
        return { error_message: `Overlapping ranges: ${overlappingRanges.join(', ')}` };
      }

      if(confidenceLevelError.length > 0) {
        return { error_message: `Confidence level error: ${confidenceLevelError.join(', ')}` };
      }

      // // soft delete the existing active ranges
      await this.dbServiceObj.markInActive("is_active", true, this.referenceDataBomProductConfidenceRepository);
      // // update the new ranges
      await this.dbServiceObj.saveData(insertConfidenceRanges, this.referenceDataBomProductConfidenceRepository);
      
      return "Confidence level ranges updated successfully";
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: 'Failed to update BOM confidence configuration' };
    }
  }

  async updateSubscriptionPricing(subscriptionPricingData: SubscriptionPricingDto[], userId) {
    try {
      let invalidRanges = [];
      let insertSubscriptionPricingData = [];
      let overlappingRanges = [];

      for (let i = 0; i < subscriptionPricingData.length; i++) {
        // check if the range is valid that min_user_count is less than max_user_count
        if(subscriptionPricingData[i].min_user_count >= subscriptionPricingData[i].max_user_count && subscriptionPricingData[i].max_user_count !== null) {
          invalidRanges.push(`${subscriptionPricingData[i].min_user_count}-${subscriptionPricingData[i].max_user_count}`);
        }
        
        // check if the range is overlapping with the other ranges
        for (let j = i + 1; j < subscriptionPricingData.length; j++) {
          const range1 = subscriptionPricingData[i];
          const range2 = subscriptionPricingData[j];
          
          // Check if ranges overlap
          // Case 1: Both ranges have specific max_user_count values
          // Case 2: One range has null max_user_count (unbounded upper limit)
          const isOverlapping = 
            // When both have specific max values
            ((range1.max_user_count !== null && range2.max_user_count !== null) &&
              ((range1.min_user_count <= range2.max_user_count && range1.max_user_count >= range2.min_user_count) ||
                (range2.min_user_count <= range1.max_user_count && range2.max_user_count >= range1.min_user_count))) ||
            // When range1 has null max (unbounded upper)
            (range1.max_user_count === null && range1.min_user_count <= range2.max_user_count) ||
            // When range2 has null max (unbounded upper)
            (range2.max_user_count === null && range2.min_user_count <= range1.max_user_count);
          
          if (isOverlapping) {
            overlappingRanges.push(`(${range1.min_user_count}-${range1.max_user_count === null ? '∞' : range1.max_user_count}) overlaps with (${range2.min_user_count}-${range2.max_user_count === null ? '∞' : range2.max_user_count})`);
          }
        }

        insertSubscriptionPricingData.push({
          min_user_count: subscriptionPricingData[i].min_user_count,
          max_user_count: subscriptionPricingData[i].max_user_count,
          subscription_amount: subscriptionPricingData[i].subscription_amount,
          action_performed_by: userId,
        });
              
      }
      if(invalidRanges.length > 0) {
        return { error_message: `Invalid ranges: ${invalidRanges.join(', ')}` };
      }

      if(overlappingRanges.length > 0) {
        return { error_message: `Overlapping ranges: ${overlappingRanges.join(', ')}` };
      }

      // soft delete the existing active ranges
      await this.dbServiceObj.markInActive("is_active", true, this.referenceDataSubscriptionPricingTiersRepository);
      await this.dbServiceObj.saveData(insertSubscriptionPricingData, this.referenceDataSubscriptionPricingTiersRepository);
      
      return "Subscription pricing updated successfully";

    }catch(error){
      BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG);
      return { error_message: 'Failed to update subscription pricing' };
    }
  }

  async uploadFile(file, fileType, adminId: string, accessToken: string) {
    try {

       // upload split excel in public bucket
       let uploadSplitExcelResponse = await this.utils.processExportFileAndUploadToS3('search_upload_8d67ce9c-2be8-48fe-9d2b-c8a17399fdd5', 'search');
       if (uploadSplitExcelResponse && uploadSplitExcelResponse['error_message']) {
         return uploadSplitExcelResponse;
       }
      return "Upload split file in s3";

      // validate the uploaded file
      const isValidFile = await this.utils.validateUploadedFile(file);
      if (!isValidFile) {
        return { error_message: 'Invalid file' };
      } else if (isValidFile && isValidFile['error_message']) {
        return isValidFile;
      }

      // Generate a unique upload ID using UUID v4
      let uploadId = `upload_${uuidv4()}`;

      if (fileType === Constants.SEARCH_FILE_TYPE) {
        uploadId = `${Constants.SEARCH_FILE_TYPE}_${uploadId}`;
      } else if (fileType === Constants.PRICING_FILE_TYPE) {
        uploadId = `${Constants.PRICING_FILE_TYPE}_${uploadId}`;
      }

      // Save file locally
      const saveUploadedFileLocallyRes = await this.utils.saveUploadedFileLocally(file.buffer, fileType, uploadId, file.originalname);
      if (saveUploadedFileLocallyRes && saveUploadedFileLocallyRes['error_message']) {
        return saveUploadedFileLocallyRes;
      }

      // upload the given file in S3
      const saveUploadedFileToS3Res = await this.utils.saveUploadedFileToS3(file, uploadId)
      if (saveUploadedFileToS3Res && saveUploadedFileToS3Res['error_message']) {
        return saveUploadedFileToS3Res;
      }

      // Start processing an uploaded file & split into n excels
      const processFileRes = await this.processFile(uploadId);
      if (processFileRes && processFileRes['error_message']) {
        return { error_message: 'Failed to process file' };
      }

      // upload split excel in public bucket
      let uploadSplitExcelRes = await this.utils.processExportFileAndUploadToS3(uploadId, fileType);
      if (uploadSplitExcelRes && uploadSplitExcelRes['error_message']) {
        return uploadSplitExcelRes;
      }
return "Upload split file in s3";
      // delete the local files
      await this.utils.deleteLocalFile(uploadId);

      // update version number
      const version = await this.updateVersion(fileType);

      if (version) {
        // send email to the admin
        await this.awsQueue.sendReferenceDataProductExcelUpdateEmail(fileType);

        // send notification to the user
        const internalNotifcation = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataDesktopNotificationRepository, { "notification_event": Constants.INTERNAL_NOTIFICATION });
        if (internalNotifcation) {
          const payload: NotificationEventDto = {
            notification_event: internalNotifcation.notification_event,
            action: internalNotifcation.action,
            priority: internalNotifcation.priority.toUpperCase(),
            message: internalNotifcation.notification_message,
            po_number: null,
            email_id: null,
            send_to: null,
            send_to_users: null,
            expiry_date: null
          }

          const callNotificationResponse = await this.callNotificationService(payload, adminId, accessToken);

          if (typeof callNotificationResponse === "object" && callNotificationResponse[responseErrorTag]) {
            return callNotificationResponse;
          }
        }

        // send event to sqs for importing excel data to table
        // const event = fileType === Constants.SEARCH_FILE_TYPE ? "IMPORT_PRODUCT_SEARCH_EXCEL_TO_DB" : "IMPORT_PRODUCT_PRICING_EXCEL_TO_DB";

        // await this.awsQueue.sendReferenceDataExcelToImportQueue(fileType, event)
      }

      return 'File uploaded successfully.';

    } catch (error) {
      console.log(error);
      return { error_message: 'Failed to upload file' };
    }
  }

  async processFile(uploadId: string) {
    try {
      const fileInfo = await this.utils.getFileInfo(uploadId);
      if (!fileInfo || fileInfo['error_message']) {
        return {error_message: `Upload ID ${uploadId} not found`}
      }
      // Start processing
      const processExcelFileRes = await this.utils.processExcelFile( fileInfo.originalFilePath, fileInfo.fileType, uploadId, );
      if (processExcelFileRes && processExcelFileRes['error_message']) {
        return { error_message: 'Failed to process file' };
      }

      return `Processing completed for ${uploadId}`;

    } catch (error) {
      console.log("Error in processFile-1 : ", error);
      return { error_message: 'Failed to process file' };
    }

  }

  async updateVersion(fileType: string) {
    try {
      let version = null;
      if (fileType) {
        let versionLog = {};

        const existingVersion = await this.dbServiceObj.findOne(this.refDataProductsExcelLogVersionRepository, "name", fileType);

        if (!existingVersion) {
          versionLog["name"] = fileType;
          versionLog["version"] = version = "1.0.0";
          await this.dbServiceObj.saveData(versionLog, this.refDataProductsExcelLogVersionRepository);
        } else {

          await this.dbServiceObj.updateByMultipleWhere({ "is_active": false }, { "name": fileType }, this.refDataProductsExcelLogVersionRepository);

          // Parse semantic version and increment minor version
          const versionParts = existingVersion.version.split('.');
          const major = parseInt(versionParts[0]) || 1;
          const minor = parseInt(versionParts[1]) || 0;
          const patch = parseInt(versionParts[2]) || 0;

          // Increment minor version and reset patch to 0
          const newVersion = version = `${major}.${minor + 1}.${patch}`;

          versionLog["name"] = fileType;
          versionLog["version"] = newVersion;
          await this.dbServiceObj.saveData(versionLog, this.refDataProductsExcelLogVersionRepository);
        }
      }
      return version;

    } catch (error) {
      console.log(`Error updating version number: ${error}`);
      return { error_message: `Error updating version number: ${error?.message}` };
    }
  }

}
