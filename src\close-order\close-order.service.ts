import { DataBaseService, ReferenceDataSettings } from '@bryzos/base-library';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Constants } from 'src/Constants';
import { CloseOrderDto,CloseAchOrderDto } from './dto/close-order.dto';
import { UserPurchaseOrder, ReferenceDataOrderStatus, UserPurchaseOrderLine, UserPurchaseOrderLedger, AdminLogCloseOrders, CompanyBuyNowPayLater, LogBryzosCreditLimit } from '@bryzos/extended-widget-library';
import { Balance } from 'src/Balance';
import { AwsQueue } from 'src/AwsQueue';
import { format, subDays, isSameDay, parse, parseISO} from 'date-fns';

const responseErrorTag = Constants.ERROR_TAG;

@Injectable()
export class CloseOrderService {
  private dbServiceObj = new DataBaseService();

  constructor(
    private readonly balance:Balance,
    private readonly awsQueue:AwsQueue,
    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly userReferenceDataOrderStatus: Repository<ReferenceDataOrderStatus>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(AdminLogCloseOrders) private readonly adminLogCloseOrdersRepository: Repository<AdminLogCloseOrders>,
    @InjectRepository(UserPurchaseOrderLedger) private readonly userPurchaseOrderLedgerRepository: Repository<UserPurchaseOrderLedger>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(LogBryzosCreditLimit) private readonly logBryzosCreditLimitRepository: Repository<LogBryzosCreditLimit>,
    
  ) {}

  async closeorderByAdmin(adminId, closeOrderDto: CloseOrderDto)
  { 
    let response = null;
    let poNumber = closeOrderDto.po_number;
    let purchaseOrderLineIds=null;
    let purchaseOrderLineId=null;
    let orderLineData=null;
    let orderPrice=null;

    const orderData = await this.dbServiceObj.findOne(this.userPurchaseOrderRepository,'buyer_po_number',poNumber);

    const activeStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ACTIVE);
    const resolvedStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.RESOLVED);

    let currentDate = new Date();
    let today = format(currentDate, 'yyyy-MM-dd HH:mm:ss');

    if (orderData && closeOrderDto.hasOwnProperty('purchase_order_line_id') && closeOrderDto.purchase_order_line_id !== undefined && closeOrderDto.purchase_order_line_id !== null && Object.keys(closeOrderDto.purchase_order_line_id).length > 0) 
    {
      purchaseOrderLineIds=closeOrderDto.purchase_order_line_id;
      let paymentMethod  = orderData.payment_method;
      for (const OrderLineId of Object.values(purchaseOrderLineIds)) 
      {
        purchaseOrderLineId = OrderLineId.toString();

        orderLineData = await this.dbServiceObj.findOne(this.userPurchaseOrderLineRepository,'id',purchaseOrderLineId);

        if(orderLineData && orderLineData.status===null)
        {
          let buyerId=orderData.buyer_id;

          orderPrice=orderLineData.actual_buyer_line_total;

          // let autoFundDateSatisfied = false;
          // if(orderLineData.seller_funding_date)
          // {
          //   const sellerFundingDate = parse(orderLineData.seller_funding_date, 'yyyy-MM-dd HH:mm:ss', new Date());
          //   const sellerFundingDateMinusOneDay = subDays(sellerFundingDate, 1);
          //   const now = parse(today, 'yyyy-MM-dd HH:mm:ss', new Date());
          //   if(isSameDay(sellerFundingDateMinusOneDay, now) || sellerFundingDateMinusOneDay < now){
          //     autoFundDateSatisfied = true;
          //   }
          // }

          let claimedBySatisfied = null;
          let referenceId = purchaseOrderLineId;

          const paymentMethodSatisfied = [Constants.PAYMENT_METHOD_ACH_CREDIT, Constants.PAYMENT_METHOD_BRYZOS_PAY].includes(orderData.payment_method);
          const orderStatusSatisfied = [activeStatusId.id, resolvedStatusId.id].includes(orderLineData.order_status_id);
          const paymentProcessingStatusSatisfied = orderLineData.payment_processing_status === null? true: false;
          const orderCloseStatus = orderLineData.status === null? true: false;


          if (orderData.claimed_by === null || orderData.claimed_by === Constants.CLAMIED_BY_READY_TO_CLAIM || orderData.claimed_by === Constants.CLAMIED_BY_PENDING_STATUS || !orderData.claimed_by.includes('@')) {
            claimedBySatisfied = false ;
          } else if (orderData.claimed_by.includes('@')) {
            claimedBySatisfied = true; 
          }
      
          if (paymentMethodSatisfied && orderStatusSatisfied  && claimedBySatisfied  && paymentProcessingStatusSatisfied && orderCloseStatus) {
            await this.awsQueue.sendToCloseOrderQueue(referenceId,buyerId);
            await this.dbServiceObj.updateWithoutMapper({ 'status': Constants.IN_PROGRESS}, 'id', referenceId, this.userPurchaseOrderLineRepository);
            response = 'Close Order In Progress';

            const logInsert = {
              po_number: poNumber,
              purchase_order_line_id: purchaseOrderLineId,
              order_price: orderPrice,
              response: response,
              admin_user_id: adminId
            };
            await this.setLogsCloseOrders(logInsert);
            
          } else {
            response='';
            if (!paymentMethodSatisfied)
              response = 'Order payment method is not available for autofund. ';
            if (!orderStatusSatisfied)
              response += 'For current order status of this order, autofund is not possible. ';
            // if (!autoFundDateSatisfied  && paymentMethod ===  Constants.PAYMENT_METHOD_BRYZOS_PAY)
            //   response += 'Order auto payment date is not up to date. ';
            if (!claimedBySatisfied)
              response += 'Order is not claimed. ';
            if (!paymentProcessingStatusSatisfied)
              response += 'Order is already in processing state. ';
            if (!orderCloseStatus)
              response += 'Close Order is already in process. ';
         
            let responseObj = {[responseErrorTag]: response};
            
            const logInsert = {
              po_number: poNumber,
              purchase_order_line_id: purchaseOrderLineId,
              order_price: orderPrice,
              response: response,
              admin_user_id: adminId
            };
            
            await this.setLogsCloseOrders(logInsert);
            return responseObj;
          }
        }else if(orderLineData  && orderLineData.status === Constants.IN_PROGRESS){
          response = {
            [responseErrorTag]: 'Close Order is already in process.'
          };
          return response;
        }
      }
    } else {
      response = {
        [responseErrorTag]: `Order : ${poNumber} does not exist`
      };
      return response;
    }
    
    return response;
  }

  async setLogsCloseOrders(insert){
    await this.dbServiceObj.saveOrUpdateReferenceData(insert,this.adminLogCloseOrdersRepository);
  }

  async getAllBuyerOpenOrder() {
  
    const activeStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ACTIVE);
    const resolvedStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.RESOLVED);
    const completedStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDER_STATUS_COMPLETED);

    let orderStatus = [activeStatusId.id, completedStatus.id, resolvedStatusId.id];

    const mapperFields = { selectFields:[
      'table1.buyer_po_number AS buyer_po_number',
      'payment_method',
      'CASE WHEN actual_buyer_po_price IS NOT NULL THEN actual_buyer_po_price ELSE buyer_po_price END AS po_buyer_po_price',
      'table1.sales_tax AS sales_tax',
      'CASE WHEN table1.actual_buyer_po_price IS NOT NULL THEN table1.actual_buyer_po_price + table1.sales_tax ELSE table1.buyer_po_price + table1.sales_tax END AS total_po_price',
      'table1.buyer_internal_po as po_buyer_internal_po',
      'user_purchase_order_line.id as purchase_order_line_id',
      'user_purchase_order_line.po_line as po_line',
      'description',
      'buyer_company_name'
    ] };


    const conditions = [
      { column: 'is_buyer_order_open', operator: '=', value: true, table: 'user_purchase_order_line', },
      { column: 'order_status_id', operator: 'IN', value: orderStatus, table: 'user_purchase_order_line', },
      { column: 'payment_method', operator: '=', value: Constants.PAYMENT_METHOD_BRYZOS_PAY },
      { column: 'is_active', operator: '=', value: true, table: 'user_purchase_order_line',},
      { column: 'is_active', operator: '=', value: true },

    ];

    const leftJoins = [
      { table: 'user_purchase_order_line', joinColumn: 'purchase_order_id', mainTableColumn: 'id' },
    ];

    const orderBy = {"table1.created_date":"DESC","user_purchase_order_line.po_line":"ASC"};
    const purchaseOrders = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository, leftJoins, conditions, mapperFields, orderBy);

    const buyerOrders = [];
    const poNumbers = [];
    const lineData={};
    let i = 0;

    for(const purchaseOrder of purchaseOrders){
      
      const orderData={};
      orderData["purchase_order_line"]= [];
      const lineDescription={};

      lineDescription["id"]=purchaseOrder.purchase_order_line_id;
      lineDescription["po_line"]=purchaseOrder.po_line;
      lineDescription["description"]=purchaseOrder.description;
      
      if(!poNumbers.includes(purchaseOrder.buyer_po_number)){
        poNumbers.push(purchaseOrder.buyer_po_number);
        lineData[purchaseOrder.buyer_po_number] = [];
        orderData["po_payment_method"] = purchaseOrder.payment_method;
        orderData["po_sales_tax"] = purchaseOrder.sales_tax;
        orderData["po_buyer_internal_po"] = purchaseOrder.po_buyer_internal_po;
        orderData["po_buyer_po_number"] = purchaseOrder.buyer_po_number;
        orderData["po_buyer_po_price"] = purchaseOrder.po_buyer_po_price;
        orderData["total_po_price"] = purchaseOrder.total_po_price;
        orderData["buyer_company_name"] = purchaseOrder.buyer_company_name;
        orderData["purchase_order_line"] = [];
      }

      lineData[purchaseOrder.buyer_po_number].push(lineDescription);

      if(orderData.hasOwnProperty("po_buyer_po_number")){
        buyerOrders.push(orderData);
      }

    }

    const dataArray = [];
    for(const buyerOrder of buyerOrders){
      const finalObjects = {};
      finalObjects["po_payment_method"] = buyerOrder.po_payment_method;
      finalObjects["po_sales_tax"] = buyerOrder.po_sales_tax;
      finalObjects["po_buyer_internal_po"] = buyerOrder.po_buyer_internal_po;
      finalObjects["po_buyer_po_number"] = buyerOrder.po_buyer_po_number;
      finalObjects["po_buyer_po_price"] = buyerOrder.po_buyer_po_price;
      finalObjects["total_po_price"] = Number(buyerOrder.total_po_price);
      finalObjects["buyer_company_name"] = buyerOrder.buyer_company_name; // Added this line
      finalObjects["purchase_order_line"] = lineData[buyerOrder.po_buyer_po_number];
      
      dataArray.push(finalObjects);
    }
    return dataArray;
  }

  async closeBuyerorder(adminId, closeOrderDto: CloseOrderDto)
  { 
    let response = null;
    let poNumber = closeOrderDto.po_number;
    let purchaseOrderLineIds=null;
    let purchaseOrderLineId=null;
    let orderLineData=null;
    let orderPrice=null;
    let poLine=null;

    const orderData = await this.dbServiceObj.findOne(this.userPurchaseOrderRepository,'buyer_po_number',poNumber);

    const activeStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ACTIVE);
    const resolvedStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.RESOLVED);
    const completedStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDER_STATUS_COMPLETED);
    const cancelStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCANCELLED);

    if (orderData && closeOrderDto.hasOwnProperty('purchase_order_line_id') && closeOrderDto.purchase_order_line_id !== undefined && closeOrderDto.purchase_order_line_id !== null && Object.keys(closeOrderDto.purchase_order_line_id).length > 0) 
    {
      purchaseOrderLineIds=closeOrderDto.purchase_order_line_id;
      let paymentMethod  = orderData.payment_method;

      for (const OrderLineId of Object.values(purchaseOrderLineIds)) 
      {
        purchaseOrderLineId = OrderLineId.toString();

        orderLineData = await this.dbServiceObj.findOne(this.userPurchaseOrderLineRepository,'id',purchaseOrderLineId);

        if(orderLineData)
        {
          poLine = orderLineData.po_line;

          // orderPrice=orderLineData.actual_buyer_line_total;

          let sales_tax = (orderLineData.sales_tax != null && orderLineData.sales_tax != undefined && !isNaN( orderLineData.sales_tax))? parseFloat(orderLineData.sales_tax) : 0; 

          orderPrice= parseFloat(orderLineData.actual_buyer_line_total) + sales_tax;

          let claimedBySatisfied = null;
          let referenceId = purchaseOrderLineId;

          const paymentMethodSatisfied = [Constants.PAYMENT_METHOD_BRYZOS_PAY].includes(orderData.payment_method);
          const orderStatusSatisfied = [activeStatusId.id, resolvedStatusId.id, completedStatus.id].includes(orderLineData.order_status_id);
          const orderCloseStatus = orderLineData.is_buyer_order_open == 1? true: false;

          if (orderData.claimed_by === null || orderData.claimed_by === Constants.CLAMIED_BY_READY_TO_CLAIM || orderData.claimed_by === Constants.CLAMIED_BY_PENDING_STATUS || !orderData.claimed_by.includes('@')) {
            claimedBySatisfied = false ;
          } else if (orderData.claimed_by.includes('@')) {
            claimedBySatisfied = true; 
          }
     
          if (paymentMethodSatisfied && orderStatusSatisfied  && claimedBySatisfied && orderCloseStatus ) {
            let updateOrderLine = await this.dbServiceObj.updateWithoutMapper({ 'is_buyer_order_open': false}, 'id', referenceId, this.userPurchaseOrderLineRepository);

            if(updateOrderLine) {
              let buyerPayment = await this.getBuyerPaidData(referenceId);

              if (Number(buyerPayment) > 0) {

                await this.dbServiceObj.saveOrUpdateReferenceData({ purchase_order_line_id: referenceId, buyer_payment_type: Constants.PAYMENT_PAID, buyer_paid: buyerPayment},this.userPurchaseOrderLedgerRepository);

                //replenish buyer credit
                if(orderData && orderData.payment_method==Constants.PAYMENT_METHOD_BRYZOS_PAY )
                {  
                  let buyerId = orderLineData.buyer_id;
                  const cbnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository,'user_id',buyerId);
                 
                  if(cbnplData)
                  {
                  
                    let availableBryzosCreditLimit = parseFloat(cbnplData.bryzos_available_credit_limit) + Number(buyerPayment);
                    let outsatndingBryzosCreditLimit = parseFloat(cbnplData.bryzos_credit_limit) - availableBryzosCreditLimit;
            
                    let cbnpl_update_set = {bryzos_available_credit_limit: availableBryzosCreditLimit, bryzos_outstanding_credit_limit: outsatndingBryzosCreditLimit};
                   
                    await this.dbServiceObj.updateByMultipleWhere(cbnpl_update_set, { user_id: buyerId }, this.companyBuyNowPayLaterRepository);

                  }
                  
                }
    
              }
            }
            response = `Order Line ${poLine} Closed for Buyer`;

            const ordersNotCompletedResult = await this.userPurchaseOrderLineRepository
            .createQueryBuilder('order')
            .select('COUNT(order.id)', 'nc_orders')
            .where('order.buyer_po_number = :poNumber', { poNumber: poNumber })
            .andWhere('order.order_status_id NOT IN (:...statusIds)', { statusIds: [cancelStatus.id] })
            .andWhere('order.is_buyer_order_open = true')
            .getRawOne();
      
            const notCompletedOrders = ordersNotCompletedResult ? ordersNotCompletedResult.nc_orders : 0;        
        
            if (Number(notCompletedOrders) === 0) {
              await this.dbServiceObj.updateByMultipleWhere({ is_closed_buyer: true }, { buyer_po_number: poNumber }, this.userPurchaseOrderRepository);
              response += ` & PO ${poNumber} moved to closed orders for Buyer.`;
            }

            const logInsert = {
              po_number: poNumber,
              purchase_order_line_id: purchaseOrderLineId,
              order_price: orderPrice,
              response: response,
              admin_user_id: adminId
            };
    
            await this.setLogsCloseOrders(logInsert);
            
          } else {
            response='';
            if (!paymentMethodSatisfied)
              response = 'Order payment method is not available for buyer close order. ';
            if (!orderStatusSatisfied)
              response += 'For current order status of this order, buyer close order is not possible. ';
            if (!claimedBySatisfied)
              response += 'Order is not claimed. ';
            if (!orderCloseStatus)
              response += 'Buyer Order is already closed. ';
         
            let responseObj = {[responseErrorTag]: response};
            
            const logInsert = {
              po_number: poNumber,
              purchase_order_line_id: purchaseOrderLineId,
              order_price: orderPrice,
              response: response,
              admin_user_id: adminId
            };

            
            await this.setLogsCloseOrders(logInsert);
            return responseObj;
          }
        }
      }
      if(orderData.payment_method===Constants.PAYMENT_METHOD_BRYZOS_PAY )
      {
        let buyerId = orderData.buyer_id;
        await this.setLogsBuyerCredits(buyerId, adminId, 'BUYER_CLOSE_ORDER_AD', poNumber);
      }
    } else {
      response = {
        [responseErrorTag]: `Order : ${poNumber} does not exist`
      };
      return response;
    }
    
    return response;
  }

  async getBuyerPaidData(purchaseOrderLineId){
    let buyerPaidPrice = 0;

    // Get order price data for the buyer
    const orderPriceResult = await this.userPurchaseOrderLedgerRepository
      .createQueryBuilder('ledger')
      .select(
        'IFNULL(SUM(ledger.extended), 0) + IFNULL(SUM(ledger.bryzos_fees), 0) + IFNULL(SUM(ledger.sales_tax), 0)',
        'buyer_paid_price',
      )
      .where('ledger.purchase_order_line_id = :purchaseOrderLineId', { purchaseOrderLineId: purchaseOrderLineId })
      .getRawOne();

    const orderPrice = orderPriceResult ? orderPriceResult.buyer_paid_price : 0;

    const buyerPaymentsResult = await this.userPurchaseOrderLedgerRepository
      .createQueryBuilder('ledger')
      .select('IFNULL(SUM(ledger.buyer_paid), 0)', 'buyer_paid_entry')
      .where('ledger.purchase_order_line_id = :purchaseOrderLineId', { purchaseOrderLineId: purchaseOrderLineId })
      .andWhere('ledger.buyer_payment_type = :paymentType', { paymentType: Constants.PAYMENT_PAID })
      .getRawOne();

    const buyerPaidEntry = buyerPaymentsResult ? buyerPaymentsResult.buyer_paid_entry : 0;

    if (buyerPaidEntry == 0) {
      buyerPaidPrice = orderPrice;
    }

    return buyerPaidPrice;
  }
async setLogsBuyerCredits(buyerId, adminId, type, poNumber){
  const insertCreditLogs = {};
  const cbnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository,'user_id',buyerId);
  if(cbnplData)
  {

    insertCreditLogs["admin_id"] = adminId;
    insertCreditLogs["buyer_id"] = buyerId;
    insertCreditLogs["credit_limit"] = parseFloat(cbnplData?.bryzos_credit_limit);
    insertCreditLogs["available_credit_limit"] = parseFloat(cbnplData?.bryzos_available_credit_limit);
    insertCreditLogs["outstanding_credit_limit"] =  parseFloat(cbnplData.bryzos_credit_limit) - parseFloat(cbnplData.bryzos_available_credit_limit);
    insertCreditLogs["reason"] = type;
    insertCreditLogs["po_number"] = poNumber;
    
    await this.dbServiceObj.saveOrUpdateReferenceData(insertCreditLogs,this.logBryzosCreditLimitRepository);
  }
}

async retrieveOpenAchOrdersFromBuyer() {
  const activeStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus, 'value', Constants.ACTIVE);
  const resolvedStatusId = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus, 'value', Constants.RESOLVED);
  const completedStatus = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus, 'value', Constants.ORDER_STATUS_COMPLETED);

  const orderStatus = [activeStatusId.id, resolvedStatusId.id, completedStatus.id];

  const selectedFields = [
    'table1.buyer_po_number AS buyer_po_number',
    'payment_method',
    'CASE WHEN actual_buyer_po_price IS NOT NULL THEN actual_buyer_po_price ELSE buyer_po_price END AS po_buyer_po_price',
    'table1.sales_tax AS sales_tax',
    'COALESCE(table1.deposit_amount, 0) AS deposit_amount',
    'CASE WHEN actual_buyer_po_price IS NOT NULL THEN actual_buyer_po_price + table1.sales_tax ELSE buyer_po_price + COALESCE(table1.sales_tax, 0) END AS buyer_credit',
    'table1.buyer_internal_po as po_buyer_internal_po',
    'user_purchase_order_line.id as purchase_order_line_id',
    'user_purchase_order_line.po_line as po_line',
    'description',
    'user_purchase_order_line.is_buyer_order_open as is_buyer_order_open',
    'buyer_company_name',

  ];
  

  const leftJoin = [];
  const poConditions = [];

  let table1 = { "table": "user_purchase_order_line", "joinColumn": "purchase_order_id", "mainTableColumn": "id" };

  leftJoin.push(table1);

  let poCondition1 = { "column": "payment_method", "operator": "=", "value": Constants.PAYMENT_METHOD_ACH_CREDIT };
  poConditions.push(poCondition1);
  // let poCondition2 = { "column": "is_buyer_order_open", "operator": "=", "value": true, "table": "user_purchase_order_line" };
  // poConditions.push(poCondition2);
  let poCondition3 = { "column": "is_active", "operator": "=", "value": true, "table": "user_purchase_order_line" };
  poConditions.push(poCondition3);
  let poCondition4 = { "column": "order_status_id", "operator": "IN", "value": orderStatus, "table": "user_purchase_order_line" };
  poConditions.push(poCondition4);
  let poCondition5 = { "column": "is_closed_buyer", "operator": "=", "value": false };
  poConditions.push(poCondition5);
  let poCondition6 = { "column": "is_active", "operator": "=", "value": true };
  poConditions.push(poCondition6);
  let poCondition7 = { "column": "is_ach_po_approved", "operator": "=", "value": true };
  poConditions.push(poCondition7);

  let mapper = {};
  mapper['selectFields'] = selectedFields;
  mapper['dateFields'] = ['table1.created_date', 'table1.time_stamp'];
  const orderBy = { "table1.created_date": "DESC", "user_purchase_order_line.po_line": "ASC" };

  const purchaseOrders = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository, leftJoin, poConditions, mapper, orderBy);

  if(purchaseOrders.length == 0 ){  return { [responseErrorTag]: 'No active ACH orders found' };}

  const purchaseOrderLineIds = purchaseOrders.map((purchaseOrder) => purchaseOrder.purchase_order_line_id);

  // Fetch buyer_paid data for all purchaseOrderLineIds in a single query
  const buyerPaidData = await this.getAchBuyerPaidData(purchaseOrderLineIds);

  const buyerOrders = [];
  const poNumbers = [];
  const lineData = {};
  const orderPaidData = {}; // Store total buyer paid data
  const totalBuyerPaidData = {}; // Store total buyer paid data for each buyer_po_number
  const orderDueData = {};
  const totalBuyerDueData = {};

  for (const purchaseOrder of purchaseOrders) {
    const orderData = {};
    orderData["purchase_order_lines"] = [];
    const lineDescription = {};

    lineDescription["id"] = purchaseOrder.purchase_order_line_id;
    lineDescription["po_line"] = purchaseOrder.po_line;
    lineDescription["description"] = purchaseOrder.description;

    const buyerPaid = buyerPaidData[purchaseOrder.purchase_order_line_id] || {
      buyer_paid_price: 0,
      price: 0,
    };

    const buyerPaidPrice = Number(buyerPaid.buyer_paid_price);
    const price = Number(lineDescription["price"]);
    
    lineDescription["buyer_paid"] = buyerPaidPrice;
    lineDescription["price"] = buyerPaid.price;
    lineDescription["due_amount"] = (lineDescription["price"] - lineDescription["buyer_paid"]).toFixed(4);
    lineDescription["deposit_amount"] = Number(buyerPaid.deposit_amount);

    if (!poNumbers.includes(purchaseOrder.buyer_po_number)) {
      poNumbers.push(purchaseOrder.buyer_po_number);
      lineData[purchaseOrder.buyer_po_number] = [];
      orderPaidData[purchaseOrder.buyer_po_number] = 0;
      orderDueData[purchaseOrder.buyer_po_number] = 0;

      orderData["po_payment_method"] = purchaseOrder.payment_method;
      orderData["po_sales_tax"] = purchaseOrder.sales_tax;
      orderData["po_buyer_internal_po"] = purchaseOrder.po_buyer_internal_po;
      orderData["po_buyer_po_number"] = purchaseOrder.buyer_po_number;
      orderData["po_buyer_company_name"] = purchaseOrder.buyer_company_name;
      orderData["po_buyer_po_price"] = purchaseOrder.po_buyer_po_price;
      orderData["buyer_credit"] = purchaseOrder.buyer_credit;
      orderData["deposit_amount"] = purchaseOrder.deposit_amount;
      orderData["total_buyer_paid"] = 0; // Initialize total_buyer_paid to 0 for each buyer_po_number
      orderData["total_due_amount"] = 0;
      orderData["purchase_order_lines"] = [];
    }

    if(purchaseOrder.is_buyer_order_open == 1){
      lineData[purchaseOrder.buyer_po_number].push(lineDescription);
    }
    orderPaidData[purchaseOrder.buyer_po_number] += Number(lineDescription["buyer_paid"]);
    orderDueData[purchaseOrder.buyer_po_number] += Number(lineDescription["due_amount"]);

    if (orderData.hasOwnProperty("po_buyer_company_name")) {
      buyerOrders.push(orderData);
    }
  }

  for (const buyerOrder of buyerOrders) {
    // Assign total buyer paid data from orderPaidData to totalBuyerPaidData
    totalBuyerPaidData[buyerOrder.po_buyer_po_number] = orderPaidData[buyerOrder.po_buyer_po_number].toFixed(4);
    totalBuyerDueData[buyerOrder.po_buyer_po_number] = orderDueData[buyerOrder.po_buyer_po_number].toFixed(4);
  }

  const dataArray = buyerOrders.map((buyerOrder) => {
    return {
      payment_method: buyerOrder.po_payment_method,
      sales_tax: buyerOrder.po_sales_tax,
      buyer_internal_po: buyerOrder.po_buyer_internal_po,
      buyer_company_name: buyerOrder.po_buyer_company_name,
      buyer_po_number: buyerOrder.po_buyer_po_number,
      buyer_po_price: buyerOrder.po_buyer_po_price,
      deposit_amount: buyerOrder.deposit_amount,
      buyer_credit: Number(buyerOrder.buyer_credit).toFixed(4),
      total_buyer_paid: Number(totalBuyerPaidData[buyerOrder.po_buyer_po_number]).toFixed(4),
      total_due_amount: Number(totalBuyerDueData[buyerOrder.po_buyer_po_number]).toFixed(4),
      purchase_order_lines: lineData[buyerOrder.po_buyer_po_number],
    };
  });

  return dataArray;
}

async getAchBuyerPaidData(purchaseOrderLineIds) {
  // Fetch buyer_paid data for all purchaseOrderLineIds in a single query
  const buyerPaidData = {};

  // Query the database for buyer_paid data
  const paymentType = Constants.PAYMENT_PAID; // Define the paymentType variable
  const queryResult = await this.userPurchaseOrderLedgerRepository  
    .createQueryBuilder()
    .select('purchase_order_line_id')
    .addSelect('(SUM(CASE WHEN buyer_payment_type = :paymentType THEN buyer_paid ELSE 0 END))', 'buyer_paid_price')
    .addSelect('COALESCE(SUM(extended), 0) + COALESCE(SUM(sales_tax), 0)', 'price')
    .addSelect('COALESCE(SUM(deposit_amount), 0)','deposit_amt')
    .where('purchase_order_line_id IN (:...purchaseOrderLineIds)', { purchaseOrderLineIds: purchaseOrderLineIds })
    .setParameter('paymentType', paymentType) // Pass the paymentType as a parameter
    .groupBy('purchase_order_line_id')
    .getRawMany();
  
  // Convert the query result to a dictionary with purchaseOrderLineId as the key
  for (const result of queryResult) {
    const purchaseOrderLineId = result.purchase_order_line_id;
    buyerPaidData[purchaseOrderLineId] = {
      buyer_paid_price: parseFloat(result.buyer_paid_price) || 0,
      price: parseFloat(result.price) || 0,
      deposit_amount: parseFloat(result.deposit_amt) || 0,
    };
  }
  
  return buyerPaidData;
}

async closeBuyerAchOrder(adminId, closeOrderDto: CloseAchOrderDto) {
  let response = null;
  let poNumber = closeOrderDto.po_number;
  let purchaseOrderLineIds = null;
  let purchaseOrderLineId = null;
  let orderLineData = null;
  let orderPrice = null;
  let poLine = null;

  const orderData = await this.dbServiceObj.findOne( this.userPurchaseOrderRepository, 'buyer_po_number', poNumber);

  const activeStatusId = await this.dbServiceObj.findOne( this.userReferenceDataOrderStatus, 'value', Constants.ACTIVE);
  const resolvedStatusId = await this.dbServiceObj.findOne( this.userReferenceDataOrderStatus, 'value', Constants.RESOLVED);
  const completedStatus = await this.dbServiceObj.findOne( this.userReferenceDataOrderStatus, 'value', Constants.ORDER_STATUS_COMPLETED);
  const cancelStatus = await this.dbServiceObj.findOne( this.userReferenceDataOrderStatus, 'value', Constants.ORDERCANCELLED);

  if (orderData && orderData.payment_method === Constants.PAYMENT_METHOD_ACH_CREDIT) {
    if (orderData.is_ach_po_approved === null) {
      return {
        [responseErrorTag]: `Order : ${poNumber} is not approved`,
      };
    }
  }

  if ( orderData && closeOrderDto.hasOwnProperty('purchase_order_line') && closeOrderDto.purchase_order_line !== undefined && closeOrderDto.purchase_order_line !== null && closeOrderDto.purchase_order_line.length > 0 ) 
  {
    purchaseOrderLineIds = closeOrderDto.purchase_order_line;

    for (const OrderLineId of purchaseOrderLineIds) {
      purchaseOrderLineId = OrderLineId.id;
      const buyerPaidAmount = OrderLineId.buyer_paid_amt;

      orderLineData = await this.dbServiceObj.findOne( this.userPurchaseOrderLineRepository, 'id', purchaseOrderLineId );
      if (orderLineData) {
        let buyerId=orderLineData.buyer_id;
        const qty_unit=orderLineData.qty_unit;

        poLine = orderLineData.po_line;
        let buyerPriceData = await this.getAchBuyerPaidData( [purchaseOrderLineId] );

        const buyerPrices = buyerPriceData[orderLineData.id] || {
          buyer_paid_price: 0,
          price: 0,
          deposit_amount: 0,
        };
        orderPrice = buyerPrices.price;
        orderPrice = Number(Number(orderPrice).toFixed(4));
        let buyerPaidPrice = buyerPrices.buyer_paid_price;
        let dueAmount = Number(orderPrice) - Number(buyerPaidPrice);
        let dueAmt = qty_unit=='Lb'? Number(dueAmount).toFixed(4) : Number(dueAmount).toFixed(2);
        const buyerPaidAmt = Number(buyerPaidAmount).toFixed(4);
        let depositAmount = Number(Number(buyerPrices.deposit_amount).toFixed(4));

        if (Number(dueAmt) < Number(buyerPaidAmt)) {
          let response = `Amount you entered exceeds the order line price for line ${poLine}!!`;
          let responseObj = { [responseErrorTag]: response };
          return responseObj;
        }
        let referenceId = purchaseOrderLineId;

        const paymentMethodSatisfied = [ Constants.PAYMENT_METHOD_ACH_CREDIT, ].includes(orderData.payment_method);
        const orderStatusSatisfied = [ activeStatusId.id, resolvedStatusId.id, completedStatus.id, ].includes(orderLineData.order_status_id);
        const orderCloseStatus = orderLineData.is_buyer_order_open == 1 ? true : false;
        if ( paymentMethodSatisfied && orderStatusSatisfied && orderCloseStatus ) 
        {
          if (Number(buyerPaidAmt) > 0) {
            await this.dbServiceObj.saveOrUpdateReferenceData( { user_id: buyerId, purchase_order_line_id: referenceId, buyer_payment_type: Constants.PAYMENT_PAID, buyer_paid: buyerPaidAmt, }, this.userPurchaseOrderLedgerRepository);
          }

          response = `Amount Paid Successfully!`;

          let totalPaidPrice = Number(buyerPaidPrice) + Number(buyerPaidAmt);
    
          let totalPaidAmt = Number(totalPaidPrice).toFixed(4);

          if (Number(totalPaidAmt) === Number(orderPrice)) {
            depositAmount = Number(depositAmount) * (-1);

            let updateOrderLine = await this.dbServiceObj.updateWithoutMapper( { is_buyer_order_open: false }, 'id', referenceId, this.userPurchaseOrderLineRepository);
           
            await this.dbServiceObj.saveOrUpdateReferenceData( { user_id: buyerId, purchase_order_line_id: referenceId, buyer_payment_type: Constants.PAYMENT_PAID, deposit_amount: depositAmount }, this.userPurchaseOrderLedgerRepository);

            if (updateOrderLine) {
              response = `Order Line ${poLine} Closed for Buyer`;
            }
          }

          const ordersNotCompletedResult =
            await this.userPurchaseOrderLineRepository
              .createQueryBuilder('order')
              .select('COUNT(order.id)', 'nc_orders')
              .where('order.buyer_po_number = :poNumber', {
                poNumber: poNumber,
              })
              .andWhere('order.order_status_id NOT IN (:...statusIds)', {
                statusIds: [cancelStatus.id],
              })
              .andWhere('order.is_buyer_order_open = true')
              .getRawOne();

          const notCompletedOrders = ordersNotCompletedResult ? ordersNotCompletedResult.nc_orders : 0;
          if (Number(notCompletedOrders) === 0) {
            await this.dbServiceObj.updateByMultipleWhere( { is_closed_buyer: true }, { buyer_po_number: poNumber }, this.userPurchaseOrderRepository);
            response += ` & PO ${poNumber} moved to closed orders for Buyer.`;
          }

          const logInsert = {
            po_number: poNumber,
            purchase_order_line_id: purchaseOrderLineId,
            order_price: Number(buyerPaidAmt),
            response: response,
            admin_user_id: adminId,
          };
          await this.setLogsCloseOrders(logInsert);
        } else {
          response = '';
          if (!paymentMethodSatisfied)
            response =
              'Order payment method is not available for buyer close order. ';
          if (!orderStatusSatisfied)
            response +=
              'For current order status of this order, buyer close order is not possible. ';
          if (!orderCloseStatus)
            response += 'Buyer Order is already closed. ';

          let responseObj = { [responseErrorTag]: response };

          const logInsert = {
            po_number: poNumber,
            purchase_order_line_id: purchaseOrderLineId,
            order_price: orderPrice,
            response: response,
            admin_user_id: adminId,
          };

          await this.setLogsCloseOrders(logInsert);
          return responseObj;
        }
      }
    }
  } else {
    response = {
      [responseErrorTag]: `Order : ${poNumber} does not exist`,
    };
    return response;
  }

  return response;
}

}


  
