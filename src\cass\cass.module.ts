import { MiddlewareConsumer, Module } from '@nestjs/common';
import { Cass<PERSON>ontroller } from './cass.controller';
import { AdminPermissionMiddleware, } from '@bryzos/base-library';
// import { LoggerMiddleware } from '../middleware/logger.middleware';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({ 
//   imports : [BaseLibraryModule,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [CassController],
//   providers: OConstants.ServiceArray,

//   exports: [CassService],
// })

@Module({
  imports: [SharedModule],
  controllers: [CassController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class CassModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
    .apply(AdminPermissionMiddleware)
    .forRoutes('/cass');
    consumer
    .apply(LoggerMiddleware)
    .exclude('/cass/transactionData', '/cass/adhoc/getCassSellerSetup', '/cass/getProbablePo', '/cass/getPos', '/cass/getFileData', '/cass/getMappedPo') // exclude get api
    .forRoutes('/cass');
  }
}
