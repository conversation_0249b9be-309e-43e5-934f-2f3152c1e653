import { Body, Controller, DefaultValuePipe, Get, ParseBoolPipe, ParseIntPipe, Post, Query, Req, Res, UsePipes, ValidationPipe } from '@nestjs/common';
import { CassService } from './cass.service';
import { Constants } from '../Constants';
import { GetSellerSetupDto, SaveCassDto, SaveCassTransactionDto, SaveAdhocCassSellerSetupDto, SaveMapTransactionToPoDto, MapTransactionToPoDto, GetDateRangeTransactionDto } from './dto/cass.dto';

const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;
const paginationResponseTag = "meta";

@Controller('cass')
@UsePipes(ValidationPipe)
export class CassController {
  constructor(private readonly cassService: CassService) { }

  @Get('/transactionData')
  async getCassTransaction(@Res() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number = 0, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number = 0, @Query('search') search: string = "") {
      let data = await this.cassService.getCassTransactionData({ page, limit, search });
      let meta;

      if (data?.items) {
        meta = data.meta;
        data = data.items;
      }
      let responseData = {
        [responseTag]: data,
        [Constants.PAGINATION_RESPONSE_TAG]: meta
      };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/cassTransaction')
  async saveCassTransaction(@Res() res, @Body() payload: SaveCassDto) {
    let userId = res.locals.authorizedUserId;
    const payloadData = payload[payloadTag]
    let responseData = {
      [responseTag]: await this.cassService.saveCassTransaction(payloadData, userId),
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/getSellerSetup')
  async getSellerSetup( @Body() payload: GetSellerSetupDto, @Res() res) {
    const payloadData = payload[payloadTag]
    const responseData = {
      [responseTag]: await this.cassService.getSellerPaymentSetup(payloadData)
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/adhoc/getCassSellerSetup')
  async getCassSellerSetup(@Res() res) {
    const responseData = {
      [responseTag]: await this.cassService.getCassSellerSetup()
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/adhoc/cassTransaction')
  async cassTransaction( @Body() payload: SaveCassTransactionDto, @Res() res) {
    let userId = res.locals.authorizedUserId;
    const payloadData = payload[payloadTag]
    const responseData = {
      [responseTag]: await this.cassService.createCassTransaction(payloadData, userId)
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/adhoc/sellerSetup')
  async cassAdhocSellerSetup( @Body() payload: SaveAdhocCassSellerSetupDto, @Res() res) {
    let userId = res.locals.authorizedUserId;
    const payloadData = payload[payloadTag]
    const responseData = {
      [responseTag]: await this.cassService.cassAdhocSellerSetup(payloadData, userId)
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getProbablePo')
  async getProbablePos(
    @Res() res,
    @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number,
    @Query('search', new DefaultValuePipe("")) search: string,
    @Query('showMatchedTransaction', new DefaultValuePipe(false), ParseBoolPipe) showMatchedTransaction: boolean
  ) {
    let data: any = await this.cassService.getProbablePos({ page, limit, search }, showMatchedTransaction)
    let meta;

    if (data?.items) {
      meta = data.meta;
      data = data.items;
    }

    const responseData = {
      [responseTag]: data,
      [paginationResponseTag]: meta 
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getPos')
  async getPurchaseOrders( @Res() res) {
    const responseData = {
      [responseTag]: await this.cassService.getPurchaseOrders()
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/mapTransactionToPo')
  @UsePipes(ValidationPipe)
  async associateTransactionPo( @Body() saveMapTransactionToPoDto: SaveMapTransactionToPoDto, @Res() res )
  {
    let adminId = res.locals.authorizedUserId;

    let payloadData = saveMapTransactionToPoDto[payloadTag];

    let responseData = {
      [responseTag] : await this.cassService.associateTransactionPo(payloadData, adminId)
    };
    
    res.locals.responseData = responseData; 
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getFileData')
  async retrieveFundingReceivedFile(@Query('cass_filename') cass_filename: string, @Res() res) {
    let responseData = {
      [responseTag]: await this.cassService.retrieveFundingReceivedFile(cass_filename),
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  //Not using this Api now 
  @Get('/getMappedPo')
  async getMappedPo(@Query('searchString') searchString: string, @Res() res) {
    let responseData = {
      [responseTag]: await this.cassService.getMappedPo(searchString),
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  //Not going to use this Api as we are not providing mapped company name now.
  @Get('/getMappedCompanies')
  async getMappedCompanies(@Req() req, @Res() res) {
    let accessToken = req.headers['accesstoken'];

    let responseData = {
      [responseTag]: await this.cassService.getMappedCompanies(accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/get-transaction-response')
  @UsePipes(ValidationPipe)
  async retrieveTransactionsForPeriod( @Body() retrieveTransactionDto: GetDateRangeTransactionDto, @Res() res )
  {
    let adminId = res.locals.authorizedUserId;
    let payloadData = retrieveTransactionDto[payloadTag];
    let responseData = {
      [responseTag] : await this.cassService.retrieveTransactionsForPeriod(payloadData)
    };
    res.locals.responseData = responseData; 
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
