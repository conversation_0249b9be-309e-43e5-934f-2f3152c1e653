import { Injectable } from '@nestjs/common';
import { DataBaseService } from '@bryzos/base-library';
import { UserAchCredit } from '@bryzos/extended-widget-library';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class UserAchCreditService {
    private readonly dbServiceObj = new DataBaseService;
    constructor(
        @InjectRepository(UserAchCredit)
        private readonly userAchCreditRepository: Repository<UserAchCredit>,
    ) { }

    async getAchCreditList() {
        const leftJoins = [
            { table: 'user', joinColumn: 'id', mainTableColumn: 'user_id' },
        ];
        const conditions = [
            { column: 'is_active', operator: '=', value: true },
            { column: 'is_active', operator: '=', value: true, table: 'user' },
        ];
        const mapperFields = {
            selectFields: [
                'table1.id AS id',
                'table1.user_id AS user_id',
                'table1.bank_name AS bank_name',
                'table1.is_approved AS is_approved',
                'table1.routing_number AS routing_number',
                'table1.account_number AS account_number',
                'CONCAT(user.first_name, " ", user.last_name) AS full_name',
                'user.email_id AS email_id',
                'table1.is_active AS is_active',
            ],
        };
        const orderBy = { 'table1.created_date': 'DESC' };
        const achCreditList = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userAchCreditRepository, leftJoins, conditions, mapperFields, orderBy);

        return achCreditList;
    }
}
