import { Test, TestingModule } from '@nestjs/testing';
import { WidgetAdminDashboardController } from './widget-admin-dashboard.controller';
import { WidgetAdminDashboardService } from './widget-admin-dashboard.service';

describe('WidgetAdminDashboardController', () => {
  let controller: WidgetAdminDashboardController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WidgetAdminDashboardController],
      providers: [WidgetAdminDashboardService],
    }).compile();

    controller = module.get<WidgetAdminDashboardController>(WidgetAdminDashboardController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
