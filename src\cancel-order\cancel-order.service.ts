import { DataBaseService, ReferenceDataSettings, BaseLibraryService } from '@bryzos/base-library';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Constants } from 'src/Constants';
import { CancelOrderSettingDto, CancelOrderDto } from './dto/cancel-order.dto';
import { UserPurchaseOrder, ReferenceDataOrderStatus, UserPurchaseOrderLine, UserPurchaseOrderLedger, CancelOrderLogs, CompanyBuyNowPayLater, LogBryzosCreditLimit, UserResaleCertificate, TaxExemptedPurchaseOrders, SellerInvoiceDump } from '@bryzos/extended-widget-library';
import { Balance } from 'src/Balance';
import { AwsQueue } from 'src/AwsQueue';
const responseErrorTag = Constants.ERROR_TAG;

@Injectable()
export class CancelOrderService {
  private dbServiceObj = new DataBaseService();

  constructor(
    private readonly balance:Balance,
    private readonly awsQueue:AwsQueue,
    private readonly baseLibraryService : BaseLibraryService,

    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly userReferenceDataOrderStatus: Repository<ReferenceDataOrderStatus>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(UserPurchaseOrderLedger) private readonly userPurchaseOrderLedgerRepository: Repository<UserPurchaseOrderLedger>,
    @InjectRepository(CancelOrderLogs) private readonly cancelOrderLogsRepository: Repository<CancelOrderLogs>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(LogBryzosCreditLimit) private readonly logBryzosCreditLimitRepository: Repository<LogBryzosCreditLimit>,
    @InjectRepository(UserResaleCertificate) private readonly userReasaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(TaxExemptedPurchaseOrders) private readonly taxExemptedPoRepository: Repository<TaxExemptedPurchaseOrders>,
    @InjectRepository(SellerInvoiceDump) private readonly sellerInvoiceDumpRepository: Repository<SellerInvoiceDump>,


  ) {}

  async getCancelOrderSettings() {
    let response = {
      order_cancellation_reminder: '',
      order_cancellation_hours: ''
    };
 
    let referenceCancelOrderSetting = await this.dbServiceObj.findAll(this.referenceDataSettingsRepository);
    if (referenceCancelOrderSetting.length > 0) {

      const filteredSettings = referenceCancelOrderSetting.filter(setting => setting.name === Constants.ORDER_CANCELLATION_REMINDER_HOURS || setting.name === Constants.ORDER_CANCELLATION_HOURS);

      filteredSettings.forEach(setting => {
        if (setting.name === Constants.ORDER_CANCELLATION_REMINDER_HOURS) {
          response.order_cancellation_reminder = setting.value;
        } else if (setting.name === Constants.ORDER_CANCELLATION_HOURS) {
          response.order_cancellation_hours = setting.value;
        }
      });
    }
    return response;
  }

  async saveCancelOrderSetting(adminId, cancelOrderSettingDto: CancelOrderSettingDto)
  {
    let response = null;
    let order_cancellation_reminder=cancelOrderSettingDto.order_cancellation_reminder;
    let order_cancellation_hours=cancelOrderSettingDto.order_cancellation_hours;

    let referenceCancelOrderSetting = await this.dbServiceObj.findAll(this.referenceDataSettingsRepository);

    const filteredSettings = referenceCancelOrderSetting.filter(setting => setting.name === Constants.ORDER_CANCELLATION_REMINDER_HOURS || setting.name === Constants.ORDER_CANCELLATION_HOURS);

    for (let i = 0; i < filteredSettings.length; i++) {
      const setting = filteredSettings[i];

      if (order_cancellation_reminder != undefined && setting.name === Constants.ORDER_CANCELLATION_REMINDER_HOURS) {
        await this.dbServiceObj.updateWithoutMapper({ 'value': order_cancellation_reminder}, 'name', Constants.ORDER_CANCELLATION_REMINDER_HOURS, this.referenceDataSettingsRepository);
      } else if (order_cancellation_hours != undefined && setting.name === Constants.ORDER_CANCELLATION_HOURS && setting.value != order_cancellation_hours) {
        await this.dbServiceObj.updateWithoutMapper({ 'value': order_cancellation_hours}, 'name', Constants.ORDER_CANCELLATION_HOURS, this.referenceDataSettingsRepository);
      }
      response = "Successful";
    }
    return response;
  }
  
  async getOrderToCancel(options: { page: number, limit: number, search: string }) {
    let response = null;
  
    const paginationApplied = options.page > 0 && options.limit > 0;
    let meta;

    const activeOrderId = (await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus, 'value', Constants.ACTIVE))?.id;
    const resolvedOrderId = (await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus, 'value', Constants.RESOLVED))?.id;
    if (!activeOrderId || !resolvedOrderId) {
      return { [responseErrorTag]: 'Something went wrong!' };
    }

    let orderData = null;
    const leftJoins = [
      { "table": "user_purchase_order_line", "joinColumn": "buyer_po_number", "mainTableColumn": "buyer_po_number" },
    ];
    const conditions = [
      { column: 'is_active', operator: '=', value: true },
      { column: ['freight_term', 'buyer_internal_po', 'buyer_po_number', 'buyer_po_price', 'buyer_po_price', 'actual_buyer_po_price', 'sales_tax'], operator: 'LIKE', value: options.search },
      { column: 'is_active', operator: '=', value: true, table: "user_purchase_order_line" },
      { column: 'order_status_id', operator: 'IN', value: [activeOrderId, resolvedOrderId], table: "user_purchase_order_line" },
      { column: 'payment_processing_status', operator: 'NULL', value: null, table: "user_purchase_order_line" },
    ];
    const mapperFields = {
      'selectFields': [
        'table1.id AS id',
        'table1.buyer_company_name AS buyer_company_name',
        'table1.buyer_id AS buyer_id',
        'table1.seller_id AS seller_id',
        'table1.buyer_po_number AS buyer_po_number',
        'table1.freight_term AS freight_term',
        'table1.payment_method AS payment_method',
        'table1.sales_tax AS sales_tax',
        'table1.buyer_internal_po AS buyer_internal_po',
        'table1.claimed_by AS claimed_by',
        'table1.is_closed_buyer AS is_closed_buyer',
        'table1.actual_buyer_po_price AS actual_buyer_po_price',
        'table1.buyer_po_price AS buyer_po_price',
        'table1.actual_seller_po_price AS actual_seller_po_price',
        'table1.seller_po_price AS seller_po_price',
      ],
    };
    const orderBy = { 'table1.created_date': 'DESC' };
    const groupBy =  'table1.buyer_po_number' ;
    orderData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository, leftJoins, conditions, mapperFields, orderBy, groupBy, options);

    if(orderData === null) {
      return {[responseErrorTag]: 'Something went wrong!'};
    }

    if (paginationApplied) {
      if (Array.isArray(orderData.items) && orderData.items.length < 1) {
        return [];
      } else {
        meta = orderData.meta;
        orderData = orderData.items;
        response = orderData;
      }
    } else {
      if (Array.isArray(orderData) && orderData.length < 1) {
        return [];
      } else {
        response = orderData;
      }
    }

    // Extract buyer_po_numbers from orderData
    const buyerPoNumbers = orderData.map((order) => order.buyer_po_number);

    // Fetch order line data based on extracted buyer_po_numbers
    const orderLineConditions = [
      { column: 'is_active', operator: '=', value: true },
      { column: 'buyer_po_number', operator: 'IN', value: buyerPoNumbers },
      { column: 'order_status_id', operator: 'IN', value: [activeOrderId, resolvedOrderId] },
      { column: 'payment_processing_status', operator: 'NULL', value: null },
    ];

    let orderLineData = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderLineRepository, null, orderLineConditions);
    if (orderData.length <= 0) {
      return {[responseErrorTag]: 'No data found!!'};
    }
    // Create a map to track buyer_po_number with the condition met
    const buyerPoWithConditionMet = new Set(
      orderLineData
        .filter(orderLine => orderLine.is_buyer_order_open === 1 && [1, 10].includes(orderLine.order_status_id))
        .map((orderLine) => orderLine.buyer_po_number)
    );

    // Construct the response without modifying the original data
    response = response.map((order) => {
      const showCancelBtn = buyerPoWithConditionMet.has(order.buyer_po_number);
      
      const orderLines = orderLineData.filter((orderLine) => orderLine.buyer_po_number === order.buyer_po_number);
      
      const purchase_order_lines = orderLines.map((orderLine) => ({
        "po_line": orderLine.po_line,
        "purchase_order_line_id": orderLine.id,
        "status": orderLine?.status || null,
        "description": orderLine.description,
        "is_buyer_order_open": orderLine.is_buyer_order_open,
      }));

      // Sort the purchase_order_lines array in ascending order based on the po_line property
      purchase_order_lines.sort((a, b) => a.po_line - b.po_line);

      if (order.seller_id) {
        order.is_claimed = true;
      } else {
        order.is_claimed = false;
      }

      return {
        id: order.id,
        buyer_id: order.buyer_id,
        seller_id: order.seller_id,
        buyer_po_number: order.buyer_po_number,
        freight_term: order.freight_term,
        payment_method: order.payment_method,
        sales_tax: order.sales_tax,
        buyer_internal_po: order.buyer_internal_po,
        buyer_total_price: order?.actual_buyer_po_price || order.buyer_po_price,
        seller_total_price: order?.actual_seller_po_price || order.seller_po_price,
        is_claimed: order.is_claimed,
        is_closed_buyer: order.is_closed_buyer,
        seller_funding_date: orderLines[0]?.seller_funding_date || null,
        order_status_id: orderLines[0]?.order_status_id,
        buyer_company_name: order.buyer_company_name,
        show_cancel_btn: showCancelBtn,
        purchase_order_lines: purchase_order_lines
      };
    }).filter((order) => order !== null);

    if (paginationApplied) {
      return { items: response, meta: meta };
    } else {
      return response;
    }
  }
  
  async cancelOrderDataByAdmin(adminId, cancelOrderDto: CancelOrderDto)
  { 
    let response = null;
    let poNumber = cancelOrderDto.po_number;
    const orderData = await this.dbServiceObj.findOne(this.userPurchaseOrderRepository,'buyer_po_number',poNumber);

    let cancelOrder = await this.cancelOrder(orderData, adminId, cancelOrderDto, Constants.SUPER_ADMIN_USER);
    if (cancelOrder && cancelOrder.hasOwnProperty('error_message')) {
      return cancelOrder;
    } else {
      response = 'Order Cancel Successful';
    }

    return response;
  }

  async cancelOrderByUser(userId, cancelOrderDto: CancelOrderDto, superAdminId)
  { 
    let response = null;
    let poNumber = cancelOrderDto.po_number;
    let type = cancelOrderDto.type;
    const totalOrderLines = await this.userPurchaseOrderLineRepository
    .createQueryBuilder('user_purchase_order_line')
    .select(['user_purchase_order_line.id', 'user_purchase_order_line.po_line'])
    .where('user_purchase_order_line.buyer_po_number = :poNumber', { poNumber })
    .getMany();

    const purchaseOrderLineMap = {};
    totalOrderLines.forEach((line) => {
      purchaseOrderLineMap[line.po_line] = line.id;
    });

    cancelOrderDto.purchase_order_line_id = purchaseOrderLineMap;
  
    const orderData = await this.dbServiceObj.findOne(this.userPurchaseOrderRepository,'buyer_po_number',poNumber);
    if(orderData != undefined) {
      if((type == Constants.BUYER_ORDER_CANCEL && orderData.buyer_id != userId) || (type == Constants.SELLER_ORDER_CANCEL && orderData.seller_id != userId)) return {"error_message":"You can't cancel this order"} 
      
      let cancelOrder = await this.cancelOrder(orderData, userId, cancelOrderDto, Constants.USER, superAdminId);
      if (cancelOrder && cancelOrder.hasOwnProperty('err_message')) {
        return cancelOrder;
      } else {
        response = 'Order Cancel Successful';
      }
    }
    return response;
  }

  async cancelOrder(orderData, userId, cancelOrderDto, actionPerfomedBy, superAdminId?: string)
  {
    let response = null;
    let poNumber = cancelOrderDto.po_number;
    let type = cancelOrderDto.type;
    let purchaseOrderLineIds = null;
    let purchaseOrderLineId =null;
    let poLevel = true;

    if (cancelOrderDto.hasOwnProperty('purchase_order_line_id') && cancelOrderDto.purchase_order_line_id !== undefined && cancelOrderDto.purchase_order_line_id !== null && Object.keys(cancelOrderDto.purchase_order_line_id).length > 0) 
    {
      purchaseOrderLineIds=cancelOrderDto.purchase_order_line_id;
      let order_cancel = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCANCELLED);
      let sellerEvent = Constants.NOTIFICATION_SELLER_ORDER_CANCEL;
      let buyerCancelOrderEvent = Constants.NOTIFICATION_BUYER_ORDER_CANCEL;
      if(order_cancel)
      {
        let orderStatusCancelled = order_cancel.id;

        const cancelEntireOrder = Object.keys(cancelOrderDto.purchase_order_line_id).length;
        const totalOrderLines = await this.userPurchaseOrderLineRepository
        .createQueryBuilder('user_purchase_order_line')
        .where('user_purchase_order_line.buyer_po_number = :poNumber', { poNumber })
        .getCount();
        if(totalOrderLines != cancelEntireOrder ){
          sellerEvent = Constants.NOTIFICATION_SELLER_ORDER_LINE_CANCEL;
          buyerCancelOrderEvent = Constants.NOTIFICATION_BUYER_ORDER_LINE_CANCEL;
          poLevel = false;
        }

        // if seller order canceled on PO level
        if(type === Constants.SELLER_ORDER_CANCEL && totalOrderLines === cancelEntireOrder )
        {
          const orderData = await this.dbServiceObj.findOne(this.userPurchaseOrderRepository,'buyer_po_number',poNumber);
          if(orderData.seller_id != null && orderData.claimed_by != Constants.CLAMIED_BY_READY_TO_CLAIM) {
            const insert_logs = {};
            insert_logs["po_number"] = orderData.buyer_po_number;
            insert_logs["buyer_po_price"] = orderData?.actual_buyer_po_price || orderData.buyer_po_price;
            insert_logs["seller_po_price"] = orderData?.actual_seller_po_price || orderData.seller_po_price;
            insert_logs["type"] = type;
            insert_logs["buyer_id"] = orderData.buyer_id;
            insert_logs["seller_id"] = orderData.seller_id;
            insert_logs["claimed_by"] = orderData.claimed_by;
            insert_logs["user_id"] = userId;
            insert_logs["action_performed_by"] = actionPerfomedBy;
            if (superAdminId) {
              insert_logs["super_admin_user_id"] = superAdminId;
            }
            await this.dbServiceObj.saveData(insert_logs,this.cancelOrderLogsRepository);

            let order_update_set = { claimed_by: Constants.CLAMIED_BY_READY_TO_CLAIM , seller_id: null, seller_company_name: null}
            await this.dbServiceObj.updateByMultipleWhere(order_update_set,{ buyer_po_number: poNumber },this.userPurchaseOrderRepository);
            await this.dbServiceObj.updateByMultipleWhere({ seller_id : null},{ buyer_po_number: poNumber },this.userPurchaseOrderLineRepository);
            await this.awsQueue.sendSellerOrderCancelEmail(poNumber);
            await this.awsQueue.sendSellerOrderCancelNotification(poNumber,sellerEvent);
            await this.sendWebsocketEvent(poNumber,poLevel,type);
            await this.updateSellerInvoice(poNumber, orderStatusCancelled);
            return Constants.ORDER_CANCEL_SUCCESS;
          }
        }

        const orderLineData = await this.dbServiceObj.findMany(this.userPurchaseOrderLineRepository,'buyer_po_number',poNumber);
        if(orderLineData.length < 1){ return {err_message : "This order does not have an active line."} }
        const activeLinesCount = orderLineData.length;

        if(activeLinesCount === cancelEntireOrder){
          poLevel = true;
        }

        const recalculatedSalesTaxResponse = await this.recalculateSalesTax(orderData, cancelOrderDto, userId, actionPerfomedBy, type);

        if(recalculatedSalesTaxResponse && recalculatedSalesTaxResponse.hasOwnProperty('error_message')){
          return {error_message : recalculatedSalesTaxResponse.error_message};
        }

        for (const OrderLineId of Object.values(purchaseOrderLineIds)) 
        {
          purchaseOrderLineId = OrderLineId.toString();
          if(orderData.payment_method===Constants.PAYMENT_METHOD_BRYZOS_PAY )
          {
            if(orderLineData && orderLineData[0].payment_token != null)
            {
              const ordersCanceled = await this.getCancelOrderCountByPoNumber(poNumber, orderStatusCancelled);
              // console.log("ordersCanceled : ", ordersCanceled);
              if(ordersCanceled && (ordersCanceled.orders - ordersCanceled.cancelled_orders )==1)
              {
                // console.log("cancel balance transaction");
                let cancelResponse = await this.cancelBalanceTransaction(poNumber,cancelOrderDto);
                if(cancelResponse && cancelResponse.hasOwnProperty('err_message')){
                  let response = {
                    [responseErrorTag]: cancelResponse['err_message']
                  };
                  return response;
                }
              }
            }
          }
          response = await this.cancelOrderFromDb(poNumber,type,userId, actionPerfomedBy, purchaseOrderLineId, superAdminId);
        }

        if(orderData.payment_method===Constants.PAYMENT_METHOD_BRYZOS_PAY )
        {
          let buyerId = orderData.buyer_id;
          await this.setLogsBuyerCredits(buyerId, actionPerfomedBy, userId, type, poNumber);
        }
      }
      if(response === Constants.ORDER_CANCEL_SUCCESS){
        if(order_cancel){
          let CancelledOrderStatusId = order_cancel.id;
          await this.updateSellerInvoice(poNumber, CancelledOrderStatusId);
        }

        if(type === Constants.BUYER_ORDER_CANCEL ){
          await this.awsQueue.sendBuyerOrderCancelEmail(poNumber);
          await this.awsQueue.sendBuyerOrderCancelNotification(poNumber,buyerCancelOrderEvent);
          await this.sendWebsocketEvent(poNumber,poLevel,type);
        }else if(type === Constants.SELLER_ORDER_CANCEL ){
          await this.awsQueue.sendSellerOrderCancelEmail(poNumber);
          await this.awsQueue.sendSellerOrderCancelNotification(poNumber,sellerEvent);
        }
      }
    } 
       
    return response;
  }

  async cancelOrderFromDb(po_number,type,userId, actionPerfomedBy, purchaseOrderLineId, superAdminId?: string)
  {
    let response = null;

    const order_cancel = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCANCELLED);

    if(order_cancel  && purchaseOrderLineId && po_number)
    {
        const orderLineData = await this.dbServiceObj.findOne(this.userPurchaseOrderLineRepository,'id',purchaseOrderLineId);

        if(orderLineData && orderLineData.order_status_id !== order_cancel.id)
        {
          let order_line_update_set=null;
          let order_update_set={};
      
          const orderData = await this.dbServiceObj.findOne(this.userPurchaseOrderRepository,'buyer_po_number',po_number);
          const insert_logs = {};
          insert_logs["po_number"] = orderLineData.buyer_po_number;
          insert_logs["purchase_order_line_id"] = purchaseOrderLineId;
          insert_logs["po_line"] = orderLineData.po_line;
          insert_logs["buyer_po_price"] = orderLineData?.actual_buyer_line_total;
          insert_logs["seller_po_price"] = orderLineData?.actual_seller_line_total;
          insert_logs["type"] = type;
          insert_logs["buyer_id"] = orderLineData.buyer_id;
          insert_logs["seller_id"] = orderLineData.seller_id;
          insert_logs["claimed_by"] = orderData.claimed_by;
          insert_logs["user_id"] = userId;
          insert_logs["action_performed_by"] = actionPerfomedBy;
          if (superAdminId) {
            insert_logs["super_admin_user_id"] = superAdminId;
          }

          const order_ledger = await this.dbServiceObj.findOne(this.userPurchaseOrderLedgerRepository,'purchase_order_line_id',purchaseOrderLineId);
          const escrow = Number(orderLineData.actual_seller_line_total) + Number(orderLineData.sales_tax);

          const insert = {};
          insert["user_id"] = orderLineData.buyer_id;
          insert["purchase_order_line_id"] = purchaseOrderLineId;
          insert["transaction_type"] = actionPerfomedBy == Constants.SUPER_ADMIN_USER ? Constants.TRANSACTION_TYPE_BA_RCDC : Constants.TRANSACTION_TYPE_USER_RCDC;
          // if order canceled on line level
          if(type === Constants.BUYER_ORDER_CANCEL || type === Constants.SELLER_ORDER_CANCEL)
          {
            insert["buyer_price_per_unit"] = orderLineData.buyer_price_per_unit * -1;
            insert["extended"] = orderLineData.actual_buyer_line_total * -1; 
            insert["buyer_purchase"] = orderLineData.actual_buyer_line_total * -1;
            insert["escrow"] = orderLineData.actual_seller_line_total * -1;
            insert["sales_tax"] = orderLineData.sales_tax * -1;
            // insert["seller_sales_tax"] = orderLineData.seller_sales_tax * -1;

            order_line_update_set = {order_status_id:order_cancel.id, is_active: "0", status: type};
            
            let orderCancelStatusId= order_cancel.id;
            let updated_buyer_po_price = orderData?.actual_buyer_po_price
            ? orderData.actual_buyer_po_price - orderLineData.actual_buyer_line_total
            : orderData?.buyer_po_price - orderLineData.actual_buyer_line_total;
          
            let updated_seller_po_price = orderData?.actual_seller_po_price
            ? orderData.actual_seller_po_price - orderLineData.actual_seller_line_total
            : orderData?.seller_po_price - orderLineData.actual_seller_line_total;

            let updated_total_weight = orderData?.actual_total_weight
            ? orderData.actual_total_weight - orderLineData.actual_total_weight
            : orderData?.total_weight - orderLineData.actual_total_weight;

            let updated_sales_tax = orderData?.sales_tax
            ? orderData.sales_tax - orderLineData.sales_tax
            : null;

            if (orderData.actual_buyer_po_price) {
              const updateOrderObject  = { actual_buyer_po_price: updated_buyer_po_price, actual_seller_po_price: updated_seller_po_price, actual_total_weight: updated_total_weight, sales_tax: updated_sales_tax };
              order_update_set = Object.assign({}, order_update_set, updateOrderObject);
            } else if (orderData.buyer_po_price) {
              const updateOrderObject  = { buyer_po_price: updated_buyer_po_price, seller_po_price: updated_seller_po_price, total_weight: updated_total_weight, sales_tax: updated_sales_tax };
              order_update_set = Object.assign({}, order_update_set, updateOrderObject);
            }
            
            await this.dbServiceObj.saveData(insert,this.userPurchaseOrderLedgerRepository);
            await this.dbServiceObj.updateByMultipleWhere(order_line_update_set,{ id: purchaseOrderLineId },this.userPurchaseOrderLineRepository)

            const ordersCanceled = await this.getCancelOrderCountByPoNumber(po_number, orderCancelStatusId);
            if(ordersCanceled && ordersCanceled.orders == ordersCanceled.cancelled_orders ){
              const updateOrderObject = { is_active: "0" };
              order_update_set = Object.assign({}, order_update_set, updateOrderObject);
            }

            await this.dbServiceObj.updateByMultipleWhere(order_update_set,{ buyer_po_number: po_number },this.userPurchaseOrderRepository);
       
            await this.dbServiceObj.saveData(insert_logs,this.cancelOrderLogsRepository);

             if(orderData && orderData.payment_method==Constants.PAYMENT_METHOD_BRYZOS_PAY )
            {  
              let buyerId = orderLineData.buyer_id;
              const cbnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository,'user_id',buyerId);
             
              if(cbnplData)
              {
                let sales_tax = (orderLineData.sales_tax != null || orderLineData.sales_tax != undefined || !isNaN( orderLineData.sales_tax))? parseFloat(orderLineData.sales_tax) : 0;
                
                let orderLinePrice = parseFloat(orderLineData.actual_buyer_line_total) + Number(sales_tax);
              
                let availableBryzosCreditLimit = parseFloat(cbnplData.bryzos_available_credit_limit) + orderLinePrice;
                let outsatndingBryzosCreditLimit = parseFloat(cbnplData.bryzos_credit_limit) - availableBryzosCreditLimit;
        
                let cbnpl_update_set = {bryzos_available_credit_limit: availableBryzosCreditLimit, bryzos_outstanding_credit_limit: outsatndingBryzosCreditLimit};
               
                await this.dbServiceObj.updateByMultipleWhere(cbnpl_update_set, { user_id: buyerId }, this.companyBuyNowPayLaterRepository);
              }
              
            }

            return Constants.ORDER_CANCEL_SUCCESS;
          }
        }else {
          response = {
            [responseErrorTag]: `Order line of : ${po_number} does not exist or already canceled`
          };
          return response;
        }
    } else {
      response = {
        [responseErrorTag]: 'Something went wrong!'
      };
      return response;
    }
    return response;
  }

  async cancelBalanceTransaction(poNumber, payload)
  {
    try {   
      const response = {};
      let note = null;
      
      note = {
        note: 'WIDGET_AD_CANCEL_ORDER '+poNumber,
      };
    
      const encodedNotes = JSON.stringify(note);

      const orderLineData = await this.dbServiceObj.findOne(this.userPurchaseOrderLineRepository,'buyer_po_number',poNumber);
      if(orderLineData)
      {
        const transactionId = orderLineData.payment_token;
        const userId = orderLineData.buyer_id;
        const dateTime = new Date().toISOString();
        let balanceKey = await this.balance.getBalanceKey();

        let balanceResponse =  await this.balance.cancelBalanceTransaction(transactionId,balanceKey);
    
        const cancelUrl = process.env.BALANCE_TRANSACTION_URL+'/'+transactionId+'/cancel';
        const balanceError = await this.checkForErrors( balanceResponse, encodedNotes, cancelUrl, userId, 'WIDGET_AD_CANCEL_TRANSACTION_ERROR_TAG');

        if (balanceError && balanceError.hasOwnProperty('err_message')) {
          // console.log('err_message exists!');
          return balanceError;
        }   
        // this will log in balance_log_YYYYMM table     
        await this.awsQueue.balanceLog( transactionId, Buffer.from(JSON.stringify(balanceResponse)).toString('base64'), Buffer.from(JSON.stringify(payload)).toString('base64'), userId, Buffer.from(cancelUrl).toString('base64'), Buffer.from('WIDGET_AD_CANCEL_TRANSACTION_TAG').toString('base64'));
      }else{     
        response['err_message'] = 'Something went wrong!';
        return response;
      } 
    }catch (error) {
        console.log('Exception 2');
        console.log(error);
    }
      
  }

  async checkForErrors(balanceRes, payload, request, userId, tag)
  {
    const response = {};

    if (!balanceRes || (typeof balanceRes === 'object' && (balanceRes.hasOwnProperty('error') || balanceRes.hasOwnProperty('statusCode')))) {

      if (balanceRes !== null && typeof balanceRes === 'object' && balanceRes.hasOwnProperty('statusCode')) {
        if (balanceRes.statusCode >= 200 && balanceRes.statusCode <= 299) {
          return response;
        }
      }
      if (balanceRes === null) {
        response['err_message'] = 'Payment Gateway ERROR';
      }
      try {
        // this will log in balance_log_YYYYMM table
        await this.awsQueue.balanceLog(Buffer.from(payload).toString('base64'), Buffer.from(JSON.stringify(balanceRes)).toString('base64'), Buffer.from('WIDGET_ADMIN_DASHBOARD').toString('base64'), userId, Buffer.from(request).toString('base64'), Buffer.from(tag).toString('base64'));
          
      } catch (error) {
        console.error(error);
      }
      if(balanceRes !== null)
        response['err_message'] = balanceRes.message;
       
    }
    return response;
  }

  sendWebsocketEvent = (poNumber:string,poLevel?:boolean,type?:string)  => new Promise(async function (resolve, reject) 
  {

    let curlResponse = null;
    const urlEndPoint = poLevel ? '/orderCancel' : '/updatePurchaseOrder';
    const updatePoListUrl = process.env.GISS_WS_SERVER + urlEndPoint

    const axios = require('axios');

    let updateData = {
      method: 'post',
      maxBodyLength: Infinity,
      url: updatePoListUrl,
      headers: { 
        'accept': 'application/json', 
        'content-type': 'application/json', 
        'gissToken':  process.env.GISS_UI_TOKEN
      },
      data: {
        buyer_po_number: poNumber,
        cancel_type: type
      }
    };

    axios.request(updateData)
    .then((response) => {
      curlResponse = response.data;
      console.log(curlResponse);
      resolve(curlResponse);
    })
    .catch((error) => {
    console.error(error.response.data);
    resolve(curlResponse);
    });
  }); 

  async getCancelOrderCountByPoNumber(poNumber, orderStatusCancelled)
  {
    if(orderStatusCancelled && poNumber)
    {
      const canceledOrderLines = await this.userPurchaseOrderLineRepository
      .createQueryBuilder('user_purchase_order_line')
      .select('COUNT(*)', 'orders')
      .addSelect('COUNT(CASE WHEN order_status_id = :orderStatusId THEN 1 END)', 'cancelled_orders')
      .where('user_purchase_order_line.buyer_po_number = :poNumber', { poNumber: poNumber })
      .setParameters({ orderStatusId: orderStatusCancelled })
      .getRawOne();

      return canceledOrderLines;
    }
    
  }

  async setLogsBuyerCredits(buyerId, actionPerfomedBy, adminId, type, poNumber){
    const insertCreditLogs = {};
    const cbnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository,'user_id',buyerId);
    if(cbnplData)
    {
      if(actionPerfomedBy == Constants.SUPER_ADMIN_USER)
        insertCreditLogs["admin_id"] = adminId;
        
      insertCreditLogs["buyer_id"] = buyerId;
      insertCreditLogs["credit_limit"] = parseFloat(cbnplData?.bryzos_credit_limit);
      insertCreditLogs["available_credit_limit"] = parseFloat(cbnplData?.bryzos_available_credit_limit);
      insertCreditLogs["outstanding_credit_limit"] = parseFloat(cbnplData.bryzos_credit_limit) - parseFloat(cbnplData.bryzos_available_credit_limit);
      insertCreditLogs["reason"] = type;
      insertCreditLogs["po_number"] = poNumber;


      await this.dbServiceObj.saveData(insertCreditLogs,this.logBryzosCreditLimitRepository);
    }
  }

  async recalculateSalesTax(orderData, cancelOrderDto, userId, actionPerfomedBy, type){

    if(orderData){
      const buyerId = orderData.buyer_id;
      const sellerId = orderData.seller_id;
      const buyerPoNumber = orderData.buyer_po_number;
      const stateId = orderData.state_id;
      const zipCode = orderData.zip;
      const paymentMethod = orderData.payment_method;

      const canceledPoLineIds = Object.values(cancelOrderDto.purchase_order_line_id);

      const orderStatusResolved = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.RESOLVED);
      const orderStatusActive= await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ACTIVE);
    
      const orderStatusIds = [orderStatusActive.id, orderStatusResolved.id];
    
      // Fetch ressale certificate
      const resaleCertData = await this.dbServiceObj.findOneByMultipleWhere(this.userReasaleCertificateRepository,{user_id:buyerId,state_id:stateId});

      // Calculate sales tax rate
      const salesTaxData = await this.baseLibraryService.calculateSalesTaxRate(stateId, zipCode, resaleCertData, this.userPurchaseOrderRepository);
      let salesTaxRate = null;
      if(salesTaxData){
        salesTaxRate =  salesTaxData.sales_tax_rate;
      }
      // Fetch purchase order line data
      const purchaseOrderLineData = await this.dbServiceObj.findManyByWhereAndWhereIn(this.userPurchaseOrderLineRepository, { buyer_po_number: buyerPoNumber }, 'order_status_id', orderStatusIds);

      if (purchaseOrderLineData && purchaseOrderLineData.length > 0 && salesTaxRate != null){
          // Calculate sales tax for each purchase order line
          const activePoLineSalesTaxData = purchaseOrderLineData
          .filter(item => !canceledPoLineIds.includes(item.id) && item.is_buyer_order_open == true)
          .map(item => ({
              id: item.id,
              old_sales_tax: item.sales_tax ? item.sales_tax : 0,
              re_calculated_sales_tax: (salesTaxRate && item.actual_buyer_line_total) ? (salesTaxRate * item.actual_buyer_line_total).toFixed(2) : 0
          }));


          const cancelledPoLineSalesTaxData = purchaseOrderLineData
          .filter(item => canceledPoLineIds.includes(item.id))
          .map(item => ({
              id: item.id,
              sales_tax: item.sales_tax ? item.sales_tax : 0,
              buyer_line_total: item.actual_buyer_line_total ? item.actual_buyer_line_total : 0,
          }));
    
           
        if(activePoLineSalesTaxData.length == 0){
          return false;
        }
          const canceledPoLineSalesTax = cancelledPoLineSalesTaxData.reduce((total, item) => total + parseFloat(item.sales_tax), 0);
          const canceledPoLineBuyerLineprice = cancelledPoLineSalesTaxData.reduce((total, item) => total + parseFloat(item.buyer_line_total), 0);

          const canceledPoLinesTotalPrice = Number(canceledPoLineBuyerLineprice) + Number(canceledPoLineSalesTax);

          // Calculate total sales tax
          let recalculatedPoSalesTax = activePoLineSalesTaxData.reduce((total, item) => total + parseFloat(item.re_calculated_sales_tax), 0).toFixed(2);
          const oldPoSalesTax = activePoLineSalesTaxData.reduce((total, item) => total + parseFloat(item.old_sales_tax), 0).toFixed(2);

          const recalculatedSalesTaxData = {"totalPoTax" : recalculatedPoSalesTax, "purchaseOrderLines" : activePoLineSalesTaxData};
          
          const recalculatedPOLineTax = recalculatedSalesTaxData ? recalculatedSalesTaxData.purchaseOrderLines : [];

          const purchaseOrderLineIdsArr = recalculatedSalesTaxData.purchaseOrderLines.map(item => item.id);
      
          if(recalculatedPOLineTax && recalculatedPOLineTax.length > 0){

            const hasDifferentSalesTax = recalculatedPOLineTax.filter(pol =>
                Number(pol.old_sales_tax) !== Number(pol.re_calculated_sales_tax)
            ).length > 0;

            //At least one item has re-calculated sales tax different from old sales tax.
            if (hasDifferentSalesTax) {

              const poLineSalesTaxData = activePoLineSalesTaxData.map(item => ({
                id: item.id,
                sales_tax: item.re_calculated_sales_tax,
              }));
            
              const cbnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository,'user_id',buyerId);

              const selectFields = ["IFNULL(SUM(escrow),0) as total_seller_price","IFNULL(SUM(buyer_purchase),0) as total_buyer_price","IFNULL(SUM(sales_tax),0) as total_sales_tax", "purchase_order_line_id"];

              const conditions = { purchase_order_line_id: { operator: "IN", value: purchaseOrderLineIdsArr } };

              const orderLedgerData = await this.dbServiceObj.FindByMultipleWhereComparisonANDLeftJoinById(this.userPurchaseOrderLedgerRepository,[],{conditions},'purchase_order_line_id',selectFields);
        
              if(orderLedgerData){
                let insertOrderLedgerData = [];
                let insertCancelLog = [];

                orderLedgerData.forEach(item => {
                  const insertOrderLedger: any = {};
                  const insertLog: any = {};
      
                  const previousSalesTax = item.total_sales_tax || 0;
                  const purchaseOrderLineId = item.purchase_order_line_id;
      
                  const updatedPoLineSalesTaxData = recalculatedSalesTaxData.purchaseOrderLines.find(item => item.id === purchaseOrderLineId);
                  const recalculatedSalesTax = updatedPoLineSalesTaxData ? updatedPoLineSalesTaxData.re_calculated_sales_tax : 0;
                  const ledgerSalesTaxDiff = Number(recalculatedSalesTax) - Number(previousSalesTax);
         
                  insertOrderLedger.sales_tax = ledgerSalesTaxDiff;
                  insertOrderLedger.transaction_type = Constants.TRANSACTION_TYPE_BA_RCDC;
                  insertOrderLedger.purchase_order_line_id = purchaseOrderLineId;
                  insertOrderLedger.user_id = buyerId;
            
                  if (Number(recalculatedSalesTax) !== Number(previousSalesTax)) {
                      insertOrderLedgerData.push(insertOrderLedger);
      
                      insertLog.old_sales_tax = Number(previousSalesTax);
                      insertLog.sales_tax = Number(recalculatedSalesTax);
                      insertLog.purchase_order_line_id = purchaseOrderLineId;
                      insertLog.po_number = buyerPoNumber;
                      insertLog.user_id = userId;
                      insertLog.buyer_po_price = item?.total_buyer_price;
                      insertLog.seller_po_price = item?.total_seller_price;
                      insertLog.type = type;
                      insertLog.buyer_id = buyerId;
                      insertLog.seller_id = sellerId;
                      insertLog.claimed_by = orderData.claimed_by;
                      insertLog.action_performed_by = actionPerfomedBy;

                      insertCancelLog.push(insertLog);
                  }
                });
      

                if(paymentMethod === Constants.PAYMENT_METHOD_BRYZOS_PAY){
                 
                  if(cbnplData)
                  {
        
                    const newAvailableCreditLimit = Number(cbnplData.bryzos_available_credit_limit) + Number(canceledPoLinesTotalPrice);

                    let poSalesTaxDiff = Number(recalculatedPoSalesTax) - Number(oldPoSalesTax);
            
                    if(Number(newAvailableCreditLimit) < Number(poSalesTaxDiff) && Number(oldPoSalesTax) < Number(recalculatedPoSalesTax)){ 
                      return { "error_message" : "Recalculate sales tax failed, insufficient buyer available credit limit!" };
                    }

                    let availableBryzosCreditLimit = Number(cbnplData.bryzos_available_credit_limit) - Number(poSalesTaxDiff);
                    let outsatndingBryzosCreditLimit = Number(cbnplData.bryzos_credit_limit) - Number(availableBryzosCreditLimit);
            
                    let cbnpl_update_set = {bryzos_available_credit_limit: availableBryzosCreditLimit, bryzos_outstanding_credit_limit: outsatndingBryzosCreditLimit};
                  
                    await this.dbServiceObj.updateByMultipleWhere(cbnpl_update_set, { user_id: buyerId }, this.companyBuyNowPayLaterRepository);
                  }
                    
                }

                // console.log('insertCancelLog : ',insertCancelLog);

                // await this.dbServiceObj.saveData(insertCancelLog,this.cancelOrderLogsRepository);
         
                // if(recalculatedPoSalesTax){
                //   await this.dbServiceObj.updateByMultipleWhere({sales_tax: recalculatedPoSalesTax},{ buyer_po_number: buyerPoNumber },this.userPurchaseOrderRepository);
                // }

                if(poLineSalesTaxData && poLineSalesTaxData.length>0){
                  await this.dbServiceObj.saveData(poLineSalesTaxData,this.userPurchaseOrderLineRepository);
                }

                if(insertOrderLedgerData && insertOrderLedgerData.length > 0){
                  await this.dbServiceObj.saveData(insertOrderLedgerData,this.userPurchaseOrderLedgerRepository);
                }

                if(recalculatedPoSalesTax){
                  //get total updated sales tax
                  const mapper = { selectFields: ["IFNULL(SUM(sales_tax),0) as total_sales_tax"] };
  
                  const orderLineSalesTaxData = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.userPurchaseOrderLineRepository, { buyer_po_number: buyerPoNumber}, mapper, 'created_date', 'ASC');
  
                  if (orderLineSalesTaxData?.length > 0) {
                    recalculatedPoSalesTax = orderLineSalesTaxData[0].total_sales_tax;
  
                    await this.dbServiceObj.updateByMultipleWhere({sales_tax: recalculatedPoSalesTax},{ buyer_po_number: buyerPoNumber },this.userPurchaseOrderRepository);
                  }               
                }

                if(Number(recalculatedPoSalesTax) == 0)
                {
                  const taxExemptLog: any = {};

                  if(salesTaxData){
                    if(salesTaxData.is_threshold_met_cert_approved){
                      taxExemptLog.resale_certificate_url = resaleCertData.cerificate_url_s3;
                      taxExemptLog.resale_certificate_id = resaleCertData.id;
                    }
                    taxExemptLog.po_number = buyerPoNumber;
                    taxExemptLog.event = type;
                    taxExemptLog.annual_total_transaction_amount = salesTaxData.annual_transaction_amount;
                    taxExemptLog.annual_total_transaction = salesTaxData.annual_transaction;
                    taxExemptLog.combined_rate_number = salesTaxData.ref_combined_rate_number;
                    taxExemptLog.economic_nexus_threshold = salesTaxData.ref_economic_nexus_threshold;
                    taxExemptLog.economic_nexus_threshold_number_transactions = salesTaxData.ref_economic_nexus_threshold_number_transactions;
                  }
  
                  await this.dbServiceObj.saveData(taxExemptLog,this.taxExemptedPoRepository);    
                }
              }

            }


          }
      }
    }
  }

  async updateSellerInvoice(poNumber: string, cancelledOrderStatusId: number){
    const sellerPoNumber = poNumber.replace('S','P');
    const columnContditions1 = [
      { columnName: 'is_active', operator: '=', value: true },
      { columnName: 'po_number', operator: '=', value: sellerPoNumber }
    ];
    const sellerInvoiceData = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.sellerInvoiceDumpRepository,columnContditions1,['*']);
    if(sellerInvoiceData.length === 0){ return; }
    // Sum the item_count of the same po number
    const invoiceLineItemCount = sellerInvoiceData ? sellerInvoiceData.reduce((sum, record) => Number(sum) + Number(record.item_count || 0), 0) : 0;
    
    // Fetch order line data based on buyer_po_number
    const columnContditions = [
      { columnName: 'is_active', operator: '=', value: true },
      { columnName: 'order_status_id', operator: '!=', value: cancelledOrderStatusId},
      { columnName: 'buyer_po_number', operator: '=', value: poNumber },
    ]

    const orderLineData = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.userPurchaseOrderLineRepository,columnContditions,['*']);

    let orderLineItemCount = 0;

    // Check if order line data is available and extract the seller ID and line item count
    if (orderLineData && orderLineData.length > 0) {
      orderLineItemCount = orderLineData.length;
    }

    if(invoiceLineItemCount === orderLineItemCount) {
      await this.dbServiceObj.updateByMultipleWhere({is_po_line_matched: true}, {"po_number": sellerPoNumber},this.sellerInvoiceDumpRepository);
    } else if (invoiceLineItemCount !== orderLineItemCount) {
      await this.dbServiceObj.updateByMultipleWhere({ is_po_line_matched: false }, { "po_number": sellerPoNumber }, this.sellerInvoiceDumpRepository);
    }
    // Whole Po is cancelled so therefore we have to remove buyer invoice pdf from the seller_invoice_dump table 
    if (orderLineItemCount === 0) {
      await this.dbServiceObj.updateByMultipleWhere({ is_cancelled: true, buyer_invoice_s3_url : null }, { "po_number": sellerPoNumber }, this.sellerInvoiceDumpRepository);
    }else{
      await this.awsQueue.sendPoToGeneratePdfData(poNumber, Constants.BUYER_INVOICE_GENERATE_PDF_ONLY, 'SELLER_INVOICE_EXTRACT');
    }
  }
}