import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { AwsUtilityV3, BaseLibraryService, BryzosLogger, DataBaseService, Constants as libConstants } from '@bryzos/base-library';
import { CassAdhocSellerSetup, CassTransactionView, LogCassPaymentStatus, LogCassSupplier, PaymentInfo, ReferenceDataGeneralSettings, ReferenceDataOrderStatus, User, UserArPaymentInfo, UserPurchaseOrder, UserPurchaseOrderLedger, UserPurchaseOrderProcessingHistory, UserSellingPreference, CassFundingReceivedStatus, CassTransactionEmail, AdminLogCloseOrders, CompanyBuyNowPayLater, LogBryzosCreditLimit, UserPurchaseOrderLine, CassMappingProbalePosView, ReferenceDataSettings, CassFinancialTransactionStatus, CassBuyerAmountReceivedPerPO } from '@bryzos/extended-widget-library';
import { Constants } from '../Constants';
import * as uuid4 from 'uuid4';
import { Utils } from '../utils';
import * as path from 'path';
import * as Client from 'ssh2-sftp-client';
import * as moment from 'moment-timezone';
import * as fs from 'fs';
import * as AsyncLock from 'async-lock';
import { Balance } from '../Balance';
import { stringify } from 'csv-stringify';
import { CassDto, SellerSetupDto, CassTransactionDto, CassAdhocSellerSetupDto, MapTransactionToPoDto, GetTransactionDto } from './dto/cass.dto';
import { CassApis } from './CassApis';
import { paginateRaw } from 'nestjs-typeorm-paginate';
import { HttpService } from '@nestjs/axios';
import { createHash } from 'crypto';

const responseErrorTag = Constants.ERROR_TAG;

@Injectable()
export class CassService {
  private dbObjService = new DataBaseService();
  private lock: AsyncLock = new AsyncLock();
  private cassPassword: string;

  constructor(
    private readonly entityManager: EntityManager,
    @InjectRepository(CassTransactionView) private readonly cassTransactionView: Repository<CassTransactionView>,
    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly referenceDataOrderStatusRepository: Repository<ReferenceDataOrderStatus>,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(UserPurchaseOrderLedger) private readonly userPurchaseOrderLedgerRepository: Repository<UserPurchaseOrderLedger>,
    @InjectRepository(ReferenceDataGeneralSettings) private readonly referenceDataGeneralSettingsRepository: Repository<ReferenceDataGeneralSettings>,
    @InjectRepository(PaymentInfo) private readonly paymentInfoRepository: Repository<PaymentInfo>,
    @InjectRepository(UserArPaymentInfo) private readonly userArPaymentInfoRepository: Repository<UserArPaymentInfo>,
    @InjectRepository(UserPurchaseOrderProcessingHistory) private readonly userPurchaseOrderProcessingHistoryRepository: Repository<UserPurchaseOrderProcessingHistory>,
    @InjectRepository(LogCassPaymentStatus) private readonly logCassPaymentStatusRepository: Repository<LogCassPaymentStatus>,
    @InjectRepository(LogCassSupplier) private readonly logCassSupplierRepository: Repository<LogCassSupplier>,
    @InjectRepository(CassAdhocSellerSetup) private readonly cassAdhocSellerRepository: Repository<CassAdhocSellerSetup>,
    @InjectRepository(CassFundingReceivedStatus) private readonly cassFundingReceivedRepository: Repository<CassFundingReceivedStatus>,
    @InjectRepository(UserSellingPreference) private readonly userSellingPreferenceRepository: Repository<UserSellingPreference>,
    @InjectRepository(CassTransactionEmail) private readonly cassTransactionEmailRepository: Repository<CassTransactionEmail>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(AdminLogCloseOrders) private readonly adminLogCloseOrdersRepository: Repository<AdminLogCloseOrders>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(LogBryzosCreditLimit) private readonly logBryzosCreditLimitRepository: Repository<LogBryzosCreditLimit>,
    @InjectRepository(CassMappingProbalePosView) private readonly cassMappingProbalePosViewRepository: Repository<CassMappingProbalePosView>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(CassFinancialTransactionStatus) private readonly cassFinancialTransactionStatusRepository: Repository<CassFinancialTransactionStatus>,
    @InjectRepository(CassBuyerAmountReceivedPerPO) private readonly cassBuyerAmountReceivedPerPORepository: Repository<CassBuyerAmountReceivedPerPO>,
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly baseLibraryService: BaseLibraryService,
    private readonly balance: Balance,
    private readonly utils: Utils,
    private readonly cassApis: CassApis,
    private readonly awsUtility: AwsUtilityV3,
    private readonly httpService : HttpService
  ) { 
    this.getCassPassword();
  }

  private async getCassPassword() {
    this.cassPassword = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_CASS_SFTP, Constants.PASSWORD);
  }

  async getCassTransactionData(params: { page: number, limit: number, search: string }) {
    let response = null;

    const paginationApplied = params.page > 0 && params.limit > 0;

    // const leftJoins = [
    //   { table: 'user_purchase_order', joinColumn: 'seller_po_number', mainTableColumn: 'PO_NUMBER' },
    //   { table: 'user_purchase_order_line', joinColumn: 'buyer_po_number', mainTableColumn: 'buyer_po_number', joinTable: 'user_purchase_order' },
    // ];
    //const conditions = [{ column: 'seller_id', operator: 'NOT NULL', value: null, table: 'user_purchase_order', }];
    // const mapperFields = { selectFields: ['table1.*'] };

    // const orderBy = {
    //   'user_purchase_order.created_date': 'DESC',
    //   'user_purchase_order_line.po_line': 'ASC'
    // };

    // const groupByColumn = 'table1.PO_NUMBER';

    // response = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.cassTransactionView, [], [], mapperFields, null, null, params);
    response = await this.dbObjService.findAllWithoutIsActive(this.cassTransactionView);

    if (response === null || response === undefined) {
      return { [responseErrorTag]: 'Something went wrong!' };
    }

    // if (paginationApplied) {
    //   if (Array.isArray(response.items) && response.items.length < 1) {
    //     return { [responseErrorTag]: 'No data found' };
    //   }
    // } else {
    if (Array.isArray(response) && response.length < 1) {
      return { [responseErrorTag]: 'No data found' };
    }
    // }
    return response;
  }

  async saveCassTransaction(payload: CassDto, userId: number) {
    return this.lock.acquire('lockKey', async () => {
      try {
        return await this.uploadSmtpCass(payload, userId);
      } catch (error) {
        console.error(error);
        return { 'error_message': 'Something went wrong!' };
      }
    });
  };

  async getSellerPaymentSetup(data: SellerSetupDto) {
    let response = null;
    const poData = await this.dbObjService.findOne(this.userPurchaseOrderRepository, "seller_po_number", data.po_number);
    if (poData?.seller_id) {
      response = this.getSellerPayment(poData.seller_id);
    }
    return response;
  }

  async getSellerPayment(sellerId: string) {
    let paymentData: any = null;
    const paymentDataLeftJoins = [
      { table: "payment_info", joinColumn: "id", mainTableColumn: "payment_info_id" },
      { table: "reference_data_pgpm_mapping", joinColumn: "id", mainTableColumn: "pgpm_mapping_id", joinTable: "payment_info" },
      { table: "user_selling_preference", joinColumn: "user_id", mainTableColumn: "user_id", joinTable: "payment_info" },
    ]
    const paymentDataConditions = [
      { column: "user_id", operator: "=", value: sellerId, table: "payment_info" },
      { column: "is_active", operator: "=", value: true, table: "payment_info" },
      { column: "is_active", operator: "=", value: true },
      { column: "is_active", operator: "=", value: true, table: "user_selling_preference" },
    ]
    const mapperFields = {
      selectFields: ['user_selling_preference.company_name ,table1.user_id as seller_id', 'payment_info.pgpm_mapping_id as pgpm_mapping_id', 'reference_data_pgpm_mapping.payment_method as payment_method', 'table1.routing_number as routing_number', 'table1.account_number as account_number']
    }
    paymentData = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.userArPaymentInfoRepository, paymentDataLeftJoins, paymentDataConditions, mapperFields);

    if (paymentData && paymentData.length > 0) {
      paymentData.forEach(item => {
        item.email = null;
      });
      const cassTransactionEmail = await this.dbObjService.getLatestData(this.cassTransactionEmailRepository, { seller_company_name: paymentData[0].company_name }, 'created_date', 1, 'DESC');
      if (cassTransactionEmail && cassTransactionEmail.length > 0) {
        paymentData[0]['email'] = cassTransactionEmail[0].to_email;
      }
    }
    
    return paymentData;
    
  }

  async uploadSmtpCass(payload: CassDto, userId: number) {
    let response = {};
    let data: any = {};
    let insert: any = {};
    let insertLog: any = {};
    let amount = payload.amount;

    if (amount <= 0) {
      return { [responseErrorTag]: 'Enter amount greather then zero.' };
    }

    let poNumber = payload.po_number;
    let paymentMethod = payload.payment_method;
    let internalNote = payload.internal_note;
    // let statementDescriptor = payload.statement_descriptor.substring(0, 80); // For 1000 record type  "Remarks" has string limit of 80.
    let statementDescriptor = payload.statement_descriptor; //For 2201 record type (CASS_PAYMENT_RECORD_TYPE), 5th record has string limit of 200.
    let sellerPaymentMethod = payload.seller_payment_method;
    let sellerInvoiceNumber = payload?.seller_invoice_number ?? null;
    let date = moment().utc().format('YYYYMMDD');

    if (!(await this.checkAndCreatePublicForlder())) {
      return { [responseErrorTag]: 'unable to create public folder in root directory' };
    }

    let filePath: any = path.join(process.cwd(), 'public');
    let filenameLogging = 'cass.service.ts';
    let logEvent = 'Upload 0150 file in CASS';
    let referenceId = userId;
    let processId = poNumber;
    let remoteFile = null;
    let recipientEmail = null;
    let status = null;

    if (payload?.to_email) {
      const emails = payload.to_email.split(";").map(item => item.trim()).join(";"); 
      recipientEmail = await this.emailValidation(emails);
      if (recipientEmail == "") {
        return { error_message: "Some of the entered email's are not valid please check" };
      }
    }
    
    let userData: any = await this.getUserDataById(userId);
    if (userData) {
      insert.user_id = userData.email_id;
      let timeStamp = userData.time_stamp;
      let ctTimeStamp = this.utils.convertUtcToCst(timeStamp);
      let ctDate = ctTimeStamp.format('M/D/YY');
      let ctTime = ctTimeStamp.format('hh:mm A');
      insert.user_id_login_date = ctDate;
      insert.user_login_time_ct = ctTime;
    }

    insert.amount = amount;
    insert.seller_invoice_number = sellerInvoiceNumber;
    let resolvedStatusId = await this.getOneReferenceDataOrderId(Constants.ORDER_STATUS_RESOLVED);
    let activeStatusId = await this.getOneReferenceDataOrderId(Constants.ORDER_STATUS_ACTIVE);
    let orderCompletedId = await this.getOneReferenceDataOrderId(Constants.ORDER_STATUS_COMPLETED);

    const orderDetails = await this.getUserPurchaseOrder(poNumber, [activeStatusId, resolvedStatusId, orderCompletedId]);
    if (paymentMethod === Constants.BOX_4_BNPL && !orderDetails?.payment_token) {
      return { [responseErrorTag]: 'There is no BNPL set up for this Order' };
    }

    let logCassPaymentStatus = await this.getLogCassPaymentStatusData(poNumber);

    let version: any = 'A';
    if (logCassPaymentStatus?.length > 0) {
      let count = logCassPaymentStatus.length;
      version = logCassPaymentStatus[count - 1].version;
      version = this.getNextVersionCode(version);
    }
    let poUniqueIdentifier = `${poNumber}-${version}`;
    processId = poNumber;

    if (process.env.ENVIRONMENT === 'DEMO' && process.env.CASS_PO_UNIQUE_IDENTIFIER_PREFIX) {
      poUniqueIdentifier = `${process.env.CASS_PO_UNIQUE_IDENTIFIER_PREFIX}-${poNumber}-${version}`;
      processId = poNumber;
    }

    data = { message: { Step: 'Step 0', process_id: processId, message_attribute: JSON.stringify(payload) }, info: 'Data sent via UI' };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

    if (!sellerPaymentMethod && paymentMethod === Constants.BOX_1_SELLER_PAYMENT) {
      return { [responseErrorTag]: 'No seller payment selected' };
    }

    if (sellerPaymentMethod && sellerPaymentMethod === Constants.CHECK_PAYMENT_METHOD && paymentMethod === Constants.BOX_1_SELLER_PAYMENT) {
      return { [responseErrorTag]: 'Seller check payment method not allowed' };
    }
    insert.transfer_type = paymentMethod;

    //get withdrawal details
    let withdrawls: any = await this.getWithdrawlTypeAmountType(paymentMethod, amount, poNumber, userId, sellerPaymentMethod);

    data = { message: { process_id: processId, Step: 'Step 1', message_attribute: JSON.stringify(withdrawls) }, info: 'Data from getWithdrawlTypeAmountType function', };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

    if (!withdrawls) {
      return { [responseErrorTag]: 'PO not found' };
    }

    let disbursementMethod = withdrawls.disbursement_method;
    if (!disbursementMethod) {
      return { [responseErrorTag]: 'No seller payment found' };
    }

    let destinationAccountId = withdrawls.destination_account_id;
    if (!destinationAccountId) {
      return { [responseErrorTag]: 'No destination account found' };
    }

    data = { message: { processId: processId, Step: 'Step 2', message_attribute: JSON.stringify({ amount: amount, paymentMethod: paymentMethod, disbursement_method: withdrawls.disbursement_method, statementDescriptor: statementDescriptor, destination_account_id: withdrawls.destination_account_id }) }, info: 'Inside uploadSmtpCass' };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data,);

    //get buyer from cass
    let cassBuyer = await this.getCassBuyer(orderDetails.buyer_id, userId);
    if (cassBuyer?.err_message) {
      return cassBuyer;
    } else if (!cassBuyer) {
      return { [responseErrorTag]: 'No buyer account found' };
    }

    //set drawndown method : this is same for all 4 boxes
    //04 – ACH Debit (CIS to debit buyer’s account)
    let drawdownMethod = process.env.DEFAULT_DRAWDOWN_METHOD;
    insert["seller_company_name"] = withdrawls?.seller_company_name;
    const drawdownAmount = '0.00';

    //set disbursement method

    //CASS file name
    let localFileName = `${process.env.CASS_ID}${process.env.CASS_TARGET_ENV}${process.env.CASS_ORIGINAL_FILE_TYPE}${moment().utc().format('YYYYMMDDhhmmss')}.csv`;

    let cassFile = [`${Constants.CASS_FILE_RECORD_TYPE},${localFileName}`];

    let cassTransaction = [`${Constants.CASS_TRANSACTION_RECORD_TYPE},${poUniqueIdentifier},${poUniqueIdentifier},${date},${cassBuyer},${destinationAccountId},${drawdownAmount},${drawdownMethod},USD,${date},,${amount},${disbursementMethod},USD,${date},,,,`];

    let list = [`${Constants.CASS_PAYMENT_RECORD_TYPE},,,,${statementDescriptor},,,,,,,,,,`];

    let file: any = await this.openFile(path.join(filePath, localFileName));

    const _cassFile = [];
    cassFile.forEach((line1) => {
      const items = (line1.toString()).split(',');
      _cassFile.push(...items);
    });

    const _cassTransaction = [];
    cassTransaction.forEach((line2) => {
      const items = (line2.toString()).split(',');
      _cassTransaction.push(...items);
    });

    const _list = [];
    list.forEach((line3) => {
      const items = (line3.toString()).split(',');
      _list.push(...items);
    });

    const writeInCsvFileData = [_cassFile, _cassTransaction, _list];

    //need 4th line for BNPL transfer
    if (withdrawls['buyer_payment_type'] === Constants.CASS_BNPL_PAYMENT) {
      let bnplTransferDetails = await this.getBnplTransferDetails(orderDetails, userId);
      if (!bnplTransferDetails || bnplTransferDetails?.err_message) {
        response = { [responseErrorTag]: 'Something went wrong' };
        insert['exception'] = 'No transaction info found in Balance';
        // goto Insert_Status_In_DB;
        await this.gotoInsertStatusInDBCode(insert, payload, orderDetails, poNumber, filePath, remoteFile, localFileName, userId, processId, filenameLogging, referenceId, logEvent);
        return response;
      }

      //Recipient Name max length=22
      let bnplLine = [Constants.WIRE_DISBURSEMENT_RECORD_TYPE, `${userData.first_name} ${userData.last_name}`, `${bnplTransferDetails['routing_number'] ?? ''}`, `${bnplTransferDetails['account_number'] ?? ''}`];
      const _bnplLine = [];
      bnplLine.forEach((line4) => {
        const items = (line4.toString()).split(',');
        _bnplLine.push(...items);
      });
      writeInCsvFileData.push(_bnplLine);
    }

    const csvFileData = await this.writeInCsvFile(path.join(filePath, localFileName), writeInCsvFileData);
    if (!csvFileData) {
      return { [responseErrorTag]: 'error in csv file creation' };
    }

    let localFile: any = await this.readFile(path.join(filePath, localFileName));
    //encode data to save in loggly
    let encodedFileContent = await this.utils.getEncodedData(localFile);

    data = { message: { process_id: processId, Step: 'Step 3', message_attribute: JSON.stringify({ file_name: localFileName, file_content: encodedFileContent }) }, info: 'Local File Saved without processing' };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

    insert['local_file'] = localFile;

    // let cassPassword: any = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_CASS_SFTP, Constants.PASSWORD);

    // login via sftp
    const sftp = new Client();
    try {
      await sftp.connect({ host: process.env.CASS_IP, port: 22, username: process.env.CASS_USER, password: this.cassPassword });

      //check if folder is present on remote server
      let targetDir = '';
      if (process.env.CASS_ENV == Constants.LOCAL_ENV) {
        targetDir = (await sftp.exists(Constants.CASS_TEST_DIR)) === 'd' ? Constants.CASS_TEST_DIR : '';
      } else if (process.env.CASS_ENV == Constants.PROD_ENV) {
        targetDir = (await sftp.exists(Constants.CASS_PROD_DIR)) === 'd' ? Constants.CASS_PROD_DIR : '';
      }

      let serverFileName = localFileName;
      insert['status'] = Constants.NOT_UPLOADED;
      if (targetDir) {
        data = { message: { process_id: processId, Step: 'Step 5', message_attribute: JSON.stringify(serverFileName) }, info: 'CASS Server file, Start Uploading file...' };
        this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

        //with SFTP::SOURCE_LOCAL_FILE it uploads a file and without it it uploads a string.
        file = await sftp.put(path.join(filePath, localFileName), `/${targetDir}/${serverFileName}`);
        if (file) {
          let dst = fs.createWriteStream(path.join(filePath, 'remote-file' + localFileName));
          await sftp.get(`/${targetDir}/${serverFileName}`, dst);

          remoteFile = await this.readFile(path.join(filePath, 'remote-file' + localFileName));
          insert['internal_note'] = internalNote;
          if (remoteFile) {
            response = remoteFile;
            data = { message: { process_id: processId, Step: 'Step 6', message_attribute: JSON.stringify(serverFileName) }, info: 'File Uploaded Successfully!', };
            this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

            insert['status'] = status = Constants.UPLOADED;
            insert['version'] = version;

            //insert in order_ledger
            let ledgerId = uuid4();
            let orderLedgerInsert = { id: ledgerId, [withdrawls.amount_type]: amount, purchase_order_line_id: orderDetails.purchase_order_line_id, transaction_type: 'CASS_TRANSFER' };
            if (withdrawls['withdrawl_type']) {
              orderLedgerInsert['withdrawal_type'] = withdrawls['withdrawl_type'];
            } else if ('buyer_payment_type' in withdrawls) {
              orderLedgerInsert['buyer_payment_type'] = withdrawls['buyer_payment_type'];
            }

            await this.dbObjService.saveData(orderLedgerInsert, this.userPurchaseOrderLedgerRepository);
            insert['order_ledger_id'] = ledgerId;
            //insert into order_processing_history
            await this.dbObjService.saveData({ id: uuid4(), po_number: poNumber, price: amount, purchase_order_line_id: orderDetails.purchase_order_line_id, event: 'CASS_' + paymentMethod.toUpperCase(), json: JSON.stringify(payload) }, this.userPurchaseOrderProcessingHistoryRepository);
          } else {
            insert['exception'] = 'File get error';
            response = { [responseErrorTag]: 'Something Went Wrong!' };
          }
        } else {
          data = { message: { process_id: processId, Step: 'Error 2', message_attribute: JSON.stringify(serverFileName), }, info: 'File Upload Exception!' };
          this.utils.createLogData(filenameLogging, referenceId, logEvent, data,);
          insert['exception'] = Constants.FILE_UPLOAD_EXCEPTION;
          response = { [responseErrorTag]: 'Something Went Wrong!' };
        }
      } else {
        data = { message: { process_id: processId, Step: 'Error 3', message_attribute: JSON.stringify(serverFileName), }, info: 'Target Directory not found!', };
        this.utils.createLogData(filenameLogging, referenceId, logEvent, data,);
        insert['exception'] = Constants.TARGET_DIR_NOT_FOUND;
        response = { [responseErrorTag]: 'Something Went Wrong!' };
      }
      await this.gotoInsertStatusInDBCode(insert, payload, orderDetails, poNumber, filePath, remoteFile, localFileName, userId, processId, filenameLogging, referenceId, logEvent);

      if(paymentMethod == Constants.BOX_1_SELLER_PAYMENT|| paymentMethod == Constants.BOX_5_ADHOC_PAYMENT){
        const cassTransaction = await this.dbObjService.findOne(this.logCassPaymentStatusRepository, "cass_filename", localFileName );
        insertLog.po_number = poNumber;
        insertLog.transaction_type = paymentMethod
        insertLog.amount = amount;
        insertLog.to_email = recipientEmail;
        insertLog.cass_payment_id = cassTransaction ? cassTransaction.id : null;
        insertLog.event = Constants.CASS_TRANSACTION_FUNDS_ON_WAY;
        insertLog.status = status;
        insertLog.admin_id = referenceId;
        insertLog.seller_company_name = withdrawls?.seller_company_name;

        await this.dbObjService.saveData(insertLog, this.cassTransactionEmailRepository);
      }

      response = "Successfully Uploaded";
      return response;
    } catch (e) {
      console.log("send email error : ", e);
      console.log("send email error message : ", e?.message);
      console.log("send email error stack: ", e?.stack);
      data = { message: { process_id: processId, Step: 'Error 1', message_attribute: JSON.stringify(e) }, info: 'SFTP Login Failed' };
      this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

      insert['exception'] = Constants.LOGIN_FAILED;
      response = { [responseErrorTag]: 'Remote server login failed' };
      // goto Insert_Status_In_DB;
      await this.gotoInsertStatusInDBCode(insert, payload, orderDetails, poNumber, filePath, remoteFile, localFileName, userId, processId, filenameLogging, referenceId, logEvent);
      return response;
    } finally {
      sftp.end();
    }
  };

  async gotoInsertStatusInDBCode(insert, payload, orderDetails, poNumber, filePath, remoteFile, localFileName, userId, processId, filenameLogging, referenceId, logEvent, isAdhocUser = null) {

    // Insert_Status_In_DB:
    if (payload['payment_method'] === Constants.BOX_1_SELLER_PAYMENT) {
      //get seller account and routing number
      let sellerBankInfo = null;
      let truevalutDocumentId = null;

      if (isAdhocUser) {

        sellerBankInfo = await this.dbObjService.findOne(this.cassAdhocSellerRepository, 'unique_identifier', payload.cass_unique_id);
        truevalutDocumentId = sellerBankInfo ? sellerBankInfo.truevault_reference_document_id : null;
      } else {
        sellerBankInfo = await this.cassApis.getSellerInfo(orderDetails.seller_id);
        truevalutDocumentId = sellerBankInfo ? sellerBankInfo.reference_document_id : null;
      }

      if (sellerBankInfo) {
        insert['routing_number'] = sellerBankInfo.routing_number;
        insert['account_number'] = sellerBankInfo.account_number;
        insert['truevault_reference_document_id'] = truevalutDocumentId;

      }
    }
    insert['id'] = uuid4();
    insert['UIID'] = poNumber;
    insert['cass_content_file'] = remoteFile;
    insert['cass_filename'] = localFileName;
    insert['admin_id'] = userId;
    //get current date time with respect to CT time
    let submissionTimeStamp = this.utils.convertUtcToCst(moment().utc().format('YYYY-MM-DD HH:mm:ss'));
    let submissionCtDate = submissionTimeStamp.format('M/D/YY');
    let submissionCtTime = submissionTimeStamp.format('hh:mm A');
    insert['date_of_payment_submission'] = submissionCtDate;
    insert['time_of_payment_submission_ct'] = submissionCtTime;

    await this.dbObjService.saveData(insert, this.logCassPaymentStatusRepository);

    const data = { message: { process_id: processId, Step: 'Step 7', message_attribute: JSON.stringify(insert), }, info: 'Log Data in DB' };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

    const deleteFileData = await this.deleteFile(path.join(filePath, localFileName), path.join(filePath, 'remote-file' + localFileName));
    if (!deleteFileData) {
      return { [responseErrorTag]: 'something went wrong' };
    }
  }

  async getUserPurchaseOrder(poNumber, ids) {
    return (await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(
      this.userPurchaseOrderRepository,
      [{ table: 'user_purchase_order_line', joinColumn: 'purchase_order_id', mainTableColumn: 'id', }],
      [{ column: 'seller_po_number', operator: '=', value: poNumber, }, { column: 'is_active', operator: '=', value: true, }, { column: 'is_active', operator: '=', value: true, table: 'user_purchase_order_line' }, { column: 'order_status_id', operator: 'IN', value: ids, table: 'user_purchase_order_line' }],
      { selectFields: ['table1.*', 'user_purchase_order_line.id AS purchase_order_line_id', 'user_purchase_order_line.payment_token AS payment_token',] },
      null, null))?.[0];
  }

  async getUserDataById(id) {
    return this.dbObjService.findOne(this.userRepository, 'id', id)
  }

  async checkAndCreatePublicForlder() {
    return new Promise((resolve) => {
      fs.access(path.join(process.cwd(), 'public'), (err) => {
        if (err) {
          fs.mkdir(path.join(process.cwd(), 'public'), (err) => {
            if (err) {
              resolve(false);
              this.utils.logInfo(err);
            } else {
              resolve(true);
            }
          });
        } else {
          resolve(true);
        }
      });
    });
  }

  async openFile(filePath, readMode = "w") {
    return new Promise((resolve) => {
      fs.open(filePath, readMode, (err, fileData) => {
        if (err) {
          resolve(null);
        } else {
          resolve(fileData);
        }
      },
      );
    });
  }

  async readFile(filePath: string) {
    return new Promise((resolve) => {
      fs.readFile(filePath, (err, data) => {
        if (err) {
          resolve(null);
        } else if (data) {
          resolve(data.toString());
        }
      });
    });
  }

  async writeFile(filePath: string, data: string) {
    return new Promise((resolve) => {
      fs.writeFile(filePath, data, (err) => {
        if (err) {
          resolve(null);
        } else {
          resolve(true);
        }
      },
      );
    });
  }

  async writeInCsvFile(filePath: string, data: any[], delimiter = "|") {
    return new Promise((resolve, reject) => {
      stringify(data, { delimiter }, (err, output) => {
        if (err) {
          resolve(null);
        } else {
          fs.writeFile(
            filePath,
            output,
            (err) => {
              if (err) {
                reject(err);
                throw err;
              } else {
                resolve(true);
              }
            },
          );
        }
      },
      );
    });
  }

  async deleteFile(filePath1: string, filePath2: string) {
    return new Promise((resolve, reject) => {
      fs.unlink(filePath1, (err) => {
        if (err) {
          resolve(false);
          this.utils.logInfo(err);
        } else {
          fs.unlink(filePath2, (err2) => {
            if (err2) {
              resolve(false);
              this.utils.logInfo(err);
            } else {
              resolve(true);
            }
          },
          );
        }
      });
    });
  }

  async getOneReferenceDataOrderId(value: string) {
    return (await this.dbObjService.findOne(this.referenceDataOrderStatusRepository, "value", value))?.id;
  }

  async getLogCassPaymentStatusData(poNumber: string) {
    const mapper = { selectFields: ['is_active', 'local_file', 'order_ledger_id', 'routing_number', 'status', 'time_of_payment_submission_ct', 'time_stamp', 'transfer_type', 'truevault_reference_document_id', 'UIID', 'user_id', 'user_id_login_date', 'user_login_time_ct', 'version'] };
    return await this.dbObjService.findManyByMultipleWhereAndSelect(this.logCassPaymentStatusRepository, { UIID: poNumber, status: Constants.UPLOADED }, mapper, 'created_date', 'ASC',);
  }

  async getWithdrawlTypeAmountType(paymentMethod, withdrawlAmount, poNumber, userId, sellerPaymentMethod = null) {
    let insert: any = {};
    if (withdrawlAmount > 0) {

      let cassTransaction = await this.dbObjService.findOne(this.cassTransactionView, "PO_NUMBER", poNumber, false);

      if (!cassTransaction) {
        return null;
      }

      switch (paymentMethod) {
        case Constants.BOX_1_SELLER_PAYMENT:
          insert.withdrawl_type = Constants.CASS_SELLER_PAYOUT;
          insert.amount_type = 'withdrawal';
          insert.withdraw_flag = true;
          //get if seller is present in bryzos and cass
          const sellerId = await this.checkSellerExistsByPoNumber(poNumber, userId);
          insert.destination_account_id = sellerId;
          insert.disbursement_method = await this.utils.getCassCode(sellerPaymentMethod);
          insert.seller_company_name = cassTransaction.SELLER_MAIN_COMPANY_NAME;
          break;
        case Constants.BOX_2_BRYZOS_HOLDINGS:
          insert.withdrawl_type = Constants.CASS_BRYZOS_FEE_PAYOUT;
          insert.amount_type = 'withdrawal';
          insert.withdraw_flag = true;
          insert.destination_account_id = insert.seller_company_name = await this.cassApis.getCassDefaultAccount(Constants.CASS_TRANSFER_BRYZOS_HOLDINGS, Constants.SELLER, userId);
          insert.disbursement_method = process.env.DISBURSEMENT_METHOD_FOR_DEFAULT_ACCOUNTS_TRANSFER;
          insert.seller_company_name = insert.destination_account_id;
          break;
        case Constants.BOX_3_SALES_TAX:
          insert.withdrawl_type = Constants.CASS_SALES_TAX_PAYOUT;
          insert.amount_type = 'taxation_amount_paid';
          insert.withdraw_flag = true;
          insert.destination_account_id = insert.seller_company_name = await this.cassApis.getCassDefaultAccount(Constants.CASS_TRANSFER_SALES_TAX_ID, Constants.SELLER, userId);
          insert.disbursement_method = process.env.DISBURSEMENT_METHOD_FOR_DEFAULT_ACCOUNTS_TRANSFER;
          insert.seller_company_name = insert.destination_account_id;
          break;
        case Constants.BOX_4_BNPL:
          insert.buyer_payment_type = Constants.CASS_BNPL_PAYMENT;
          insert.amount_type = 'buyer_paid';
          insert.withdraw_flag = true;
          //no cass check here as we add buyer's account and routing number for this transfer
          insert.destination_account_id = insert.seller_company_name = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_CASS, Constants.CASS_TRANSFER_BNPL);
          insert.disbursement_method = process.env.DISBURSEMENT_METHOD_FOR_LOAN_ACCOUNT_TRANSFER;
          insert.seller_company_name = cassTransaction.BUYER_MAIN_COMPANY_NAME;
          break;
        default:
          insert.withdrawl_type = 'CASS_TRANSFER';
          insert.amount_type = 'withdrawal';
          insert.withdraw_flag = false;
          insert.destination_account_id = null;
          insert.disbursement_method = null;
          insert.seller_company_name = null;
      }

    }
    return insert;
  }

  async getCassBuyer(buyerId, userId) {
    let response = null;
    if (buyerId) {
      //get buyer info
      let userData = (await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.userRepository,
        [{ table: 'user_buying_preference', joinColumn: 'user_id', mainTableColumn: 'id' }],
        [{ column: 'id', operator: '=', value: buyerId }, { column: 'is_active', operator: '=', value: true }, { column: 'is_active', operator: '=', value: true, table: 'user_buying_preference' }],
        { selectFields: ['table1.*', 'user_buying_preference.company_name AS company_name', 'user_buying_preference.user_id AS user_id'] },
        null, null))?.[0];

      if (userData) {
        let cassUniqueIdentifier = null;
        let cassUniqueIdentifierPrefix = (await this.dbObjService.findOneByMultipleWhere(this.referenceDataGeneralSettingsRepository, { name: Constants.CASS_BUYER_UNIQUE_IDENTIFIER_PREFIX, is_active: true }))?.value;
        let cassUniqueIdentifierTag = (await this.dbObjService.findOneByMultipleWhere(this.referenceDataGeneralSettingsRepository, { name: Constants.CASS_UNIQUE_IDENTIFIER_TAG, is_active: true }))?.value;

        if (cassUniqueIdentifierPrefix && cassUniqueIdentifierTag) {
          cassUniqueIdentifier = cassUniqueIdentifierPrefix + userData[cassUniqueIdentifierTag];
        } else {
          return response
        };

        //check buyer exists in CASS
        let cassBuyerResult = await this.cassApis.checkUserExistsInCass(cassUniqueIdentifier, Constants.BUYER, userId, false, userData.id);
        if (!cassBuyerResult) {
          //create buyer in cass
          let cassBuyerCreation = this.cassApis.createCassBuyer(buyerId, userId);
          response = cassBuyerCreation;
        } else {
          response = cassBuyerResult;
        }
      }
    }
    return response;
  }

  async getBnplTransferDetails(order, userId) {
    let response: any = {};
    if (order && order.payment_method == Constants.PAYMENT_METHOD_BRYZOS_PAY) {
      response = await this.balance.getTransactionInfo(order.payment_token, userId);
    }
    return response;
  }

  async checkSellerExistsByPoNumber(poNumber, userId) {
    let response = null;
    let sellerData = await this.dbObjService.findOne(this.userPurchaseOrderRepository, "seller_po_number", poNumber);

    const sellerId = sellerData?.seller_id;
    if (!sellerId) {
      return null;
    }

    //get company's admin id
    let sellersId = await this.getSellerPaymentData(sellerId);

    if (!sellersId) {
      return response;
    }
    //get cass unique identifier
    let logCassSupplier = await this.dbObjService.findOneByMultipleWhere(this.logCassSupplierRepository, { "user_id": sellersId });

    //check seller exists in CASS
    if (logCassSupplier) {
      let cassSellerId = logCassSupplier.cass_unique_id;
      let userType = Constants.SELLER;
      response = await this.cassApis.checkUserExistsInCass(cassSellerId, userType, userId, false, sellersId);
    }

    return response;
  }

  async getSellerPaymentData(userId) {
    let id = null;

    if (userId) {
      let sellerPaymentMethods = await this.utils.getPaymentMethodId(Constants.SELLER,);

      let paymentData = (await this.dbObjService.findManyByWhereAndWhereIn(this.paymentInfoRepository, { user_id: userId }, "pgpm_mapping_id", sellerPaymentMethods))?.[0];

      if (paymentData?.user_id) {
        id = paymentData.user_id;
      }
    }
    return id;
  }

  getNextVersionCode(char: string) {
    char = char.toUpperCase();

    if (same(char, 'Z')) {
      let txt = '';
      let length = char.length;

      while (length--) {
        txt += 'A';
      }
      return txt + 'A';
    } else {
      let p = "";
      let q = "";
      if (char.length > 1) {
        p = char.substring(0, char.length - 1);
        q = String.fromCharCode(p.slice(-1).charCodeAt(0));
      }
      let l = char.slice(-1).charCodeAt(0);
      let z = nextLetter(l);
      return z === 'A' ? (p.slice(0, -1) + nextLetter(q.slice(-1).charCodeAt(0)) + z) : (p + z);
    }

    function nextLetter(length: number) {
      return length < 90 ? String.fromCharCode(length + 1) : 'A';
    }

    function same(str: string, char: string) {
      var length = str.length;
      while (length--) {
        if (str[length] !== char) {
          return false;
        }
      }
      return true;
    }
  }

  async getCassSellerSetup() {
    let response = null;

    response = await this.getCassSellerPayment();

    return response;
  }

  async getCassSellerPayment() {
    let paymentData: any = null;

    // Database query to find bryzos sellers
    const paymentDataLeftJoins = [
      { table: "reference_data_pgpm_mapping", joinColumn: "id", mainTableColumn: "pgpm_mapping_id" },
      { table: "user_selling_preference", joinColumn: "user_id", mainTableColumn: "user_id" },
      { table: "log_cass_supplier", joinColumn: "user_id", mainTableColumn: "user_id", joinTable: "user_selling_preference" }
    ]
    const paymentDataConditions = [
      { column: "is_active", operator: "=", value: true, table: "reference_data_pgpm_mapping" },
      { column: "is_active", operator: "=", value: true },
      { column: "is_active", operator: "=", value: true, table: "log_cass_supplier" },
      { column: "is_active", operator: "=", value: true, table: "user_selling_preference" }
    ]
    const mapperFields = {
      selectFields: ['log_cass_supplier.cass_unique_id as cass_unique_id', 'table1.account_number as account_number', 'table1.routing_number as routing_number', 'user_selling_preference.company_name as company_name', 'CONCAT(user_selling_preference.first_name, " ", user_selling_preference.last_name) as seller_name'
      ]
    }
    const orderBy = { 'table1.created_date': 'DESC' };

    paymentData = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.userArPaymentInfoRepository, paymentDataLeftJoins, paymentDataConditions, mapperFields, orderBy);

    // Database query to find ad-hoc sellers
    const mapper = [
      'unique_identifier as cass_unique_id', 'account_number', 'routing_number', 'seller_company as company_name'
    ];

    let orderBy1 = { 'created_date': 'DESC' };

    const adhocSellerResponse = await this.dbObjService.findAllWithCustomSelectFields(this.cassAdhocSellerRepository, mapper, orderBy1);
    
    if(adhocSellerResponse && adhocSellerResponse.length > 0) {
      adhocSellerResponse.map(item => {
        item.seller_name = null;
        item.email = null;
        return item;
      });
    }
    
   
    if((!paymentData || paymentData.length === 0) && (!adhocSellerResponse || adhocSellerResponse.length === 0 )){
      return {"error_message": "No Data!"}
    }
   
    // Extract company names from paymentData array and remove duplicates
    const bryzosCompanyNames = paymentData.map(item => item.company_name);
    const adhocCompanyNames = adhocSellerResponse.map(item => item.company_name);
    const combinedCompanyNames = [...new Set([...bryzosCompanyNames, ...adhocCompanyNames])];

    // console.log(combinedCompanyNames);

    const distinctTransactions = await this.getDistinctCompanyTransactions( this.cassTransactionEmailRepository, true, combinedCompanyNames );


    // Create a map for distinct transactions
    const transactionMap = distinctTransactions.reduce((acc, transaction) => {
        if (!acc[transaction.seller_company_name]) {
            acc[transaction.seller_company_name] = new Set();
        }
        acc[transaction.seller_company_name].add(transaction.to_email);
        return acc;
    }, {});

    // Update adhocSellerResponse with email information from distinct transactions
    adhocSellerResponse.forEach(adhocSeller => {
        const emails = transactionMap[adhocSeller.company_name] || new Set();
        adhocSeller.email = Array.from(emails).join(';');
        if(adhocSeller.email == ''){
          adhocSeller.email = null;
        }
    });

    // Initialize email field in paymentData items
    paymentData.forEach(item => {
        item.email = null;
    });

    // Update paymentData with email information from distinct transactions
    paymentData.forEach(payment => {
        const emails = transactionMap[payment.company_name] || new Set();
        payment.email = Array.from(emails).join(';');
        if(payment.email == ''){
          payment.email = null;
        }
    });

    let responseData = [{ "adhoc_seller": adhocSellerResponse, "bryzos_seller": paymentData }];

    return responseData;
  }

  async getDistinctCompanyTransactions( entityClass: Repository<any>, isActive: boolean, sellerCompanyNames: string[]) {
    return await entityClass
      .createQueryBuilder('t1')
      .select([
        't1.seller_company_name as seller_company_name',
        't1.to_email as to_email',
        't1.po_number as po_number',
        't1.transaction_type as transaction_type',
        't1.created_date as created_date',
      ])
      .innerJoin(
        subQuery =>
          subQuery
            .select([
              'seller_company_name',
              'MAX(created_date) as latest_date',
            ])
            .from(entityClass.metadata.target, 't2')
            .where('is_active = :isActive', { isActive })
            .andWhere('seller_company_name IN (:...sellerCompanyNames)', {
              sellerCompanyNames,
            })
            // .andWhere('transaction_type = :transactionType', {
            //   transactionType,
            // })
            .groupBy('seller_company_name'),
        't2',
        't1.seller_company_name = t2.seller_company_name AND t1.created_date = t2.latest_date',
      )
      .where('t1.is_active = :isActive', { isActive })
      // .andWhere('t1.transaction_type = :transactionType', { transactionType })
      .getRawMany();
  }
  
  async createCassTransaction(payload: CassTransactionDto, adminId: number) {
    return this.lock.acquire('lockKey', async () => {
      try {
        return await this.uploadCassTransactionFile(payload, adminId);
      } catch (error) {
        console.error(error);
        return { 'error_message': 'Something went wrong!' };
      }
    });
  }

  async uploadCassTransactionFile(payload: CassTransactionDto, adminId: number) {
    let response = {};
    let data: any = {};
    let insert: any = {};
    let insertLog: any = {};
    let amount = payload.amount;
    let poNumber = payload.po_number;

    if (amount <= 0) {
      return { [responseErrorTag]: 'Enter amount greater then zero.' };
    }

    let cassTransactionPo = await this.dbObjService.findOne(this.cassTransactionView, "PO_NUMBER", poNumber, false);

    if (!cassTransactionPo) {
      return { [responseErrorTag]: 'PO not found' };
    }

    let cassUniqueId = payload.cass_unique_id;
    // let statementDescriptor = payload.statement_descriptor.substring(0, 80); // For 1000 record type  "Remarks" has string limit of 80.
    let statementDescriptor = payload.statement_descriptor; //For 2201 record type (CASS_PAYMENT_RECORD_TYPE), 5th record has string limit of 200.
    let sellerPaymentMethod = process.env.CASS_ADHOC_SELLER_PAYMENT_METHOD;
    let internalNote = payload?.internal_note !== undefined ? payload.internal_note : null;
    let cassDefaultBuyer = payload.is_cass_default_buyer;
    let sellerInvoiceNumber = payload?.seller_invoice_number ?? null;
    let recipientEmail = null;
    if (payload?.to_email) {
      const emails = payload.to_email.split(";").map(item => item.trim()).join(";"); 
      recipientEmail = await this.emailValidation(emails);
      if (recipientEmail == "") {
        return { error_message: "Some of the entered email's are not valid please check" };
      }
    }
    let paymentMethod = Constants.BOX_1_SELLER_PAYMENT;
    payload['payment_method'] = paymentMethod;
    insert.transfer_type = Constants.BOX_5_ADHOC_PAYMENT;
    let userType = Constants.SELLER;
    let sellerId = null;
    let isAdhocUser = null;
    let cassBuyer = null;
    insert.amount = amount;
    insert.seller_invoice_number = sellerInvoiceNumber;
    let date = moment().utc().format('YYYYMMDD');

    if (!(await this.checkAndCreatePublicForlder())) {
      return { [responseErrorTag]: 'Unable to create public folder in root directory' };
    }

    let filePath: any = path.join(process.cwd(), 'public');
    let filenameLogging = 'cass.service.ts';
    let logEvent = 'Upload 0150 file in CASS';
    let referenceId = adminId;
    let processId = poNumber;
    let remoteFile = null;
    let status = null;

    let userData: any = await this.getUserDataById(adminId);
    if (userData) {
      insert.user_id = userData.email_id;
      let timeStamp = userData.time_stamp;
      let ctTimeStamp = this.utils.convertUtcToCst(timeStamp);
      let ctDate = ctTimeStamp.format('M/D/YY');
      let ctTime = ctTimeStamp.format('hh:mm A');
      insert.user_id_login_date = ctDate;
      insert.user_login_time_ct = ctTime;
    }

    let resolvedStatusId = await this.getOneReferenceDataOrderId(Constants.ORDER_STATUS_RESOLVED);
    let activeStatusId = await this.getOneReferenceDataOrderId(Constants.ORDER_STATUS_ACTIVE);
    let orderCompletedId = await this.getOneReferenceDataOrderId(Constants.ORDER_STATUS_COMPLETED);

    const orderDetails = await this.getUserPurchaseOrder(poNumber, [activeStatusId, resolvedStatusId, orderCompletedId]);

    let logCassPaymentStatus = await this.getLogCassPaymentStatusData(poNumber);

    let version: any = 'A';
    if (logCassPaymentStatus?.length > 0) {
      let count = logCassPaymentStatus.length;
      version = logCassPaymentStatus[count - 1].version;
      version = this.getNextVersionCode(version);
    }
    let poUniqueIdentifier = `${poNumber}-${version}`;
    processId = poNumber;

    if (process.env.ENVIRONMENT === 'DEMO' && process.env.CASS_PO_UNIQUE_IDENTIFIER_PREFIX) {
      poUniqueIdentifier = `${process.env.CASS_PO_UNIQUE_IDENTIFIER_PREFIX}-${poNumber}-${version}`;
      processId = poNumber;
    }

    data = { message: { Step: 'Step 0', process_id: processId, message_attribute: JSON.stringify(payload) }, info: 'Data sent via UI' };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

    if (!sellerPaymentMethod && paymentMethod === Constants.BOX_1_SELLER_PAYMENT) {
      return { [responseErrorTag]: 'No seller payment selected' };
    }

    if (sellerPaymentMethod && sellerPaymentMethod === Constants.CHECK_PAYMENT_METHOD && paymentMethod === Constants.BOX_1_SELLER_PAYMENT) {
      return { [responseErrorTag]: 'Seller check payment method not allowed' };
    }

    //get withdrawal details
    let withdrawls: any = {};
    withdrawls.withdrawl_type = Constants.CASS_SELLER_PAYOUT;
    withdrawls.amount_type = 'withdrawal';
    withdrawls.withdraw_flag = true;
    withdrawls.destination_account_id = cassUniqueId;

    //get if seller is present in bryzos adhoc
    let seller_company_name = null;
    let adhocCassSupplier = await this.dbObjService.findOne(this.cassAdhocSellerRepository, "unique_identifier", cassUniqueId);

    //check seller exists in CASS
    if (adhocCassSupplier) {
      sellerId = adhocCassSupplier.id
      isAdhocUser = true;
      insert.seller_company_name = adhocCassSupplier.seller_company;
      insertLog.seller_company_name = adhocCassSupplier.seller_company;
    }else{
      //get if seller is present in bryzos
      let cassSupplier = await this.dbObjService.findOne(this.logCassSupplierRepository, "cass_unique_id", cassUniqueId);
      if (cassSupplier) {
        sellerId = cassSupplier.user_id;
        const sellerPaymentInfo = await this.getSellerPayment(sellerId);
        seller_company_name = sellerPaymentInfo ? sellerPaymentInfo[0]?.company_name : null;
        sellerPaymentMethod = sellerPaymentInfo ? sellerPaymentInfo[0]?.payment_method : null;
        let sellerData = await this.dbObjService.findOne(this.userSellingPreferenceRepository, "user_id", sellerId );
        if(sellerData){
          insert.seller_company_name = sellerData.company_name;
          insertLog.seller_company_name = sellerData.company_name;

        }
      }else{
        return { [responseErrorTag]: 'User does not exist!' };
      }
    }

    //get if seller is present in cass
    let cassResponse = await this.cassApis.checkUserExistsInCass(cassUniqueId, userType, adminId, false, sellerId, isAdhocUser);

    if (!cassResponse)
      return { [responseErrorTag]: 'User does not exist in cass!' };

    insert.destination_account_id = cassUniqueId;
    insert.disbursement_method = withdrawls.disbursement_method = await this.utils.getCassCode(sellerPaymentMethod);


    data = { message: { process_id: processId, Step: 'Step 1', message_attribute: JSON.stringify(withdrawls) }, info: 'Data from getWithdrawlTypeAmountType function', };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

    if (!withdrawls) {
      return { [responseErrorTag]: 'PO not found' };
    }

    let disbursementMethod = withdrawls.disbursement_method;
    if (!disbursementMethod) {
      return { [responseErrorTag]: 'No seller payment found' };
    }

    let destinationAccountId = withdrawls.destination_account_id;
    if (!destinationAccountId) {
      return { [responseErrorTag]: 'No destination account found' };
    }

    data = { message: { processId: processId, Step: 'Step 2', message_attribute: JSON.stringify({ amount: amount, paymentMethod: paymentMethod, disbursement_method: withdrawls.disbursement_method, statementDescriptor: statementDescriptor, destination_account_id: withdrawls.destination_account_id }) }, info: 'Inside uploadSmtpCass' };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data,);

    //get buyer from cass
    if (cassDefaultBuyer) {
      cassBuyer = await this.cassApis.getCassDefaultAccount(Constants.CASS_BUYER_ID, Constants.BUYER, adminId);
    } else {
      cassBuyer = await this.getCassBuyer(orderDetails.buyer_id, adminId);
      if (cassBuyer?.err_message) {
        return cassBuyer;
      } else if (!cassBuyer) {
        return { [responseErrorTag]: 'No buyer account found' };
      }
    }

    if (!cassBuyer) {
      return { [responseErrorTag]: 'Buyer account not found!' };
    }

    //set drawndown method : this is same for all 4 boxes
    let drawdownMethod = process.env.DEFAULT_DRAWDOWN_METHOD;
    const drawdownAmount = '0.00';

    //CASS file name
    let localFileName = `${process.env.CASS_ID}${process.env.CASS_TARGET_ENV}${process.env.CASS_ORIGINAL_FILE_TYPE}${moment().utc().format('YYYYMMDDhhmmss')}.csv`;

    let cassFile = [`${Constants.CASS_FILE_RECORD_TYPE},${localFileName}`];

    let cassTransaction = [`${Constants.CASS_TRANSACTION_RECORD_TYPE},${poUniqueIdentifier},${poUniqueIdentifier},${date},${cassBuyer},${destinationAccountId},${drawdownAmount},${drawdownMethod},USD,${date},,${amount},${disbursementMethod},USD,${date},,,,`];

    // let list = [`${Constants.CASS_PAYMENT_RECORD_TYPE},${poUniqueIdentifier},${poUniqueIdentifier},${date},,${amount},USD,${statementDescriptor}`];

    let list = [`${Constants.CASS_PAYMENT_RECORD_TYPE},,,,${statementDescriptor},,,,,,,,,,`];

    let file: any = await this.openFile(path.join(filePath, localFileName));

    const _cassFile = [];
    cassFile.forEach((line1) => {
      const items = (line1.toString()).split(',');
      _cassFile.push(...items);
    });

    const _cassTransaction = [];
    cassTransaction.forEach((line2) => {
      const items = (line2.toString()).split(',');
      _cassTransaction.push(...items);
    });

    const _list = [];
    list.forEach((line3) => {
      const items = (line3.toString()).split(',');
      _list.push(...items);
    });

    const writeInCsvFileData = [_cassFile, _cassTransaction, _list];

    const csvFileData = await this.writeInCsvFile(path.join(filePath, localFileName), writeInCsvFileData);
    if (!csvFileData) {
      return { [responseErrorTag]: 'Error in csv file creation' };
    }

    let localFile: any = await this.readFile(path.join(filePath, localFileName));
    //encode data to save in loggly
    let encodedFileContent = await this.utils.getEncodedData(localFile);

    data = { message: { process_id: processId, Step: 'Step 3', message_attribute: JSON.stringify({ file_name: localFileName, file_content: encodedFileContent }) }, info: 'Local File Saved without processing' };
    this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

    insert['local_file'] = localFile;

    //let cassPassword: any = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_CASS_SFTP, Constants.PASSWORD);

    // login via sftp
    const sftp = new Client();
    try {
      await sftp.connect({ host: process.env.CASS_IP, port: 22, username: process.env.CASS_USER, password: this.cassPassword });

      //check if folder is present on remote server
      let targetDir = '';
      if (process.env.CASS_ENV == Constants.LOCAL_ENV) {
        targetDir = (await sftp.exists(Constants.CASS_TEST_DIR)) === 'd' ? Constants.CASS_TEST_DIR : '';
      } else if (process.env.CASS_ENV == Constants.PROD_ENV) {
        targetDir = (await sftp.exists(Constants.CASS_PROD_DIR)) === 'd' ? Constants.CASS_PROD_DIR : '';
      }

      let serverFileName = localFileName;
      insert['status'] = Constants.NOT_UPLOADED;
      if (targetDir) {
        data = { message: { process_id: processId, Step: 'Step 5', message_attribute: JSON.stringify(serverFileName) }, info: 'CASS Server file, Start Uploading file...' };
        this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

        //with SFTP::SOURCE_LOCAL_FILE it uploads a file and without it it uploads a string.
        file = await sftp.put(path.join(filePath, localFileName), `/${targetDir}/${serverFileName}`);
        if (file) {
          let dst = fs.createWriteStream(path.join(filePath, 'remote-file' + localFileName));
          await sftp.get(`/${targetDir}/${serverFileName}`, dst);

          remoteFile = await this.readFile(path.join(filePath, 'remote-file' + localFileName));
          insert['internal_note'] = internalNote;
          if (remoteFile) {
            response = remoteFile;
            data = { message: { process_id: processId, Step: 'Step 6', message_attribute: JSON.stringify(serverFileName) }, info: 'File Uploaded Successfully!', };
            this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

            insert['status'] = status = Constants.UPLOADED;
            insert['version'] = version;

            //insert in order_ledger
            let ledgerId = uuid4();
            let orderLedgerInsert = { id: ledgerId, [withdrawls.amount_type]: amount, purchase_order_line_id: orderDetails.purchase_order_line_id, transaction_type: 'CASS_ADHOC_TRANSFER' };
            if (withdrawls['withdrawl_type']) {
              orderLedgerInsert['withdrawal_type'] = withdrawls['withdrawl_type'];
            }

            await this.dbObjService.saveData(orderLedgerInsert, this.userPurchaseOrderLedgerRepository);
            insert['order_ledger_id'] = ledgerId;
            //insert into order_processing_history
            await this.dbObjService.saveData({ id: uuid4(), po_number: poNumber, price: amount, purchase_order_line_id: orderDetails.purchase_order_line_id, event: 'CASS_' + paymentMethod.toUpperCase(), json: JSON.stringify(payload) }, this.userPurchaseOrderProcessingHistoryRepository);
          } else {
            insert['exception'] = 'File get error';
            response = { [responseErrorTag]: 'Something Went Wrong!' };
          }
        } else {
          data = { message: { process_id: processId, Step: 'Error 2', message_attribute: JSON.stringify(serverFileName), }, info: 'File Upload Exception!' };
          this.utils.createLogData(filenameLogging, referenceId, logEvent, data,);
          insert['exception'] = Constants.FILE_UPLOAD_EXCEPTION;
          response = { [responseErrorTag]: 'Something Went Wrong!' };
        }
      } else {
        data = { message: { process_id: processId, Step: 'Error 3', message_attribute: JSON.stringify(serverFileName), }, info: 'Target Directory not found!', };
        this.utils.createLogData(filenameLogging, referenceId, logEvent, data,);
        insert['exception'] = Constants.TARGET_DIR_NOT_FOUND;
        response = { [responseErrorTag]: 'Something Went Wrong!' };
      }
      await this.gotoInsertStatusInDBCode(insert, payload, orderDetails, poNumber, filePath, remoteFile, localFileName, adminId, processId, filenameLogging, referenceId, logEvent, isAdhocUser);
   
      // if(recipientEmail && recipientEmail != ""){
        const cassTransaction = await this.dbObjService.findOne(this.logCassPaymentStatusRepository, "cass_filename", localFileName );
        insertLog.po_number = poNumber;
        insertLog.transaction_type = Constants.BOX_5_ADHOC_PAYMENT;
        insertLog.amount = amount;
        insertLog.to_email = recipientEmail;
        insertLog.cass_payment_id = cassTransaction ? cassTransaction.id : null;
        insertLog.event = Constants.CASS_TRANSACTION_FUNDS_ON_WAY;
        insertLog.status = status;
        insertLog.admin_id = adminId;

       await this.dbObjService.saveData(insertLog, this.cassTransactionEmailRepository);
      // }
   
      response = "Successfully Uploaded";
      return response;
    } catch (e) {
      console.log("send email error : ", e);
      console.log("send email error message : ", e?.message);
      console.log("send email error stack: ", e?.stack);
      data = { message: { process_id: processId, Step: 'Error 1', message_attribute: JSON.stringify(e?.message) }, info: 'SFTP Login Failed' };
      this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

      insert['exception'] = Constants.LOGIN_FAILED;
      response = { [responseErrorTag]: 'Remote server login failed' };
      // goto Insert_Status_In_DB;
      await this.gotoInsertStatusInDBCode(insert, payload, orderDetails, poNumber, filePath, remoteFile, localFileName, adminId, processId, filenameLogging, referenceId, logEvent, isAdhocUser);
      return response;
    } finally {
      sftp.end();
    }
  };

  async cassAdhocSellerSetup(data: CassAdhocSellerSetupDto, adminId) {
    let response = null;
    // save seller in DB
    if (data) {
      const insertSellerData = {
        unique_identifier: data.cass_unique_id,
        seller_company: data.seller_company,
        account_number: data.account_number,
        routing_number: data.routing_number,
        truevault_reference_document_id: data.truevault_document_id,
        created_by: adminId
      }
      const sellerData = await this.dbObjService.saveData(insertSellerData, this.cassAdhocSellerRepository);

      if (sellerData) {
        response = 'Seller created successfully';
      }
    }
    return response;
  }

  //TODO: Need to move this to library
  //not in use
  async getUnequalTransactionData(page: number, limit: number, condition: any, doGroupBy: boolean, orderBy: any) {
    const paginationApplied = page > 0 && limit > 0;
    const data = await this.cassMappingProbalePosViewRepository
      .createQueryBuilder("table1")
      .select('*')
    if (condition?.operator == "LIKE") {
      if (condition.value) {
        let columns = "";
        const len = condition.column?.length ?? 0;
        condition.column.forEach((col, i) => {
          columns += `IFNULL(\`table1\`.\`${col}\`, '')${i < (len - 1) ? "," : ""}`;
        });
        data.andWhere(`CONCAT(${columns}) LIKE :value`, { value: `%${condition.value}%` });
      }
    }
    data.andWhere(`(processed_amount != mapped_pos_buyer_total OR (processed_amount IS NULL AND mapped_pos_buyer_total IS NOT NULL) OR (processed_amount IS NOT NULL AND mapped_pos_buyer_total IS NULL) OR (processed_amount IS NULL AND mapped_pos_buyer_total IS NULL))`)
    data.groupBy(`table1.id`);
    if (orderBy) {
      data.orderBy(orderBy);
    }
    if (doGroupBy && paginationApplied) {
      const { items, meta } = await paginateRaw(data, { page: page, limit: limit });
      return { items, meta }
    }
    return await data.getRawMany();
  }

  async getProbablePos(options: { page: number, limit: number, search: string }, showMatchedTransaction: boolean) {
    const response = [];

    const paginationApplied = options.page > 0 && options.limit > 0;
    const conditions: any = [];
    if (paginationApplied && options.search && options.search.trim()!="") {
      conditions.push({ column: ['cass_filename','processed_amount','mapped_orders'], operator: 'LIKE', value: options.search });
      
    }
    if(!showMatchedTransaction){
      conditions.push({customCondition:`(processed_amount != mapped_pos_buyer_total OR (processed_amount IS NULL AND mapped_pos_buyer_total IS NOT NULL) OR (processed_amount IS NOT NULL AND mapped_pos_buyer_total IS NULL) OR (processed_amount IS NULL AND mapped_pos_buyer_total IS NULL))`})
    }
    let meta;
    const orderBy = {"created_date":"DESC", "id":"DESC"};
    
    let cassProbableOrders = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.cassMappingProbalePosViewRepository, [], conditions,  "*",orderBy, "table1.id", options) ;
    if (paginationApplied) {
      if (cassProbableOrders?.items?.length) {
        meta = cassProbableOrders.meta;
        cassProbableOrders = cassProbableOrders.items;
      } else {
        options.page=1; 
        cassProbableOrders = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.cassMappingProbalePosViewRepository, [], conditions,  "*",orderBy, "table1.id", options) ;
        meta = cassProbableOrders.meta;
        cassProbableOrders = cassProbableOrders.items;
      }
    } else {
      if (!cassProbableOrders?.length) {
        return [];
      }
    }
    
    const ids = cassProbableOrders.map(obj => obj.id);
    if(!ids.length){return []}
    const conditions1 = [
      { column: 'id', operator: 'IN', value: ids },
    ];
    let cassProbableOrdersAll = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.cassMappingProbalePosViewRepository, [], conditions1, null, null, null);
    if (!cassProbableOrdersAll?.length) {
      return [];
    }

    // Use an object to aggregate orders by id
    const ordersObject = {};

    for (const order of cassProbableOrders) {
      // Initialize po_numbers if not already done
      if (!ordersObject[order.id]) {
        ordersObject[order.id] = {
          id: order.id,
          processed_amount : order.processed_amount,
          cass_filename : order.cass_filename,
          mapped_pos_buyer_total : order.mapped_pos_buyer_total,
          payment_type : order.financial_operation_code,
          mapped_orders : order.mapped_orders,
          po_numbers : []
        };

        if (order.total_po_price) {
          // Aggregate po_numbers
          ordersObject[order.id].po_numbers = cassProbableOrdersAll.map(obj => {
            if (obj.id === order.id && obj.total_po_price) {
              return {
                user_purchase_order_id: obj.user_purchase_order_id,
                po_number: obj.buyer_po_number,
                buyer_company_name: obj.buyer_company_name,
                buyer_name : obj.buyer_name,
                seller_name : obj.seller_name,
                buyer_total_purchase: +obj.total_po_price
              };
            }
          }).filter(Boolean)
        }
      }
    }

    // Convert the object to the desired array format
    response.push(...Object.values(ordersObject));

    if (paginationApplied) {
      return { items: response, meta: meta };
    } else {
      return response;
    }
  }

  async getPurchaseOrders() {
    let response = null;

    let purchaseOrders = await this.getPurchaseOrderDetails();
    
    if (purchaseOrders && purchaseOrders.length > 0) {
      response = purchaseOrders.map(purchaseOrder => {
        let totalPurchase = 0;

        if (purchaseOrder.actual_buyer_po_price) {
          totalPurchase += +purchaseOrder.actual_buyer_po_price;
        } else if (purchaseOrder.buyer_po_price) {
          totalPurchase += +purchaseOrder.buyer_po_price;
        }

        if (purchaseOrder.sales_tax) {
          totalPurchase += +purchaseOrder.sales_tax;
        }

        return {
          buyer_company_name: purchaseOrder.buyer_company_name,
          po_number: purchaseOrder.po_number,
          seller_company_name: purchaseOrder.seller_company_name,
          buyer_total_purchase: +totalPurchase.toFixed(2),
          buyer_name :  purchaseOrder.buyer_first_name && purchaseOrder.buyer_last_name ? `${purchaseOrder.buyer_first_name} ${purchaseOrder.buyer_last_name}` : null,
          seller_name: purchaseOrder.seller_first_name && purchaseOrder.seller_last_name ? `${purchaseOrder.seller_first_name} ${purchaseOrder.seller_last_name}` : null,
          total_amount_received : +parseFloat(purchaseOrder.total_amount_received).toFixed(2)
        }
      });
    }
    return response;
  }

  async associateTransactionPo(payload: MapTransactionToPoDto[], adminId) {
    let response = null;
    const cassFinancialUpdateDto = [];
    const poNumbers = [];
    let amountReceivedDto = [];
    for (const item of payload) {
      const mappedOrders = item.selected_orders.filter(order => !order.is_unmapped);
      const totalMappedAmount = mappedOrders.reduce((sum, order) => sum + order.amount_received_from_buyer, 0);
      if (item.id || mappedOrders.length || adminId) {
        cassFinancialUpdateDto.push({
          id: item.id,
          mapped_orders: mappedOrders?.length ? JSON.stringify(mappedOrders) : null,
          po_mapped_by: adminId,
          mapped_pos_buyer_total: totalMappedAmount,
        }); // Merge the arrays
      }

      if (item.selected_orders?.length) {
        for (const obj of item.selected_orders){
          await this.dbObjService.markInActiveMultipleWhere(this.cassBuyerAmountReceivedPerPORepository,{buyer_po_number: obj.po_number, cass_financial_transaction_id: item.id} )
          !obj.is_unmapped ? amountReceivedDto.push({ buyer_po_number: obj.po_number, amount: obj.amount_received_from_buyer, cass_financial_transaction_id: item.id }): undefined;
          if (obj.po_number && obj.auto_close_order && !poNumbers.find(po => po === obj.po_number)) {
            poNumbers.push(obj.po_number);
          }
        };
      }
    }

    this.dbObjService.saveData(amountReceivedDto, this.cassBuyerAmountReceivedPerPORepository);
    const mapTransactionToPo = await this.dbObjService.saveData(cassFinancialUpdateDto, this.cassFinancialTransactionStatusRepository);
    let errorMessage = "";
    
    await Promise.all(poNumbers.map(async (poNumber) => {
      const response = await this.closeOrderFromCassMapping(adminId, poNumber);
      if (response?.[responseErrorTag]) {
        errorMessage += `${poNumber} mapped but ${response[responseErrorTag]}, `;
      }
    }));

    if (errorMessage) {
      return { [responseErrorTag]: errorMessage };
    }

    if (mapTransactionToPo) {
      response = "Successfully updated";
      return response;
    }

    return { [responseErrorTag]: 'Something went wrong!' };
  }

  async retrieveFundingReceivedFile(cassFilename) {

    try {
      if (!cassFilename) {
        return { [responseErrorTag]: 'Please select a file!' };
      }
      let cassS3Folder = null;
      cassS3Folder = process.env.CASS_S3_FOLDER;
      if (!cassS3Folder) {
        return { [responseErrorTag]: 'Invalid File!' };
      }
      const keyname = `${cassS3Folder}/${cassFilename}`;

      let s3FileData = await this.awsUtility.getS3File(keyname, process.env.CASS_BUCKET);
      
      if (s3FileData) {
        //convert the file data to a string:
        let fileData = '';
        for await (const chunk of s3FileData) {
          fileData += chunk.toString('utf-8');
        }
        return fileData;
      }

      return { [responseErrorTag]: 'File not found or unable to access' };
    } catch (error) {
      console.error('Error fetching file from S3:', error);
      return { [responseErrorTag]: 'Something went wrong' };
    }
  }

  async getMappedPo(searchString: string) {
    let response = null
    const conditions = [
      { column: ['po_number', 'company_name'], operator: 'LIKE', value: searchString },
      { column: 'is_active', operator: '=', value: true },
      { column: 'mapped_orders', operator: 'NOT NULL' },
    ];
    const orderBy = { 'created_date': 'DESC' };
    let mappedPo = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.cassFundingReceivedRepository, [], conditions, null, orderBy);
    if (mappedPo.length > 0)
      response = mappedPo;

    return response;
  }
  async emailValidation(emailString) {
    let validated_email = "";
    if (emailString) {
      const emails = emailString.split(';');
      for (const email of emails) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailRegex.test(email)) {
          validated_email += email + ";";
        } else {
          return false;
          break;
        }
      }
      validated_email = validated_email.replace(/;+$/, '');
    } else {
      validated_email = "";
    }
    return validated_email;
  }

  async closeOrderFromCassMapping(adminId: string, poNumber: string) {
    let response = null;

    const orderData = await this.dbObjService.findOne(this.userPurchaseOrderRepository, 'buyer_po_number', poNumber);

    if (!orderData) {
      return { [responseErrorTag]: "Order not found" };
    }
    if (!orderData.seller_id) {
      return { [responseErrorTag]: "Order is not claimed" };
    }

    const activeStatusId = (await this.dbObjService.findOne(this.referenceDataOrderStatusRepository, 'value', Constants.ACTIVE))?.id;
    const resolvedStatusId = (await this.dbObjService.findOne(this.referenceDataOrderStatusRepository, 'value', Constants.RESOLVED))?.id;
    const completedStatus = (await this.dbObjService.findOne(this.referenceDataOrderStatusRepository, 'value', Constants.ORDER_STATUS_COMPLETED))?.id;

    const orderStatusIds = [activeStatusId, resolvedStatusId, completedStatus];

    const userPurchaseOrderLine = await this.dbObjService.findManyByWhereAndWhereIn(this.userPurchaseOrderLineRepository, { "purchase_order_id": orderData.id, is_buyer_order_open: true }, "order_status_id", orderStatusIds);

    if (!userPurchaseOrderLine?.length) {
      return { [responseErrorTag]: "Unable to close po" };
    }

    let buyerPayment = 0;

    for (const orderLineData of userPurchaseOrderLine) {
      const purchaseOrderLineId = orderLineData.id;
      const referenceId = purchaseOrderLineId;

      const salesTax = orderLineData.sales_tax ? +orderLineData.sales_tax : 0;
      const orderPrice = (+orderLineData.actual_buyer_line_total) + salesTax;

      const updateOrderLine = await this.dbObjService.updateWithoutMapper({ 'is_buyer_order_open': false }, 'id', referenceId, this.userPurchaseOrderLineRepository);
      if (updateOrderLine) {
        const amount = await this.getBuyerPaidData(referenceId);

        if (amount) {
          await this.dbObjService.saveData({ purchase_order_line_id: referenceId, buyer_payment_type: Constants.PAYMENT_PAID, buyer_paid: amount }, this.userPurchaseOrderLedgerRepository);
        }

        buyerPayment += amount;
      }

      const logInsert = {
        po_number: poNumber,
        purchase_order_line_id: purchaseOrderLineId,
        order_price: orderPrice,
        response: `Line Id "${purchaseOrderLineId}" is Closed for Po "${poNumber}"`,
        admin_user_id: adminId
      };
      await this.setLogsCloseOrders(logInsert);
    }

    //replenish buyer credit
    if (buyerPayment && orderData && orderData.payment_method == Constants.PAYMENT_METHOD_BRYZOS_PAY) {
      const buyerId = orderData.buyer_id;
      const cbnplData = await this.dbObjService.findOne(this.companyBuyNowPayLaterRepository, 'user_id', buyerId);

      if (cbnplData) {
        const availableBryzosCreditLimit = Number(cbnplData.bryzos_available_credit_limit) + Number(buyerPayment);
        const outsatndingBryzosCreditLimit = Number(cbnplData.bryzos_credit_limit) - availableBryzosCreditLimit;

        const cbnpUpdateDto = {
          bryzos_available_credit_limit: availableBryzosCreditLimit,
          bryzos_outstanding_credit_limit: outsatndingBryzosCreditLimit
        };
        await this.dbObjService.updateByMultipleWhere(cbnpUpdateDto, { user_id: buyerId }, this.companyBuyNowPayLaterRepository);

        const insertCreditLogs = {
          admin_id: adminId,
          buyer_id: buyerId,
          credit_limit: +cbnplData?.bryzos_credit_limit,
          available_credit_limit: availableBryzosCreditLimit,
          outstanding_credit_limit: outsatndingBryzosCreditLimit,
          reason: Constants.CLOSE_PO_FROM_CASS_MAPPING,
          po_number: poNumber,
        }
        this.dbObjService.saveData(insertCreditLogs, this.logBryzosCreditLimitRepository);
      }
    }

    const updateUserPurchase = await this.dbObjService.updateByColumnId(this.userPurchaseOrderRepository, { is_closed_buyer: true }, "id", orderData.id);
    if (!updateUserPurchase) {
      return { [responseErrorTag]: 'Something went wrong' };
    }

    response = `${poNumber} Closed`;

    return response;
  }

  async getBuyerPaidData(purchaseOrderLineId) {
    let buyerPaidPrice = 0;
    let orderPrice = 0;
    let buyerPaidEntry = 0;

    const orderPriceResult = await this.dbObjService.findMany(this.userPurchaseOrderLedgerRepository, "purchase_order_line_id", purchaseOrderLineId);

    orderPriceResult?.forEach(obj => {
      if (+obj.extended) {
        orderPrice += parseFloat(obj.extended);
      }
      if (+obj.bryzos_fees) {
        orderPrice += parseFloat(obj.bryzos_fees);
      }
      if (+obj.sales_tax) {
        orderPrice += parseFloat(obj.sales_tax);
      }
    });

    const buyerPaymentsResult = await this.dbObjService.findManyByMultipleWhereAndSelect(
      this.userPurchaseOrderLedgerRepository,
      { purchase_order_line_id: purchaseOrderLineId, buyer_payment_type: Constants.PAYMENT_PAID },
      { selectFields: ['buyer_paid'] }
    );

    buyerPaymentsResult?.forEach(obj => {
      if (+obj.buyer_paid) {
        buyerPaidEntry += parseFloat(obj.buyer_paid)
      }
    });

    if (orderPrice > buyerPaidEntry) {
      return orderPrice - buyerPaidEntry;
    }

    return buyerPaidPrice;
  }

  async setLogsCloseOrders(insert) {
    await this.dbObjService.saveData(insert, this.adminLogCloseOrdersRepository);
  }

  async getMappedCompanies(accessToken: string) {
    let response = [];

    // mapped companies
    const mapper = { selectFields: ["distinct(mapped_company_name) as mapped_company_name"] };
    const condition = [{ column: 'mapped_company_name', operator: "NOT NULL", value: null, }, { column: 'is_active', operator: '=', value: true, }];
    const orderBy = { 'created_date': 'DESC' }
    const mappedCompaniesObjs = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.cassFundingReceivedRepository, [], condition, mapper, orderBy);
    if (mappedCompaniesObjs?.length) {
      response = mappedCompaniesObjs.map(obj => obj.mapped_company_name);
    }

    // main companies
    try {
      const securityData = await this.dbObjService.findOne(this.referenceDataSettingsRepository, "name", libConstants.SECURITY_SECRET_KEY);
      let securityHashKey: string;
      if (securityData) {
        securityHashKey = createHash('sha256').update(securityData.value).digest('hex');
      }

      const headers = {
        accesstoken: accessToken,
        origin: process.env.AD_ORIGIN,
        referer: process.env.AD_REFERER,
        security: securityHashKey,
        'User-Agent': process.env.SM_BASE_URL_USER_AGENT
      };

      const mainCompaniesObjs = (await this.httpService.axiosRef.get(`${process.env.BRYZOS_WIDGET_SERVICE_URL}/user/company`, { headers }))?.data?.data;
      if (mainCompaniesObjs?.length) {
        const mainCompanies = mainCompaniesObjs.map(obj => obj.company_name);

        mainCompanies.forEach(compName => {
          if (!response.find(companyName => compName === companyName)) {
            response.push(compName);
          }
        });
      }
    } catch (error) {
      BryzosLogger.log(BryzosLogger.log(JSON.stringify({ "Error": error }), process.env.LOGGLY_ERROR_TAG));
    }

    if (response.length) {
      return response;
    } else {
      return { [responseErrorTag]: 'No company found!' };
    }
  }

  async getPurchaseOrderDetails() {
    const result = await this.entityManager
      .createQueryBuilder('user_purchase_order', 'table1')
      .select([
        'table1.buyer_po_number AS po_number',
        'table1.seller_company_name AS seller_company_name',
        'table1.buyer_company_name AS buyer_company_name',
        'table1.buyer_po_price AS buyer_po_price',
        'table1.actual_buyer_po_price AS actual_buyer_po_price',
        'table1.sales_tax AS sales_tax',
        'table1.deposit_amount AS deposit_amount',
        'user_buying_preference.first_name AS buyer_first_name',
        'user_buying_preference.last_name AS buyer_last_name',
        'user_selling_preference.first_name AS seller_first_name',
        'user_selling_preference.last_name AS seller_last_name',
        'SUM(cass_buyer_amount_received_per_po.amount) AS total_amount_received'
      ])
      .leftJoin(
        'cass_buyer_amount_received_per_po',
        'cass_buyer_amount_received_per_po',
        'cass_buyer_amount_received_per_po.buyer_po_number = table1.buyer_po_number AND cass_buyer_amount_received_per_po.is_active = true'
      )
      .leftJoin(
        'user_buying_preference',
        'user_buying_preference',
        'user_buying_preference.user_id = table1.buyer_id AND user_buying_preference.is_active = true'
      )
      .leftJoin(
        'user_selling_preference',
        'user_selling_preference',
        'user_selling_preference.user_id = table1.seller_id AND user_selling_preference.is_active = true'
      )
      .where('table1.is_active = true')
      .andWhere('table1.seller_id IS NOT NULL')
      .andWhere('table1.is_closed_buyer = false')
      .groupBy('table1.buyer_po_number')
      .orderBy('table1.created_date', 'DESC')
      .getRawMany();

    return result;
  }
  
  async retrieveTransactionsForPeriod(payloadData: GetTransactionDto) {
    const { from_date, to_date } = payloadData;
  
    const leftJoins = [
      {
        table: 'cass_financial_transaction_status',
        joinCustomCondition: `cass_financial_transaction_status.unique_identifier = CONCAT(table1.UIID, '-', table1.version)`,
      },
    ];
  
    const conditions = [
      {
        customCondition: `STR_TO_DATE(table1.date_of_payment_submission, "%m/%d/%y") BETWEEN :fromDate AND :toDate`,
        value: { fromDate: from_date, toDate: to_date },
      },
      { column: 'is_active', operator: '=', value: true },
    ];
  
    const mapperFields = {
      selectFields: [
        'table1.UIID AS po_number',
        'CONCAT(table1.UIID, "-", table1.version) AS unique_identifier',
        'table1.transfer_type AS transfer_type',
        'table1.date_of_payment_submission AS date_of_payment_submission',
        'table1.seller_company_name AS seller_company_name',
        'table1.amount AS amount_paid',
        `CASE 
          WHEN COUNT(DISTINCT financial_operation_code) < 2 THEN null  -- Less than 2 financial operation codes
          WHEN SUM(CASE WHEN financial_operation_code = 300 THEN 1 ELSE 0 END) > 0 THEN 0  -- If 300 is present 
          ELSE 1  -- If no 300 and at least 2 financial operation codes
         END AS is_transaction_success`,
      ],
    };
  
    // Define grouping and ordering
    const groupByColumn = 'table1.UIID, table1.cass_filename, table1.transfer_type, table1.date_of_payment_submission';
    const orderBy = { 'STR_TO_DATE(table1.date_of_payment_submission, "%m/%d/%y")': 'DESC' };
  
    const result = await this.dbObjService.FindManyByMultipleConditionAndLeftJoin( this.logCassPaymentStatusRepository, leftJoins, conditions, mapperFields, orderBy, groupByColumn, );
  
    return result.map((row) => ({
      ...row,
      is_transaction_success: row.is_transaction_success !== null ? Number(row.is_transaction_success) : null,
    }));
  }
  
}
