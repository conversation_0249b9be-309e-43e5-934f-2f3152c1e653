import { AwsUtilityV3,BaseLibraryService, Constants } from "@bryzos/base-library";
import { Injectable } from "@nestjs/common";
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
// import { ExceptionService } from "./handlers/exception.service";
import { ExceptionService } from '@bryzos/extended-widget-library';

@Injectable()
export class AwsQueue {

    constructor(private readonly awsUtility:AwsUtilityV3) {}
    
  async balanceLog( balanceRequest: any, balanceResponse: any, payload: any, userId: string | null, request: string | null, tag: string )
  {
    let queueUrl = process.env.AWS_BALANCE_LOGS_QueueUrl;
    
    if (!userId) {
      userId = 'null';
    }
    if (!request) {
      request = 'null';
    }
    if (!balanceRequest) {
      balanceRequest = 'null';
    } else {
      balanceRequest = Buffer.from(balanceRequest).toString('base64');
    }
    if (!payload) {
      payload = 'null';
    } else {
      payload = Buffer.from(payload).toString('base64');
    }
    if (!balanceResponse) {
      balanceResponse = 'null';
    }
    balanceResponse = balanceResponse.substring(0, 130000);

    let currentDate = new Date();
    const utcDate = utcToZonedTime(currentDate, 'UTC');
    let date_time = format(utcDate, 'yyyy-MM-dd HH:mm:ss');

    let messageAttributes= {
        user_id: {
          DataType: 'String',
          StringValue: userId,
        },
        request: {
          DataType: 'String',
          StringValue: balanceRequest,
        },
        response: {
          DataType: 'String',
          StringValue: balanceResponse,
        },
        datetime: {
          DataType: 'String',
          StringValue: date_time,
        },
        payload: {
          DataType: 'String',
          StringValue: payload,
        },
        request_url: {
          DataType: 'String',
          StringValue: request,
        },
        tag_name: {
          DataType: 'String',
          StringValue: tag,
        },
    };

    let messageBody= 'MessageBody BALANCE_LOG';
    this.awsUtility.sendDataToSQS(queueUrl,messageAttributes, messageBody);
  }

  async sendBuyerOrderCancelEmail(poNumber:string) {
    let queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    let messageAttributes= {
        "reference_id": {
            DataType: "String",
            StringValue: poNumber
        },
        "event": {
            DataType: "String",
            StringValue: "WIDGET_AD_BUYER_CANCEL_ORDER"
        }
    };
    let  messageBody= "Send widget admin cancel buyer order";
    this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
  }

  async sendSellerOrderCancelEmail(poNumber:string) {
    let queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    let messageAttributes= {
        "reference_id": {
            DataType: "String",
            StringValue: poNumber
        },
        "event": {
            DataType: "String",
            StringValue: "WIDGET_AD_SELLER_CANCEL_ORDER"
        }
    };
    let  messageBody= "Send widget admin cancel seller order";
    this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
  }

  async sendToCloseOrderQueue(referenceId:string, buyerId:string) {

    let queueURL = process.env.AWS_WIDGET_FUND_SELLER_QUEUE_URL;
    let messageAttributes= {
        "reference_id": {
            DataType: "String",
            StringValue: referenceId
        },
        "buyer_id": {
          DataType: "String",
          StringValue: buyerId
      },
        "event": {
            DataType: "String",
            StringValue: "WIDGET_AD_CLOSE_ORDER"
        }
    };
    let  messageBody= "Send widget admin close order";
    this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
  }

  async sendPoToGeneratePdfData(poNumber: string, event: string, processName:string = null) {
    let queueURL = process.env.AWS_WIDGET_GENERATE_PDF_DATA_QUEUE_URL;
    let messageAttributes = {
      "po_number": {
        DataType: "String",
        StringValue: poNumber
      },
      "event": {
        DataType: "String",
        StringValue: event
      }
    };
    if(processName){
      messageAttributes["process"] = {
        DataType: "String",
        StringValue: processName
      }
    }
    let messageBody = "MessageBody Widget Order PDF Generator Queue";
    let resp = this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody)
  }

  async sendSellerOrderCancelNotification(poNumber:string,event:string) {
    let queueURL = process.env.NOTIFICATION_QUEUE_URL;
    let messageAttributes= {
        "reference_id": {
            DataType: "String",
            StringValue: poNumber
        },
        "event": {
            DataType: "String",
            StringValue: event
        }
    };
    let  messageBody= "Send widget admin cancel seller order";
    this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
  }

  
  async sendBuyerOrderCancelNotification(poNumber:string,event:string) {
    let queueURL = process.env.NOTIFICATION_QUEUE_URL;
    let messageAttributes= {
        "reference_id": {
            DataType: "String",
            StringValue: poNumber
        },
        "event": {
            DataType: "String",
            StringValue: event
        }
    };
    let  messageBody= "Send widget admin cancel buyer order";
    this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
  }

  async sendEmailGeneratedByAdmin(queueURL:string,messageAttributes:any,messageBody:string){
    this.awsUtility.sendDataToSQS(queueURL,messageAttributes,messageBody)
  }

  async balance_log(balance_request, balance_response, payload, user_id, date_time, request, tag) {

    let queueUrl = process.env.AWS_BALANCE_LOGS_QueueUrl;

    if (user_id == null) {
      user_id = JSON.stringify("null");
    }

    if (request == null) {
      request = JSON.stringify("null");
    }
    if (balance_request == null) {
      balance_request = JSON.stringify("null");
    } else {
      balance_request = JSON.stringify(balance_request);
    }

    if (payload == null) {
      payload = JSON.stringify("null");
    } else {
      payload = JSON.stringify(payload);
    }
    if (balance_response == null) {
      balance_response = JSON.stringify("null");
    }

    balance_response = balance_response.substring(0, 130000);

    let params = {
      "user_id": {
        'DataType': "String",
        'StringValue': (""+ user_id)
      },
      "request": {
        'DataType': "String",
        'StringValue': balance_request
      },
      "response": {
        'DataType': "String",
        'StringValue': balance_response
      },
      "datetime": {
        'DataType': "String",
        'StringValue': date_time
      },
      "payload": {
        'DataType': "String",
        'StringValue': payload
      },
      "request_url": {
        'DataType': "String",
        'StringValue': request
      },
      "tag_name": {
        'DataType': "String",
        'StringValue': tag
      },
    };
    const messageBody = "MessageBody BALANCE_LOG";
    try {
      let result = this.awsUtility.sendDataToSQS(queueUrl, params, messageBody);
    } catch (e) {
      console.log(e);
      // output error message if fails
      // error_log(e.getMessage());
    }
  }
  
  async widgetResalesExemptionApprovalEmailUser(id: string) {
    const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    const messageAttributes = {
      "reference_id": {
        'DataType': "String",
        'StringValue': id
      },
      "event": {
        'DataType': "String",
        'StringValue': "WIDGET_SERVICE_RESALES_CERT_ACTION"
      }
    }
    const messageBody = "Widget service resale certificate approval status";
    this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody)
  }
  
  async sendNotificationBryzosPayApprovalRequest(bnplDataId: string) {
    const queueURL = process.env.NOTIFICATION_QUEUE_URL;
    const messageAttributes = {
      "reference_id": {
        'DataType': "String",
        'StringValue': bnplDataId
      },
      "event": {
        'DataType': "String",
        'StringValue': "NOTIFICATION_BNPL_APPROVAL_STATUS"
      }
    }
    const messageBody = "MessageBody Widget Bryzos Pay Approval Request"
    this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody)
  }
  
  async sendWidgetIncreaseBnplCreditResponse(userIncreaseCreditRequestId: string) {
    const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    const messageAttributes = {
      "reference_id": {
        'DataType': "String",
        'StringValue': userIncreaseCreditRequestId
      },
      "event": {
        'DataType': "String",
        'StringValue': "WIDGET_BRYZOS_PAY_INCREASE_CREDIT_RESPONSE"
      }
    }
    const messageBody = "MessageBody Widget Bryzos Pay Approval Request"
    this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody)
  }
  
  async errorMessage(message: string, referenceId: string, subject: string, authorizedForSuperEmail = "false") {
    const queueUrl = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    const messageBody = 'Message:Errors';
    const messageAttributes = {
      reference_id: { DataType: 'String', StringValue: referenceId },
      result: { DataType: 'String', StringValue: message },
      subject: { DataType: 'String', StringValue: subject },
      authorized_for_error_email: { DataType: 'String', StringValue: authorizedForSuperEmail },
      event: { DataType: 'String', StringValue: 'ERROR_MESSAGE' },
    };

    try {
      this.awsUtility.sendDataToSQS(queueUrl, messageAttributes, messageBody);
    } catch (error) {
      ExceptionService.log(error);
    }
  }

  async sendBryzosPayApprovalRequestNotification(bnplDataId) {
    const queueUrl = process.env.NOTIFICATION_QUEUE_URL;
    const messageBody = "mWidget Bryzos Pay Approval Request";
    const params = {
      reference_id: { DataType: "String", StringValue: bnplDataId },
      event: { DataType: "String", StringValue: "NOTIFICATION_BNPL_APPROVAL_STATUS" }
    };

    try {
      this.awsUtility.sendDataToSQS(queueUrl, params, messageBody);
    } catch (error) {
      ExceptionService.log(error);
    }
  }

  public async sendBryzosPayApprovalRequest($bnpl_data_id) {
    const queueUrl = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    const messageBody = 'MessageBody Widget Bryzos Pay Approval Request';
    const params = {
      reference_id: { DataType: 'String', StringValue: $bnpl_data_id },
      event: { DataType: 'String', StringValue: 'WIDGET_BRYZOS_PAY_APPROVAL_RESPONSE' }
    };
    try {
      this.awsUtility.sendDataToSQS(queueUrl, params, messageBody);
    } catch (error) {
      // output error message if fails
      ExceptionService.log(error);
    }
  }

  sendAchCreditOrderApprovalResponse(purchaseOrderId: string) {
    const queueUrl = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    const MessageAttributes = {
      reference_id: { DataType: 'String', StringValue: purchaseOrderId },
      event: { DataType: 'String', StringValue: 'WIDGET_ACH_CREDIT_ORDER_APPROVAL_RESPONSE' }
    };
    const messageBody = "This Widget Ach Credit Order Approval Response";

    try {
      this.awsUtility.sendDataToSQS(queueUrl, MessageAttributes, messageBody);
    } catch (error) {
      // output error message if fails
      ExceptionService.log(error);
    }
  }
  
  async sendDataToAwsQueue(referenceId: string, eventName: string, sqsMessageBody: string, queueURL?: string | null) {

    if (!queueURL) {
        queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;
    }

    let messageAttributes = {
        "reference_id": {
            DataType: "String",
            StringValue: referenceId
        },
        "event": {
            DataType: "String",
            StringValue: eventName
        }
    };

    let messageBody = sqsMessageBody;
    this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody);
  }

  async sendReferenceDataProductExcelUpdateEmail(fileType: string) {
    const queueURL = process.env.MESSAGE_NODE_EMAIL_QUEUE;

    const messageAttributes = {
      "reference_id": {
        DataType: "String",
        StringValue: fileType
      },
      "event": {
        DataType: "String",
        StringValue: "UPDATE_REFERENCE_DATA_PROODUCT_EXCEL_EMAIL"
      }
    };
    const messageBody = "Upadted Reference Data Product Excel Email";
    this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody)
  }

  async sendReferenceDataExcelToImportQueue(fileType: string, event: string) {
    const queueURL = process.env.IMPORT_PRODUCT_EXCEL_TO_DB_QUEUE;

    const messageAttributes = {
      "reference_id": {
        DataType: "String",
        StringValue: fileType
      },
      "event": {
        DataType: "String",
        StringValue: event
      }
    };
    const messageBody = "Import Reference Data Product Excel to DB";
    this.awsUtility.sendDataToSQS(queueURL, messageAttributes, messageBody)
  }
}