import { Injectable } from '@nestjs/common';
import { Repository, Connection, JsonContains, DataSource, EntitySchema } from 'typeorm';
import {DataBaseService, BaseLibraryService, ReferenceDataSettings, ReferenceDataPGPMMapping, ReferenceDataKeys, Constants as libConstants, AwsUtilityV3 } from '@bryzos/base-library';
import { InjectRepository, InjectDataSource } from '@nestjs/typeorm';
import { Constants } from 'src/Constants';
import { addDays, format, parseISO } from 'date-fns';
import * as moment from 'moment-timezone';
import { createCipheriv, createDecipheriv, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';
// import { ExceptionService } from './handlers/exception.service';
import { ExceptionService, BryzosLogger} from '@bryzos/extended-widget-library';
import { CompanyBuyNowPayLater } from '@bryzos/extended-widget-library';
import { HttpService } from '@nestjs/axios';
const axios = require('axios');
import { spawn } from 'child_process';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import * as path from 'path';
import { ExcelSheetWrapper } from './export-excel-sheet-wrapper';
const xlsx = require('xlsx');

const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);
const rmdir = promisify(fs.rmdir);
export interface ExportedFile {
  fileName: string;
  sheetName: string;
}
@Injectable()
export class Utils {
  private dbServiceObj = new DataBaseService();
  private uploadDir: string;

  constructor(
    private readonly baseLibraryService: BaseLibraryService,
    private readonly httpService : HttpService,
    private readonly awsUtility: AwsUtilityV3,
    private readonly excelSheetWrapper: ExcelSheetWrapper,

    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(ReferenceDataPGPMMapping) private readonly referenceDataPGPMMappingRepository: Repository<ReferenceDataPGPMMapping>,
    @InjectRepository(ReferenceDataKeys) private readonly referenceDataKeysRepository: Repository<ReferenceDataKeys>,
    @InjectDataSource() private readonly dataSource: DataSource,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>

  ) { }

  async createTransactionLogData(source,userId,referenceId,event,data) {

    const currentDateTime = new Date();
    const utcTime = currentDateTime.toISOString();


    const logData = {
        source,
        user_id: userId,
        reference_id: referenceId,
        event,
        process: Constants.LOGGING_INFO,
        date_time: utcTime,
        data,
      };


    let logResponse = BryzosLogger.log(JSON.stringify(logData),process.env.ADMIN_DASHBOARD_ACH_BNPL_LOGGLY_LOG_TAG);

  }

  convertUtcToCst(dateTime) {
    let datetime = null;
    if (moment.utc(dateTime).unix() && moment(dateTime).utc(true).format('YYYY-MM-DD HH:mm:ss')) {
      datetime = moment(dateTime).utc(true).clone().tz('America/Chicago');
    }
    return datetime;
  }

  createLogData = async (filenameLogging, referenceId, logEvent, data) => {
    let dateTime = moment().format('YYYY-MM-DD HH:mm:ss');
    let logData = { source: filenameLogging, referenceId: referenceId, event: logEvent, process: Constants.LOGGING_INFO, date_time: dateTime, data: data };
    BryzosLogger.log(JSON.stringify(logData), process.env.ADMIN_DASHBOARD_CASS_LOGGLY_LOG_TAG);
  };

  async getEncodedData(dataToEncode) {
    let encryptedRes = '';

    //encrypt vendor id
    let encryptionKey = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.ENCRYPTION_KEY);
    let encryptionType = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.ENCRYPTION_TYPE);

    let iv = randomBytes(16);

    const key = (await promisify(scrypt)(encryptionKey, 'salt', 32)) as Buffer;
    const cipher = createCipheriv(encryptionType, key, iv);

    let encryptedResponse = Buffer.concat([cipher.update(dataToEncode), cipher.final()]);

    encryptedRes = encryptedResponse.toString('base64') + ':' + iv.toString('base64');
    return encryptedRes;
  }

  async getReferenceDataSettingsColumn(name) {
    let setting = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSettingsRepository, { name: name });

    if (setting) {
      return setting.value;
    } else {
      return null;
    }
  }

  async getBryzosVendorKey(vendor, key) {
    const _key = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataKeysRepository, {vendor: vendor, key: key});

    if (_key) {
      return _key.value;
    } else {
      return null;
    }
  }

  async getKeyManager(secretManagerKey) {
    let secretUserAgent = process.env.SM_BASE_URL_USER_AGENT;
    let secretName = process.env.SM_ENV;
    let response = null;
    try {
      response = (await axios.post(`${process.env.SM_BASE_URL}/getValue`, { env_name: secretName, sm_key: secretManagerKey }, { headers: { 'User-Agent': secretUserAgent, 'x-api-key': process.env.SM_API_KEY, 'Content-Type': 'application/json' } })).data;
    } catch (err) {
      this.logInfo(err);
    }
    return response;
  }

  logInfo(err: any) {
    BryzosLogger.log(JSON.stringify(err?.message || err), process.env.CASS_LOGGLY_TAG);
  }

    async getPaymentMethodId(userType, paymentType = '') {
    let paymentMethodIds = null;
    const paymentMethodQuery = paymentType ? ` AND payment_method = '${paymentType}'` : '';

    let paymentMethod = await this.dbServiceObj.findManyWithWhere(this.referenceDataPGPMMappingRepository, "status", "1", "user_type", userType);

    if (paymentMethod) {
      paymentMethodIds = paymentMethod.map((pMethod) => pMethod.id);
    }

    return paymentMethodIds;
  }

  async getCassCode(sellerPaymentMethod) {
    let cassCode = null;
    let cassPayment = (await this.dataSource.query(`Select * from reference_data_pgpm_mapping LEFT JOIN reference_data_cass_disbursement_method ON reference_data_cass_disbursement_method.pgpm_mapping_id = reference_data_pgpm_mapping.id  WHERE payment_method='${sellerPaymentMethod}' LIMIT 0, 1`))?.[0];
    if (cassPayment) {
      cassCode = cassPayment.cass_code;
    }
    return cassCode;
  }

  async getBryzosVendorSecret(vendor, key) {
    //get secret key
    const secret: any = (await this.dataSource.query("SELECT * FROM reference_data_keys WHERE vendor = '" + vendor + "' AND `key` = '" + key + "' AND is_active = 1 LIMIT 0, 1"))?.[0];

    if (secret?.secret) {
      //decrypt secret
      let encryptionKey = process.env.VALUE_ENCRYPTION_KEY;
      let encryptionType = process.env.VALUE_ENCRYPTION_TYPE;
      let parts = secret.secret.split(':');
      if (parts.length == 2) {
        let encryptedText = Buffer.from(parts[0], 'base64');
        let iv = Buffer.from(parts[1], 'base64');

        const decipher = createDecipheriv(encryptionType, encryptionKey, iv);
        const decrypted_secret = Buffer.concat([
          decipher.update(encryptedText),
          decipher.final(),
        ]).toString();
        this.logInfo(decrypted_secret);
        this.logInfo('decrypted_secret');

        return decrypted_secret;
      }
    }
    return null;
  }

  async checkBuyerCreditLimit(price,buyerId){
    let response = null;
    const companyBuyNowPayLater = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository,{"user_id" : buyerId});
    if(companyBuyNowPayLater && companyBuyNowPayLater.is_approved == true){
      const currentBuyerCreditAvailableLimit = Number(companyBuyNowPayLater.bryzos_available_credit_limit);
      const bryzosCreditLimit = Number(companyBuyNowPayLater.bryzos_credit_limit);
      if(currentBuyerCreditAvailableLimit > price){
        //await this.updateBryzosCreditLimit(currentBuyerCreditAvailableLimit,price,bryzosCreditLimit,buyerId);
        response = companyBuyNowPayLater;
      }else{
        response = {"error_message" : "Insufficient available credit limit!"};
      }
    }else{
      response = {"error_message" : "The buyer's BNPL is not setup."};
    }
    return response;
  }

  async updateBryzosCreditLimit(availableCreditLimit,price,bryzosCreditLimit,buyerId){
    const updateCBNPLData = {};
    const newAvailableCreditLimit = Number(availableCreditLimit) - Number(price);
    updateCBNPLData['bryzos_available_credit_limit'] = newAvailableCreditLimit
    updateCBNPLData['bryzos_outstanding_credit_limit'] =  Number(bryzosCreditLimit) - newAvailableCreditLimit;
    await this.dbServiceObj.updateByMultipleWhere(updateCBNPLData, {"user_id":buyerId}, this.companyBuyNowPayLaterRepository);
  }


  sendDataToWebsocket = (object:object,url:string)  => new Promise(async function (resolve, reject)
  {
    let curlResponse = null;
    const websocketControllerEndPoint = process.env.GISS_WS_SERVER+'/'+url;

    let updateData = {
      method: 'post',
      maxBodyLength: Infinity,
      url: websocketControllerEndPoint,
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json',
        'gissToken':  process.env.GISS_UI_TOKEN
      },
      data: object
    };
    axios.request(updateData)
    .then((response) => {
      curlResponse = response.data;
      BryzosLogger.log(JSON.stringify({"webScoketResponse":curlResponse}), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
      resolve(curlResponse);
    })
    .catch((error) => {
      BryzosLogger.log(JSON.stringify({"Error":curlResponse}), process.env.LOGGLY_ERROR_TAG);
    resolve(curlResponse);
    });
  });

  async getTotalCountAndAmountTransactionsForLast12Month(state_id,zipCode,userPurchaseOrderEntity){
    let totalCountAndAmount = [];
    const currentDate = new Date(); // get current date
    const startDate = new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate());
    const oneYearAgo = new Date(startDate.toISOString().split('T')[0] + 'T00:00:00.000Z');

    const columnContditions = [
      { columnName: 'is_active', operator: '=', value: true },
      { columnName: 'state_id', operator: '=', value: state_id},
      { columnName: 'zip', operator: '=', value: zipCode },
      { columnName: 'is_closed_buyer', operator: '=', value: true },
      { columnName: 'created_date', operator: '>', value: oneYearAgo }
    ]

    const selectedFields = ["count(*) as totalCount","SUM( CASE WHEN actual_buyer_po_price IS NULL THEN buyer_po_price ELSE actual_buyer_po_price END ) as totalAmount"];
    totalCountAndAmount = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(userPurchaseOrderEntity,columnContditions,selectedFields);

    if(totalCountAndAmount && totalCountAndAmount.length){
      return totalCountAndAmount[0];
    }else{
      return false;
    }
  }

  async sendWebsocketEvent(data: any, socketEndPoint: string) {
    const url = `${process.env.GISS_WS_SERVER}/${socketEndPoint}`;

    const payload = data;
    const headers = {
      'Content-Type': 'application/json',
      'gissToken': process.env.GISS_UI_TOKEN,
      'origin': process.env.AD_ORIGIN,
      'referer': process.env.AD_REFERER,
    };

    try {
      const response = (await this.httpService.axiosRef.post(url, payload, { headers })).data;
      BryzosLogger.log(JSON.stringify({ response_type: "WS response", response }), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
      return response;
    } catch (error) {
      BryzosLogger.log(JSON.stringify({ error_type: "WS error", error }), process.env.LOGGLY_ERROR_TAG);
      ExceptionService.log(error);
    }
  }

  async saveUploadedFileLocally(fileBuffer: Buffer, fileType: string, uploadId: string, originalName: string) {
    try {
        this.uploadDir = './uploads';

        const ensureFileTypeUploadDirRes = await this.ensureFileTypeUploadDir();
        if (ensureFileTypeUploadDirRes?.error_message) {
          return ensureFileTypeUploadDirRes;
        }

        // Create upload directory
        let uploadDirPath = path.join(this.uploadDir, fileType, uploadId); // uploads/search/upload_123
        await this.ensureDirectoryExists(uploadDirPath);

         // Create sheets directory
        const sheetsDir = path.join(uploadDirPath, 'sheets'); // uploads/search/upload_123/sheets
        await this.ensureDirectoryExists(sheetsDir);

        // Create exports directory
        const exportsDir = path.join(uploadDirPath, 'exports'); // uploads/search/upload_123/exports
        await this.ensureDirectoryExists(exportsDir);

        // Save original file
        const filePath = path.join(uploadDirPath, 'original.xlsx'); // uploads/search/upload_123/original.xlsx
        await writeFile(filePath, fileBuffer);

        // Save metadata
        const metadata = {
          originalName,
          uploadDate: new Date().toISOString(),
          fileType,
          status: 'uploaded',
        };

        await writeFile(
          path.join(uploadDirPath, 'metadata.json'), // uploads/search/upload_123/metadata.json
          JSON.stringify(metadata, null, 2),
        );

        return filePath;

    } catch (error) {
      console.log(`Error saving uploaded file locally: ${error}`);
      return { error_message: `Error saving uploaded file locally: ${error?.message}` };
    }
  }


  async ensureFileTypeUploadDir() {
    try {
      const searchDir = path.join(this.uploadDir, Constants.SEARCH_FILE_TYPE);  // uploads/search
      const pricingDir = path.join(this.uploadDir, Constants.PRICING_FILE_TYPE); // uploads/pricing

      await this.ensureDirectoryExists(this.uploadDir);
      await this.ensureDirectoryExists(searchDir);
      await this.ensureDirectoryExists(pricingDir);
    } catch (error) {
      console.log(`Error ensuring file type upload directory: ${error}`);
      return { error_message: `Error ensuring file type upload directory: ${error?.message}` };
    }
  }

  async ensureDirectoryExists(dir: string) {
    try {
      await mkdir(dir, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        console.log(`Error ensuring directory exists: ${error}`);
        throw error;
      }
    }
  }

  async getFileInfo(uploadId: string) {
    try {
      // Find the upload directory
      const uploadDirectory = await this.findUploadDir(uploadId);
      if (!uploadDirectory) {
        return { error_message: `Upload Directory Not Found for Upload ID : ${uploadId}` };
      }

      // Read metadata
      const metadataPath = path.join(uploadDirectory, 'metadata.json'); // uploads/search/upload_123/metadata.json
      const metadata = JSON.parse(await readFile(metadataPath, 'utf8')); // metadata.json

      return {
        originalFilePath: path.join(uploadDirectory, 'original.xlsx'), // uploads/search/upload_123/original.xlsx
        fileType: metadata.fileType, // search
      };
    } catch (error) {
      console.log('Failed to get file info : ', error);
      return { error_message: 'Failed to get file info' };
    }
  }

  async findUploadDir(uploadId: string) {
    // Search for the upload directory in all directories
    try {

      const uploadDirStat = await stat(this.uploadDir); // uploads

      if (uploadDirStat.isDirectory()) {
        const uploadDirList = await readdir(this.uploadDir); // uploads

        for (const fileType of [Constants.SEARCH_FILE_TYPE, Constants.PRICING_FILE_TYPE]) {
          const typeDir = path.join(this.uploadDir, fileType); // uploads/search

          try {
            if (!uploadId.startsWith(`${fileType}_upload_`)) {
              console.log(`UploadId doesn't start with ${fileType}_upload_, continue searching`);
              continue;
            }

            const typeStat = await stat(typeDir);

            if (typeStat.isDirectory()) {
              const uploadDirPath = path.join(typeDir, uploadId); // uploads/search/upload_123

              try {
                const uploadStat = await stat(uploadDirPath); // uploads/search/upload_123

                if (uploadStat.isDirectory()) {
                  return uploadDirPath; // uploads/search/upload_123
                }
              } catch (error) {
                // Upload directory doesn't exist, continue searching
                console.log("findUploadDir catch-1 error (Upload directory doesn't exist, continue searching) : ", error);
              }
            }
          } catch (error) {
            // Type directory doesn't exist, continue searching
            console.log("findUploadDir catch-2 error (Type directory doesn't exist, continue searching) : ", error);
          }
        }
      }
    } catch (error) {
      // Error reading upload directory
      console.log("findUploadDir error : ", error);
    }
    return null;
  }

  async processExcelFile(filePath: string, fileType: string, uploadId: string,) {
    try {
      console.log(`Starting processing of ${fileType} file for upload ID: ${uploadId}`,);

      // Update status to processing
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 0, message: 'Starting processing', });

      // Process file based on type
      if (fileType === Constants.SEARCH_FILE_TYPE) {
        // Placeholder for search file processing
        await this.processSearchFile(filePath, uploadId);
      } else if (fileType === Constants.PRICING_FILE_TYPE) {
        // Placeholder for pricing file processing
        await this.processPricingFile(filePath, uploadId);
      } else {
        return { error_message: `Unsupported file type: ${fileType}` }
      }

      // Update status to completed
      await this.updateProcessingStatus(uploadId, { status: 'completed', progress: 100, message: 'Processing completed', });

      console.log(`Processing completed for upload ID: ${uploadId}`);
    } catch (error) {
      console.log(`Processing error for upload ID: ${uploadId} : ${error}`);

      let errorMessage = `Error processing file: ${error?.message}`;

      // Handle specific error types with more user-friendly messages
      if (error.message && error.message.includes('Invalid string length')) {
        errorMessage = 'The file is too large to process. Try splitting it into smaller files or reducing the number of columns.';
        console.log('JSZip string length error detected. File too large to process.',);
      }

      // Update status to error
      await this.updateProcessingStatus(uploadId, { status: 'error', progress: 0, message: errorMessage, });

      return { error_message: errorMessage };
    }
  }

  async updateProcessingStatus(uploadId: string, status: { status: string; progress: number; message: string }) {
    try {
      // Find the upload directory
      const uploadDirectory = await this.findUploadDir(uploadId);
      if (!uploadDirectory) {
        return { error_message: `Upload ID ${uploadId} not found` }
      }

      // Read existing metadata
      const metadataPath = path.join(uploadDirectory, 'metadata.json');
      const metadata = JSON.parse(await readFile(metadataPath, 'utf8'));

      // Update status
      metadata.status = status.status;
      metadata.progress = status.progress;
      metadata.message = status.message;
      metadata.lastUpdated = new Date().toISOString();

      console.log("updateProcessingStatus metadata : ", metadata);

      // Save updated metadata
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    } catch (error) {
      console.log(`Error updating processing status : ${uploadId}`, error);
      return { error_message: `Error updating processing status: ${error?.message}` }
    }
  }


  private async processSearchFile(filePath: string, uploadId: string,) {
    try {
      console.log(`Processing search file: ${filePath}`);

      // Hardcoded column mappings for search data
      let searchColumnMappings = [
        { source: 'Shape ID', target: 'Shape_ID' },
        { source: 'Product ID', target: 'Product_ID' },
        { source: 'LBS/Ft', target: 'LBS_FT' },
        { source: 'Order_Increment_(Ft)', target: 'Order_Increment_Ft' },
        { source: 'Order_Increment_(Ea)', target: 'Order_Increment_Ea' },
        { source: 'Order_Increment_(Lb)', target: 'Order_Increment_Lb' },
        { source: 'Order_Increment_(CWT)', target: 'Order_Increment_CWT' },
        { source: 'Order_Increment_(Net_Ton)', target: 'Order_Increment_Net_Ton' },
        { source: 'Key_1', target: 'Key1' },
        { source: 'Key_2', target: 'Key2' },
        { source: 'Key_3', target: 'Key3' },
        { source: 'Key_4', target: 'Key4' },
        { source: 'Key_5', target: 'Key5' },
        { source: 'Key_6', target: 'Key6' },
        { source: 'Key_7', target: 'Key7' },
        { source: 'Key_8', target: 'Key8' },
        { source: 'Key_9', target: 'Key9' },
        { source: 'Key_10', target: 'Key10' },
        { source: 'Key_11', target: 'Key11' },
        { source: 'Key_12', target: 'Key12' },
        { source: 'Key_13', target: 'Key13' },
        { source: 'Key_14', target: 'Key14' },
        { source: 'Key_15', target: 'Key15' },
        { source: 'Key_16', target: 'Key16' },
        { source: 'Key_17', target: 'Key17' },
        { source: 'Key_18', target: 'Key18' },
        { source: 'Key_19', target: 'Key19' },
        { source: 'Key_20', target: 'Key20' },
        { source: 'Key_21', target: 'Key21' },
        { source: 'Key_22', target: 'Key22' },
        { source: 'Key_23', target: 'Key23' },
        { source: 'Key_24', target: 'Key24' },
        { source: 'UI_Description', target: 'UI_Description' },
        { source: 'QUM_Dropdown_Options', target: 'QUM_Dropdown_Options' },
        { source: 'PUM_Dropdown_Options', target: 'PUM_Dropdown_Options' },
        { source: 'Domestic Enabled', target: 'domestic_material_only' },
      ];

      // Extract source column names for lookup in the Excel file
      const columnsToExtract = searchColumnMappings.map(
        (mapping) => mapping.source,
      );

      // Load workbook
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.readFile(filePath);

      // Get total number of sheets
      const totalSheets = workbook.worksheets.length;
      let processedSheets = 0;
      console.log("totalSheets : ", totalSheets);

      // Track exported files for metadata
      const exportedFiles: Array<{ fileName: string; sheetName: string }> = [];

      // Process each sheet
      for (const worksheet of workbook.worksheets) {
        try {
          console.log(`Processing sheet: ${worksheet.name}`);

          // Update status
          const progress = Math.floor((processedSheets / totalSheets) * 100);
          await this.updateProcessingStatus(uploadId, { status: 'processing', progress, message: `Processing sheet ${processedSheets + 1} of ${totalSheets}: ${worksheet.name}`, });

          console.log(`Processing sheet ${processedSheets + 1} of ${totalSheets}: ${worksheet.name}`);

          // Create a new workbook for the exported data
          const outputWorkbook = new ExcelJS.Workbook();
          const outputWorksheet = outputWorkbook.addWorksheet(worksheet.name);

          // Map column names to indices
          const headerIndices: { [key: string]: number } = {};
          let headerRow = worksheet.getRow(1);

          headerRow.eachCell((cell, colNumber) => {
            if (cell.value) {
              // Normalize the column name for comparison
              const normalizedValue = String(cell.value).trim();

              // Check if this column is one we want to extract
              for (const colName of columnsToExtract) {
                // Normalize the column name for comparison
                const normalizedColName = String(colName).trim();

                if (normalizedValue === normalizedColName) {
                  headerIndices[colName] = colNumber;
                  break;
                }
              }
            }
          });

          // Check if all required columns exist
          const missingColumns = columnsToExtract.filter(
            (col) => !(col in headerIndices),
          );

          if (missingColumns.length > 0) {
            console.log(`The following required columns are missing in sheet ${worksheet.name}: ${missingColumns.join(', ')}`,);
            // Continue with next sheet
            continue;
          }

          console.log(`Found all required columns in sheet ${worksheet.name}. Processing data...`,);

          // Add header row to output worksheet with target column names
          const targetColumnNames = searchColumnMappings.map(
            (mapping) => mapping.target,
          );

          // Add the new is_safe_product_code column
          targetColumnNames.push('is_safe_product_code');
          outputWorksheet.addRow(targetColumnNames);

          // Process data rows
          let rowCount = 0;
          let outputRowCount = 1; // Start at 1 because we already added the header row

          // Process each row
          for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
            const row = worksheet.getRow(rowNumber);
            rowCount++;

            // Extract values for each column
            const values = columnsToExtract.map((colName) => {
              const cellIndex = headerIndices[colName];
              if (cellIndex === undefined) return null;

              const cell = row.getCell(cellIndex);
              let cellValue = cell.value;

              // Handle formula results - get the calculated value
              if (cellValue && typeof cellValue === 'object') {
                const cellValueObj = cellValue as any;
                if ('formula' in cellValueObj) {
                  return cellValueObj.result;
                } else if ('text' in cellValueObj) {
                  return cellValueObj.text;
                } else if ('value' in cellValueObj) {
                  return cellValueObj.value;
                }
              }

              return cellValue;
            });

            // Find the Product_ID value to determine is_safe_product_code
            const productIdIndex = columnsToExtract.findIndex(
              (col) => col === 'Product ID',
            );

            let productId = values[productIdIndex];

            // Handle formula results or objects for Product ID
            if (productId && typeof productId === 'object') {
              const productIdObj = productId as any;
              if ('formula' in productIdObj) {
                productId = productIdObj.result;
              } else if ('text' in productIdObj) {
                productId = productIdObj.text;
              } else if ('value' in productIdObj) {
                productId = productIdObj.value;
              }
            }

            // Convert to string for comparison
            productId = String(productId || '');

            // Set is_safe_product_code to 1 if Product_ID is 421020001 or 422020001, otherwise 0
            const isSafeProductCode = productId === '421020001' || productId === '422020001' ? 1 : 0;

            // Add the is_safe_product_code value to the row
            values.push(isSafeProductCode);

            // Add row to output worksheet
            outputWorksheet.addRow(values);
            outputRowCount++;

            // Log progress
            if (rowCount % 1000 === 0) {
              console.log(`Processed ${rowCount} rows in sheet ${worksheet.name}...`,);
            }
          }

          console.log(`Processed ${rowCount} rows from sheet ${worksheet.name}.`,);
          console.log(`Added ${outputRowCount} rows to output file (including header).`,);

          // Save the output workbook
          // const outputFileName = `${worksheet.name}_product_search.xlsx`;
          const outputFileName = `product_search.xlsx`;
          const outputPath = await this.getExportedFilePath('./uploads', Constants.SEARCH_FILE_TYPE, uploadId, outputFileName,);

          await outputWorkbook.xlsx.writeFile(outputPath);
          console.log(`Saved exported file to: ${outputPath}`);

          // Add to exported files list
          exportedFiles.push({
            fileName: outputFileName,
            sheetName: worksheet.name,
          });

          processedSheets++;
        } catch (error) {
          console.log(`Error processing sheet ${worksheet.name}:`, error);
          // Continue with next sheet
        }
      }

      // Update metadata with exported files
      await this.updateMetadataWithExportedFiles(uploadId, exportedFiles);

      console.log(`Completed processing search file. Processed ${processedSheets} of ${totalSheets} sheets.`,);
    } catch (error) {
      console.log(`Error processing search file:`, error);
      throw error;
    }
  }

  async getExportedFilePath( uploadDir: string, fileType: string, uploadId: string, fileName: string, ) {
    const exportsDir = path.join(uploadDir, fileType, uploadId, 'exports', );
    await this.ensureDirectoryExists(exportsDir);
    return path.join(exportsDir, fileName);
  }

  async updateMetadataWithExportedFiles(uploadId: string, exportedFiles: Array<{ fileName: string; sheetName: string }>,) {
    try {
      console.log("updateMetadataWithExportedFiles uploadId : ", uploadId);
      // Find the upload directory
      const uploadDirectory = await this.findUploadDir(uploadId);
      if (!uploadDirectory) {
        console.log(`Upload Directory Not Found for Upload ID : ${uploadId}`);
        throw new Error(`Upload Directory Not Found for Upload ID : ${uploadId}`);
      }

      // Read existing metadata
      const metadataPath = path.join(uploadDirectory, 'metadata.json');
      const metadata = JSON.parse(await readFile(metadataPath, 'utf8'));

      // Update exported files
      metadata.exportedFiles = exportedFiles;
      metadata.exportedAt = new Date().toISOString();

      // Save updated metadata
      await writeFile(metadataPath, JSON.stringify(metadata, null, 2));
    } catch (error) {
      console.log(`Error updating metadata with exported files:`, error);
    }
  }

  private async processPricingFile(filePath: string, uploadId: string,) {
    console.log(`Utils Processing pricing file: ${filePath}`);

    // Track exported files for metadata
    const exportedFiles: Array<{ fileName: string; sheetName: string }> = [];

    try {
      // Process volume_pricing_brackets sheet
      console.log('Processing volume_pricing_brackets sheet');
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 10, message: 'Processing volume_pricing_brackets sheet', });

      // Hardcoded column mappings for pricing data - volume_pricing_brackets
      let volumePricingBracketsColumnMappings = [
        { source: 'bracket', target: 'bracket' },
        { source: 'low_value_lbs', target: 'low_value_lbs' },
        { source: 'high_value_lbs', target: 'high_value_lbs' },
        { source: 'default_bracket', target: 'default_bracket' },
        { source: 'ui_dropdown_choice', target: 'ui_dropdown_choice' },
        { source: 'value', target: 'value' },
      ];

      const volumePricingResult = await this.excelSheetWrapper.processExcelSheet(filePath, 'volume_pricing_brackets', 'volume_pricing_brackets.xlsx', volumePricingBracketsColumnMappings, uploadId,);

      if (volumePricingResult) {
        exportedFiles.push({
          fileName: 'volume_pricing_brackets.xlsx',
          sheetName: 'volume_pricing_brackets',
        });
      }
      console.log("volumePricingResult: ", volumePricingResult);

      // Process region_lookup_by_zip sheet
      console.log('Processing region_lookup_by_zip sheet');
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 30, message: 'Processing region_lookup_by_zip sheet', });

      // Hardcoded column mappings for pricing data - region_lookup_by_zip
      let regionLookupByZipColumnMappings = [
        { source: 'zip_code', target: 'zip_code' },
        { source: 'tier_1', target: 'tier_1' },
        { source: 'tier_2', target: 'tier_2' },
        { source: 'tier_3', target: 'tier_3' },
        { source: 'state_abbreviation', target: 'state_abbreviation' },
      ];

      const regionLookupResult = await this.processExcelSheet(filePath, 'region_lookup_by_zip', 'region_lookup_by_zip.xlsx', regionLookupByZipColumnMappings, uploadId,);

      if (regionLookupResult) {
        exportedFiles.push({
          fileName: 'region_lookup_by_zip.xlsx',
          sheetName: 'region_lookup_by_zip',
        });
      }
      console.log("regionLookupResult: ", regionLookupResult);

      // Process tiers_per_state sheet
      console.log('Processing tiers_per_state sheet');
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 50, message: 'Processing tiers_per_state sheet', });

      // Hardcoded column mappings for pricing data - tiers_per_state
      let tiersPerStateColumnMappings = [
        { source: 'state_abbreviation', target: 'state_abbreviation' },
        { source: 'tiers', target: 'tiers' },
      ];

      const tiersPerStateResult = await this.processExcelSheet(filePath, 'tiers_per_state', 'tiers_per_state.xlsx', tiersPerStateColumnMappings, uploadId,);
      if (tiersPerStateResult) {
        exportedFiles.push({
          fileName: 'tiers_per_state.xlsx',
          sheetName: 'tiers_per_state',
        });
      }
      console.log("tiersPerStateResult: ", tiersPerStateResult);

      // Process regional_pricing_lookup sheet (creates files for each state)
      console.log('Processing regional_pricing_lookup sheet for all states',);
      await this.updateProcessingStatus(uploadId, { status: 'processing', progress: 70, message: 'Processing regional_pricing_lookup sheet for all states', });

      const regionalPricingResults = await this.processRegionalPricingByState(filePath, 'regional_pricing_lookup', uploadId);
      // Add all regional pricing files to the exported files list
      exportedFiles.push(...regionalPricingResults);

      // Update metadata with exported files
      await this.updateMetadataWithExportedFiles(uploadId, exportedFiles,);

      console.log(`Completed processing pricing file. Exported ${exportedFiles.length} files.`,);
    } catch (error) {
      console.log(`Error processing pricing file: `, error);
      throw error;
    }
  }

  async processExcelSheet(inputFilePath: string, sheetName: string, outputFileName: string, columnMappings, uploadId: string,) {
    try {
      console.log(`Utils Processing sheet: ${sheetName} for export to ${outputFileName}`,);

      // Path to the JavaScript implementation
      const dir = path.resolve(__dirname, '../../sample-logic/export-excel-sheet.js'); // Use absolute path
      console.log("Utils processExcelSheet dir: ", dir);

      // Convert column mappings to JSON string
      const columnMappingsJson = JSON.stringify(columnMappings);

      // Spawn a child process to run the JavaScript implementation
      const childProcess = spawn('node', [dir, inputFilePath, sheetName, outputFileName, columnMappingsJson, uploadId,]);

      // Collect output from the process
      let output = '';
      childProcess.stdout.on('data', (data) => {
        output += data.toString();
        console.log(data.toString().trim());
      });

      let errorOutput = '';
      childProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        console.log(data.toString().trim());
      });

      // Wait for the process to complete
      return new Promise<boolean>((resolve) => {
        childProcess.on('close', (code) => {
          if (code === 0) {
            console.log(`Utils Successfully processed sheet: ${sheetName}`);
            resolve(true);
          } else {
            console.log(`Utils Failed to process sheet: ${sheetName}. Exit code: ${code}`,);
            console.log(`Utils Error output: ${errorOutput}`);
            resolve(false);
          }
        });
      });
    } catch (error) {
      console.log(`Utils Error processing sheet ${sheetName}:`, error);
      return false;
    }
  }

  async processRegionalPricingByState(inputFilePath: string, sheetName: string, uploadId: string,): Promise<ExportedFile[]> {
    try {
      console.log(`Processing regional pricing by state from sheet: ${sheetName}`,);

      // Path to the JavaScript implementation
      const dir = path.resolve(__dirname, '../../sample-logic/export-regional-pricing-by-state.js'); // Use absolute path
      console.log("processRegionalPricingByState dir: ", dir);

      // Spawn a child process to run the JavaScript implementation
      const childProcess = spawn('node', [dir, inputFilePath, sheetName, uploadId,]);

      // Collect output from the process
      let output = '';
      childProcess.stdout.on('data', (data) => {
        output += data.toString();
        console.log(data.toString().trim());
      });

      let errorOutput = '';
      childProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        console.log(data.toString().trim());
      });

      // Wait for the process to complete
      return new Promise<ExportedFile[]>((resolve) => {
        childProcess.on('close', (code) => {
          if (code === 0) {
            console.log(`Successfully processed regional pricing by state from sheet: ${sheetName}`,);

            try {
              // Parse the output to get the exported files
              const exportedFilesMatch = output.match(/EXPORTED_FILES:(.*)/);
              if (exportedFilesMatch && exportedFilesMatch[1]) {
                const exportedFiles = JSON.parse(exportedFilesMatch[1]);
                resolve(exportedFiles);
              } else {
                console.log('No exported files found in output');
                resolve([]);
              }
            } catch (error) {
              console.log('Error parsing exported files:', error);
              resolve([]);
            }
          } else {
            console.log(`Failed to process regional pricing by state from sheet: ${sheetName}. Exit code: ${code}`,);
            console.log(`Error output: ${errorOutput}`);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.log(`Error processing regional pricing by state:`, error,);
      return [];
    }
  }

  async processExportFileAndUploadToS3(uploadId: string, fileType: 'search' | 'pricing') {
    try {
      console.log("processExportFileAndUploadToS3 uploadId: ", uploadId);
      const baseDir = path.join('uploads', fileType, uploadId, 'exports');
      console.log("baseDir: ", baseDir);

      const bucket = process.env.UPLOAD_PRODUCT_PRICING_SPLIT_EXCEL_BUCKET;
      const bucketDir = process.env.S3_BUCKET_ENVIRONMENT;

      try {
        const stats = await stat(baseDir);
        if (!stats.isDirectory()) return { error: 'Exports directory not found' };
      } catch (e) {
        console.log("processExportFileAndUploadToS3-1 error: ", e);
        return { error_message: 'Exports directory does not exist' };
      }

      let files: string[];
      try {
        files = await readdir(baseDir);
      } catch (e) {
        console.log("processExportFileAndUploadToS3-2 error: ", e);
        return { error_message: 'Unable to read exports directory' };
      }

      // Store file buffers for database import
      const fileBuffersForImport: Array<{ buffer: Buffer; fileName: string }> = [];

      // Get the correct file to upload
      let fileName: string | undefined;
      let tableName: string | undefined;

      if (fileType === Constants.SEARCH_FILE_TYPE) {
        fileName = files.find(name => name === 'product_search.xlsx');
        tableName = 'reference_data_products_widget';

        if (fileName) {
          const filePath = path.join(baseDir, fileName);
          const fileBuffer = await readFile(filePath);
          const s3Key = `${bucketDir}/${fileType}/${fileName}`;

          // Upload to S3
          // await this.awsUtility.uploadFileInS3(s3Key, bucket, fileBuffer);

          // Store buffer for database import
          fileBuffersForImport.push({ buffer: fileBuffer, fileName });

          // Import to database in background without await
          this.importFileBufferToDb(fileBuffer, fileType, uploadId, fileName).catch((error: any) => {
            console.log(`Background import error for ${fileName}:`, error);
          });
        }
      } else if (fileType === Constants.PRICING_FILE_TYPE) {
        for (const fileName of files) {
          const filePath = path.join(baseDir, fileName);
          const fileBuffer = await readFile(filePath);
          const s3Key = `${bucketDir}/${fileType}/${fileName}`;

          // Upload to S3
          // await this.awsUtility.uploadFileInS3(s3Key, bucket, fileBuffer);

          let tableName: string | undefined;
          const formattedFileName = fileName.toLowerCase(); // To avoid case-sensitivity issues

          if (formattedFileName.includes('region_lookup_by_zip')) {
            tableName = 'region_lookup_by_zip_new';
          } else if (formattedFileName.includes('tiers_per_state')) {
            tableName = 'tiers_per_state_new';
          } else if (formattedFileName.includes('volume_pricing_brackets')) {
            tableName = 'volume_pricing_brackets_new';
          } else if (formattedFileName.includes('regional_volume_pricing_lookup_')) {
            // For regional volume pricing lookup files
            // Extract state code from filename
            const match = formattedFileName.match(/regional_volume_pricing_lookup_([a-z]{2})/i);
            if (match && match[1]) {
              const stateCode = match[1].toUpperCase();
              tableName = `regional_volume_pricing_lookup_${stateCode}_new`;
              console.log(`Detected state code: ${stateCode} from filename: ${fileName}`);
            }
          }
          if (!tableName) {
            console.log("Skipping file due to unknown type:", filePath);
            continue;
          }

          // Store buffer for database import
          fileBuffersForImport.push({ buffer: fileBuffer, fileName });

          // Import to database in background without await
          this.importFileBufferToDb(fileBuffer, fileType, uploadId, fileName, tableName).catch((error: any) => {
            console.log(`Background import error for ${fileName}:`, error);
          });
        }

        return {
          success: true,
          message: 'All pricing files uploaded to S3 successfully and database import started in background',
          fileCount: files.length,
        };
      }

      if (!fileName) {
        console.log("processExportFileAndUploadToS3-3 error: ", fileName);
        return { error_message: 'No valid file found in exports directory' };
      }

      return {
        success: true,
        message: 'File uploaded to S3 successfully and database import started in background',
        s3Key: `${bucketDir}/${fileType}/${fileName}`,
        localPath: path.join(baseDir, fileName),
      };
    } catch (error) {
      console.log("processExportFileAndUploadToS3 error: ", error);
      return { error_message: 'Error uploading file to S3' };
    }
  }

  async importFileBufferToDb(fileBuffer: Buffer, fileType: 'search' | 'pricing', uploadId: string, fileName: string, tableName?: string) {
    try {
      console.log(`Starting database import for ${fileType} file: ${fileName}, uploadId: ${uploadId}`);

      // Use ExcelJS to read the buffer and convert to JSON
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(fileBuffer);

      // Get the first worksheet
      const worksheet = workbook.worksheets[0];
      if (!worksheet) {
        console.log('No worksheet found in file');
        return;
      }

      // Convert sheet to JSON with header row as keys
      const data: any[] = [];
      let headers: string[] = [];

      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) {
          // First row contains headers
          headers = row.values as string[];
          headers = headers.slice(1); // Remove the first empty element that ExcelJS adds
        } else {
          // Data rows
          const rowData: any = {};
          const rowValues = row.values as any[];

          // Skip the first empty element and map values to headers
          for (let i = 1; i < rowValues.length; i++) {
            const headerIndex = i - 1;
            if (headers[headerIndex]) {
              let cellValue = rowValues[i];

              // Handle formula results - get the calculated value
              if (cellValue && typeof cellValue === 'object') {
                if ('formula' in cellValue) {
                  cellValue = cellValue.result;
                } else if ('text' in cellValue) {
                  cellValue = cellValue.text;
                } else if ('value' in cellValue) {
                  cellValue = cellValue.value;
                }
              }

              rowData[headers[headerIndex]] = cellValue;
            }
          }

          // Only add row if it has some data
          if (Object.keys(rowData).length > 0) {
            data.push(rowData);
          }
        }
      });

      if (data.length === 0) {
        console.log('Excel file is empty or has no data rows');
        return;
      }

      console.log(`Converted Excel to JSON: ${data.length} rows, Headers: ${headers.join(', ')}`);

      if (fileType === Constants.SEARCH_FILE_TYPE) {
        await this.importSearchDataToDb(data, headers, uploadId, fileName);
      } else if (fileType === Constants.PRICING_FILE_TYPE) {
        await this.importPricingDataToDb(data, headers, uploadId, fileName);
      }

      console.log(`Database import completed for ${fileType} file: ${fileName}`);
    } catch (error) {
      console.log(`Error importing ${fileType} file ${fileName} to database:`, error);
      // Log the error but don't throw to avoid breaking the main flow
      this.createLogData('importFileBufferToDb', uploadId, 'IMPORT_ERROR', {
        fileType,
        fileName,
        error: error?.message || error
      });
    }
  }

  private async importSearchDataToDb(workbook: any, uploadId: string, fileName: string) {
    try {
      console.log(`Importing search data to database for uploadId: ${uploadId}`);

      // Get the first worksheet
      const worksheet = workbook.worksheets[0];
      if (!worksheet) {
        console.log('No worksheet found in search file');
        return;
      }

      // Process rows and import to database
      // This is a placeholder - you would implement the actual database import logic here
      // based on your database schema and requirements

      let rowCount = 0;
      worksheet.eachRow((_row: any, rowNumber: number) => {
        if (rowNumber === 1) return; // Skip header row

        // Extract data from row and prepare for database insert
        // This would depend on your specific database schema
        rowCount++;

        // Log progress every 1000 rows
        if (rowCount % 1000 === 0) {
          console.log(`Processed ${rowCount} search data rows...`);
        }
      });

      console.log(`Imported ${rowCount} search data rows to database`);

      // Log successful import
      this.createLogData('importSearchDataToDb', uploadId, 'IMPORT_SUCCESS', {
        fileName,
        rowCount
      });

    } catch (error) {
      console.log('Error importing search data to database:', error);
      throw error;
    }
  }

  private async importPricingDataToDb(workbook: any, uploadId: string, fileName: string) {
    try {
      console.log(`Importing pricing data to database for uploadId: ${uploadId}, fileName: ${fileName}`);

      // Get the first worksheet
      const worksheet = workbook.worksheets[0];
      if (!worksheet) {
        console.log('No worksheet found in pricing file');
        return;
      }

      // Process rows and import to database
      // This is a placeholder - you would implement the actual database import logic here
      // based on your database schema and requirements

      let rowCount = 0;
      worksheet.eachRow((_row: any, rowNumber: number) => {
        if (rowNumber === 1) return; // Skip header row

        // Extract data from row and prepare for database insert
        // This would depend on your specific database schema
        rowCount++;

        // Log progress every 1000 rows
        if (rowCount % 1000 === 0) {
          console.log(`Processed ${rowCount} pricing data rows...`);
        }
      });

      console.log(`Imported ${rowCount} pricing data rows to database`);

      // Log successful import
      this.createLogData('importPricingDataToDb', uploadId, 'IMPORT_SUCCESS', {
        fileName,
        rowCount
      });

    } catch (error) {
      console.log('Error importing pricing data to database:', error);
      throw error;
    }
  }

  async deleteLocalFile(uploadId: string) {
    console.log(`deleteLocalFile ${uploadId} not found`);

    const deleted = await this.deleteLocalUpload(uploadId);
    if (!deleted) {
      console.log(`Upload ID ${uploadId} not found`);
      return {error_message: `Upload ID ${uploadId} not found`};
    }
    return 'File deleted successfully';
  }

  async deleteLocalUpload(uploadId: string) {
    // Find the upload directory
    const uploadDirectory = await this.findUploadDir(uploadId);
    console.log("deleteLocalUpload uploadDirectory: ", uploadDirectory);
    if (!uploadDirectory) {
      return false;
    }

    // Delete all files recursively
    await this.deleteLocalDirectory(uploadDirectory);

    return true;
  }

  async deleteLocalDirectory(dir: string) {
    const entries = await readdir(dir);
    for (const entry of entries) {
      const entryPath = path.join(dir, entry);
      const entryStat = await stat(entryPath);

      if (entryStat.isDirectory()) {
        await this.deleteLocalDirectory(entryPath);
      } else {
        await unlink(entryPath);
      }
    }
    console.log("Remove directory: ", dir);
    await rmdir(dir);
  }

  async validateUploadedFile(file) {
    try {
      if (!file) {
        return { error_message: 'File not found!' };
      }

      // Validate file type (additional check even though we have the validator)
      if (!file.originalname.match(/\.(xlsx|xls)$/)) {
        return { error_message: 'Only Excel files are allowed' };
      }

      // Check if file buffer exists
      if (!file.buffer) {
        return { error_message: 'File buffer is empty' };
      }

      // Validate Excel file content
      try {
        // Use a simpler Excel validation approach with the xlsx library
        const XLSX = require('xlsx');
        const workbook = XLSX.read(file.buffer, { type: 'buffer' });

        // Check if the workbook has at least one worksheet
        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
          return { error_message: 'Excel file does not contain any worksheets' };
        }

        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const sheetData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

        if (!sheetData || sheetData.length === 0 || sheetData.every(row => row.length === 0)) {
          return { error_message: 'Excel file is empty or has no data' };
        }

      } catch (error) {
        console.log(`Excel validation error: ${error}`);
        return { error_message: 'Invalid Excel file format. The file may be corrupted.' };
      }

      return true;
    } catch (error) {
      console.log(`Uploaded Excel File Validation Error: ${error}`);
      return false;
    }
  }

  async saveUploadedFileToS3(file, uploadId: string) {
    try {
      const bucket = process.env.UPLOAD_PRODUCT_PRICING_EXCEL_BUCKET;
      const bucketDir = process.env.S3_BUCKET_ENVIRONMENT;

      const fileExtension = path.extname(file.originalname);

      let key = `${bucketDir}/${uploadId}${fileExtension}`;

      await this.awsUtility.uploadFileInS3(key, bucket, file.buffer)

    } catch (error) {
      console.log(`Error uploading original file in S3 bucket : ${error}`);
      return { error_message: `Error uploading original file in S3 bucket: ${error?.message}` };
    }
  }

  async insertExcelDataToDB(filePath, tableName) {
    try {
      // Read Excel file
      console.log('Reading Excel file:', filePath);
      const { headers, data } = await this.readExcelFile(filePath);
      console.log(`Found ${data.length} rows with headers:`, headers);

      // Initialize DataSource
      console.log('Initializing TypeORM DataSource...');
      const dataSource = await this.initializeDataSource();

      // Truncate the table before importing new data
      console.log(`Truncating table ${tableName} before import...`);
      await this.dataSource.query(`TRUNCATE TABLE ${tableName}`);
      console.log(`Table ${tableName} truncated successfully`);

      // Prepare column names once (outside the loop)
      const columnNames = [];
      for (const header of headers) {
        const sanitizedName = await this.sanitizeColumnName(header);
        columnNames.push(sanitizedName);
      }

      // Prepare for bulk insert
      let successCount = 0;
      let errorCount = 0;
      const batchSize = 500; // Insert 500 records at a time

      // Process data in batches
      for (let i = 0; i < data.length; i += batchSize) {
        try {
          const batch = data.slice(i, i + batchSize);
          console.log(`Processing batch ${Math.floor(i / batchSize) + 1} (${i} to ${Math.min(i + batchSize, data.length)} of ${data.length} rows)...`);

          if (batch.length === 0) continue;

          // Prepare bulk insert values
          const allPlaceholders = [];
          const allValues = [];

          // Process each row in the batch
          for (const row of batch) {
            const rowPlaceholders = [];
            const rowValues = [];

            for (const header of headers) {
              let value = row[header];
              // console.log("value:", value, typeof value);
              // Handle different data types
              if (value === null || value === undefined || value === '') {
                value = null;
              } else if (typeof value === 'number') {
                // Check if this is likely a date column by header name
                const headerLower = header.toLowerCase();
                const isLikelyDateColumn =
                  headerLower.includes('date') ||
                  headerLower.includes('time') ||
                  headerLower.endsWith('_at') ||
                  headerLower.endsWith('_on') ||
                  headerLower === 'created' ||
                  headerLower === 'updated' ||
                  headerLower === 'timestamp';

                // Only convert to date if it's likely a date column and falls in date range
                if (isLikelyDateColumn && value > 25569 && value < 73050) {
                  // Convert Excel date numbers to proper dates
                  const date = new Date((value - 25569) * 86400 * 1000);
                  if (!isNaN(date.getTime())) {
                    value = date;
                  }
                }
              }

              rowPlaceholders.push('?');
              rowValues.push(value);
            }

            // Add this row's placeholders and values
            allPlaceholders.push(`(${rowPlaceholders.join(', ')})`);
            allValues.push(...rowValues);
          }

          // Execute bulk insert
          const bulkInsertSQL = `INSERT INTO ${tableName} (${columnNames.join(', ')}) VALUES ${allPlaceholders.join(', ')}`;
          await this.dataSource.query(bulkInsertSQL, allValues);

          successCount += batch.length;
          console.log(`Batch ${Math.floor(i / batchSize) + 1} completed. Total processed: ${successCount} rows`);

        } catch (error) {
          console.log(`Error inserting batch starting at row ${i + 1}:`, error.message);
          console.log(`Error details: ${error.message}`);
          errorCount += Math.min(batchSize, data.length - i);
        }
      }

      return {
        totalRows: data.length,
        successCount,
        errorCount
      };
    } catch (error) {
      console.log('Error in insertExcelDataToDB:', error);
      throw error;
    }
  }

   // Function to read Excel file and extract data
   async readExcelFile(filePath) {
    try {
      const workbook = xlsx.readFile(filePath);
      const sheetName = workbook.SheetNames[0]; // Use first sheet
      const worksheet = workbook.Sheets[sheetName];

      // console.log(`Starting database import for ${fileType} file: ${fileName}, uploadId: ${uploadId}`);

      // Use ExcelJS to read the buffer and convert to JSON
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(fileBuffer);

      // Get the first worksheet
      const worksheet = workbook.worksheets[0];

      // Convert sheet to JSON with header row as keys
      const data = xlsx.utils.sheet_to_json(worksheet);

      if (data.length === 0) {
        throw new Error('Excel file is empty or has no data rows');
      }

      // Get headers from the first row
      const headers = Object.keys(data[0]);

      return { headers, data };
    } catch (error) {
      throw new Error(`Failed to read Excel file: ${error.message}`);
    }
  }


  // Function to initialize DataSource if not already initialized
  async initializeDataSource() {
    if (!this.dataSource.isInitialized) {
      await this.dataSource.initialize();
      console.log('TypeORM DataSource initialized');
    }
    return this.dataSource;
  }

  // Function to create dynamic entity schema
  async createDynamicEntitySchema(tableName, headers, sampleData) {
    const columns = {};

    // Add ID column
    columns['id'] = {
      type: 'int',
      primary: true,
      generated: true
    };

    // Add columns based on Excel headers
    headers.forEach(async header => {
      const sanitizedName = await this.sanitizeColumnName(header);
      const sampleValue = sampleData[header];
      const columnType = await this.getTypeORMColumnType(sampleValue);

      columns[sanitizedName] = {
        type: columnType,
        length: columnType === 'varchar' ? 255 : undefined,
        precision: columnType === 'decimal' ? 10 : undefined,
        scale: columnType === 'decimal' ? 2 : undefined,
        nullable: true
      };
    });

    // Add created_at column
    columns['created_at'] = {
      type: 'timestamp',
      default: () => 'CURRENT_TIMESTAMP'
    };

    return new EntitySchema({
      name: tableName,
      tableName: tableName,
      columns: columns
    });
  }

  // Function to sanitize column names for SQL
  async sanitizeColumnName(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9_]/g, '_') // Replace non-alphanumeric chars with underscore
      .replace(/^[0-9]/, '_$&')    // Prefix with underscore if starts with number
      .replace(/_+/g, '_')         // Replace multiple underscores with single
      .replace(/^_|_$/g, '');      // Remove leading/trailing underscores
  }

  // Function to get TypeORM column type based on value
  async getTypeORMColumnType(value) {
    if (typeof value === 'number') {
      return Number.isInteger(value) ? 'int' : 'decimal';
    } else if (value instanceof Date) {
      return 'datetime';
    } else {
      return 'varchar';
    }
  }

  /**
 * Check if a table exists in the database
 * @param tableName The name of the table to check
 * @returns boolean indicating if the table exists
 */
  async checkIfTableExists(tableName: string): Promise<boolean> {
    try {
      // This query works for MySQL/MariaDB
      const result = await this.dataSource.query(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = ?
    `, [tableName]);

      return result[0].count > 0;
    } catch (error) {
      console.log(`Error checking if table ${tableName} exists:`, error);
      return false;
    }
  }

}