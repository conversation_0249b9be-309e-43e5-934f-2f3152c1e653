import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { getConnectionOptions } from 'typeorm';
import { OConstants } from './OConstants';
import { BaseLibraryModule, DataBaseService } from '@bryzos/base-library';
import { HttpModule } from '@nestjs/axios';
import { AuthService } from '@bryzos/base-library/dist/src/auth/auth.service';
import { AwsUtility } from '@bryzos/extended-widget-library';

@Module({
  imports: [
    BaseLibraryModule,
    DataBaseService,
    HttpModule,
    TypeOrmModule.forRootAsync({
      useFactory: async () =>
        Object.assign(await getConnectionOptions(), {
          autoLoadEntities: true,
        }),
    }),
    TypeOrmModule.forFeature(OConstants.EntityArray),
  ],
  providers: [
    BaseLibraryModule,
    DataBaseService,
    AuthService,
    AwsUtility,
    ...OConstants.ServiceArray,
  ],
  exports: [
    BaseLibraryModule,
    DataBaseService,
    TypeOrmModule.forFeature(OConstants.EntityArray),
    ...OConstants.ServiceArray,
  ],
})

export class SharedModule {}

