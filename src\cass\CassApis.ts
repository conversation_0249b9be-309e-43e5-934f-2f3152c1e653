import { BaseLibraryService, DataBaseService, User } from "@bryzos/base-library";
import { Constants } from "../Constants";
import { Utils } from "../utils";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { CassAdhocSellerSetup, ReferenceDataGeneralSettings, UserArPaymentInfo } from "@bryzos/extended-widget-library";
import axios from 'axios';
import { Injectable } from "@nestjs/common";

const responseTag = Constants.RESPONSE_TAG;
const responseErrorTag = Constants.ERROR_TAG;

@Injectable()
export class CassApis {
  private dbObjService = new DataBaseService();

  constructor(
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(ReferenceDataGeneralSettings) private readonly referenceDataGeneralSettingsRepository: Repository<ReferenceDataGeneralSettings>,
    @InjectRepository(UserArPaymentInfo) private readonly userArPaymentInfoRepository: Repository<UserArPaymentInfo>,
    @InjectRepository(CassAdhocSellerSetup) private readonly cassAdhocSellerRepository: Repository<CassAdhocSellerSetup>,

    private readonly baseLibraryService: BaseLibraryService,
    private readonly utils: Utils,) {

  }

  async getCassAccessToken(userId) {
    let response: any = null;
    if (userId) {
      let filenameLogging = 'Admin Cass file';
      let logEvent = 'Get CASS access Token';
      let referenceId = userId;
      let cassAuthTokenUrl = process.env.CASS_BASE_URL + '/auth/token';
      let cassPassword = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_CASS, Constants.PASSWORD);

      let postData = { userName: process.env.CASS_TOKEN_USER, password: cassPassword };

      let encodedPostData = JSON.stringify(postData);
      let jsonParsed: any = null;
      jsonParsed = (await axios.post(cassAuthTokenUrl, encodedPostData, { headers: { 'Content-Type': 'application/json' } })).data;

      if ((typeof jsonParsed === 'object' && 'errors' in jsonParsed) || 'error_message' in jsonParsed) {
        const data = { message_attribute: jsonParsed['errors'], info: 'CASS access token request response ERROR', referenceId: userId };
        this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

        return { [responseErrorTag]: 'Something went wrong' };
      }

      let accessToken = jsonParsed['token'] ? jsonParsed['token'] : null;

      const data = {
        message_attribute: referenceId,
        info: 'CASS access token request response',
        referenceId: userId,
      };
      this.utils.createLogData(filenameLogging, referenceId, logEvent, data);

      response = { [responseTag]: { access_token: accessToken } };
    }
    return response;
  }

  async getCassUser(accessToken, cassUserId, userType, userId, isDefaultAccount, bryzosId, isAdhocUser=null) {
    let response = null;
    let filenameLogging = 'Admin Cass file';
    let referenceId = userId;
    let logEvent = 'Get CASS user data';
    let urlCassUserId = cassUserId.replace(' ', '%20');

    if (cassUserId) {
      try {
        let getCassSellerUrl = `${process.env.CASS_BASE_URL}/masterData/GetMasterData/${process.env.CASS_CLIENT_KEY}/${urlCassUserId}`;

        //get CASS seller curl

        let jsonParsed = (await axios.get(getCassSellerUrl, { headers: { Accept: 'application/json', Authorization: `Bearer ${accessToken}`, }, })).data;

        if (jsonParsed) {
          if (userType == Constants.BUYER) {
            if (!isDefaultAccount && jsonParsed['drawdownAccounts'] && jsonParsed['entity']['name']) {
              //check display name
              if (cassUserId == jsonParsed['entity']['referenceNumber']) {
                //check account number
                let default_account = await this.getDefaultBankDetails();
                let cassAccountNumber = jsonParsed['drawdownAccounts']?.[0]?.['accountInformation']?.['accountNumber']?.substring(0, -2);
                let cassRoutingNumber = jsonParsed['drawdownAccounts']?.[0]?.['accountInformation']?.['routingNumber']?.substring(0, -2);
                if (default_account?.['account_number']?.substring(0, -2) == cassAccountNumber && default_account?.['routing_number']?.substring(0, -2) == cassRoutingNumber) {
                  response = true;
                }
              }
            } else {
              if (jsonParsed['drawdownAccounts'] && jsonParsed['entity']['name'] != null && jsonParsed['entity']['referenceNumber'] == cassUserId) {
                response = true;
              }
            }
          } else if (userType == Constants.SELLER) {
            if (!isDefaultAccount) {
              let sellerInfo = null;
              if(isAdhocUser){
                sellerInfo = await this.dbObjService.findOne(this.cassAdhocSellerRepository, 'id', bryzosId);
              }else{
                sellerInfo = await this.getSellerInfo(bryzosId);
              }

              if (sellerInfo) {
                if (jsonParsed['disbursementAccounts'] && jsonParsed['entity']?.['referenceNumber'] == cassUserId) {
                  //get seller info
                  let cassAccountNumber = jsonParsed?.disbursementAccounts?.[0]?.accountInformation?.accountNumber.slice(-4);
                  let cassRoutingNumber = jsonParsed?.disbursementAccounts?.[0]?.accountInformation?.routingNumber.slice(-4);

                  if (sellerInfo.routing_number.slice(-4) == cassRoutingNumber && sellerInfo.account_number.slice(-4) == cassAccountNumber) {
                    response = true;
                  }
                }
              }
            } else {
              if (jsonParsed['disbursementAccounts'] && jsonParsed['entity']?.['referenceNumber'] == cassUserId) {
                response = true;
              }
            }
          }

          let data = {
            message_attribute: Buffer.from(JSON.stringify(jsonParsed), 'base64'), info: 'CASS get user request response. Cass.php line 105', userType: userType, bryzosId: bryzosId, cass_user_id: cassUserId, requested_by: userId,
          };
          this.utils.createLogData(filenameLogging, referenceId, logEvent, data);
        } else {
          let data = {
            message_attribute: JSON.stringify(jsonParsed), info: 'CASS get user request ERROR', cass_user_id: cassUserId, requested_by: userId, userType: userType, bryzosId: bryzosId,
          };
          this.utils.createLogData(filenameLogging, referenceId, logEvent, data);
        }
      } catch (e) {
        this.utils.logInfo(e);
      }
    }
    return response;
  }

  async checkUserExistsInCass(cassAccountId, userType, userId, isDefaultAccount = false, bryzosId = null, isAdhocUser=null) {
    let response = null;
    let cassAccessToken = await this.getCassAccessToken(userId);
    if (cassAccessToken?.[responseTag]?.['access_token']) {
      let accessToken = cassAccessToken[responseTag]['access_token'];
      let cassUserResult = await this.getCassUser(accessToken, cassAccountId, userType, userId, isDefaultAccount, bryzosId, isAdhocUser);
      if (cassUserResult) {
        response = cassAccountId;
      }
    }
    return response;
  }

  async getCassDefaultAccount(key, userType, userId = 0) {
    let response = null;
    //name from secret manager
    let cassDefaultAccount = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_CASS, key);
    if (cassDefaultAccount) {
      response = this.checkUserExistsInCass(cassDefaultAccount, userType, userId, true,);
    }
    return response;
  }

  async createCassBuyer(buyerId, userId) {
    let response = null;
    let filenameLogging = 'Cass.php';
    let logEvent = 'Create CASS buyer';
    let cassAccessToken = await this.getCassAccessToken(userId);
    if (cassAccessToken['data'] && buyerId) {
      let accessToken = cassAccessToken['data']['access_token'];
      //get buyer details
      let buyer = (await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(
        this.userRepository,
        [
          { table: 'user_buying_preference', joinColumn: 'user_id', mainTableColumn: 'id' },
          { table: 'reference_data_states', joinColumn: 'id', mainTableColumn: 'company_address_state_id', joinTable: "user_buying_preference" },
        ],
        [
          { column: 'id', operator: '=', value: buyerId, },
          { column: 'is_active', operator: '=', value: true, table: 'user_buying_preference' },
        ],
        {
          selectFields: [
            'user_buying_preference.user_id AS user_id',
            'user_buying_preference.email_id AS email_id',
            'user_buying_preference.first_name AS first_name',
            'user_buying_preference.last_name AS last_name',
            'user_buying_preference.company_name AS main_company_name',
            'user_buying_preference.phone AS office_phone',
            'user_buying_preference.company_address_line1 AS main_company_line1',
            'user_buying_preference.company_address_line2 AS main_company_line2',
            'user_buying_preference.company_address_city AS main_company_city',
            'user_buying_preference.company_address_zip AS main_company_zip',
            'reference_data_states.code AS main_company_state_code',
          ],
        },
        null,
        null,
      ))?.[0];
      //get default account numbers
      let deafultAccountNumbers = await this.getDefaultBankDetails();
      if (deafultAccountNumbers && buyer) {
        let headers = { 'Content-Type': 'application/json', Authorization: `Bearer ${accessToken}` };

        let cassUniqueIdentifier = null;
        let cassUniqueIdentifierPrefix = (await this.dbObjService.findOneByMultipleWhere(this.referenceDataGeneralSettingsRepository, { name: Constants.CASS_BUYER_UNIQUE_IDENTIFIER_PREFIX, is_active: true }))?.value;
        let cassUniqueIdentifierTag = (await this.dbObjService.findOneByMultipleWhere(this.referenceDataGeneralSettingsRepository, { name: Constants.CASS_UNIQUE_IDENTIFIER_TAG, is_active: true }))?.value;
        if (cassUniqueIdentifierPrefix && cassUniqueIdentifierTag) {
          cassUniqueIdentifier = cassUniqueIdentifierPrefix + buyer[cassUniqueIdentifierTag];
        } else {
          return response;
        }

        let buyerName = buyer.first_name + ' ' + buyer.last_name;
        buyerName = buyerName.substring(0, 40);

        let userData = {
          clientKey: process.env.CASS_CLIENT_KEY,
          uniqueIdentifier: cassUniqueIdentifier, // max length=50, min=3
          uniqueGroupIdentifier: buyer.user_id, // max length=50, min=0
          masterDataType: Constants.CASS_BUYER_MASTER_DATA_TYPE, // max length=8, min=0
          masterDataName: buyer.main_company_name.substring(0, 40), // max length=40, min=0
          phoneNumber: buyer.office_phone, // max length=20, min=0
          primaryContactName: buyerName, // max length=40, min=0
          primaryContactEmailAddress: buyer.email_id, // max length=50, min=0
          remitEmailAddresses: process.env.REMIT_EMAIL_ADDRESS, // max length=200, min=0
          address1: buyer.main_company_line1.substring(0, 40), // max length=40, min=0
          address2: buyer.main_company_line2 ? buyer.main_company_line2.substring(0, 40) : null, // max length=40, min=0
          city: buyer.main_company_city.substring(0, 40), // max length=40, min=0
          stateOrProvince: buyer.main_company_state_code, // max length=2, min=0
          zipOrPostalCode: buyer.main_company_zip, // max length=10, min=0
          drawdownMethod: process.env.CASS_BUYER_DRAWDOWNMETHOD, // string max length=16, min=0
          drawdownTransactionCode: Constants.CASS_BUYER_DRAWDOWN_TRANSACTION_CODE, // int max=88 min=21
          drawdownRoutingNumber: deafultAccountNumbers['routing_number']?.toString(), // max=9 min=0
          drawdownAccountNumber: deafultAccountNumbers['account_number'], // max=17 min=0
          refundMethod: process.env.CASS_BUYER_REFUND_METHOD, // max=9 min=0
        };

        let encodedUserData = JSON.stringify(userData);
        let cassCreateBuyerUrl = process.env.CASS_BASE_URL + '/masterData/SaveMasterData';

        let jsonParsed = (await axios.post(cassCreateBuyerUrl, encodedUserData, { headers: headers, }))?.data;

        //log into loggly
        let encryptedRequestData = await this.utils.getEncodedData(encodedUserData,);
        let data = {
          message: { user_data: encryptedRequestData, request_url: cassCreateBuyerUrl, buyer_id: buyerId, cass_response: jsonParsed, buyer_unique_identifier: cassUniqueIdentifier },
          info: 'Create CASS Buyer request response',
        };
        this.utils.createLogData(filenameLogging, userId, logEvent, data);

        if (jsonParsed) {
          if (jsonParsed['validationErrors']?.length) {
            response = { [responseErrorTag]: jsonParsed['validationErrors'][0]['errorMessage'] };
          } else if (jsonParsed['errors']) {
            response = { [responseErrorTag]: JSON.stringify(jsonParsed['errors']) };
          } else if (jsonParsed['uniqueIdentifier'] == cassUniqueIdentifier) {
            response = cassUniqueIdentifier;
          }
        }
      }
    }
    return response;
  }

  async getDefaultBankDetails() {
    let bankDetails = [];
    bankDetails['account_number'] = (await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.DAN))?.toString();
    bankDetails['routing_number'] = (await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.DRN))?.toString();
    return bankDetails;
  }

  getSellerInfo = async (sellerId) => {
    let seller = null;
    if (sellerId) {
      seller = (await this.dbObjService.FindManyByMultipleConditionAndLeftJoin(this.userArPaymentInfoRepository,
        [ { table: 'user', joinColumn: 'id', mainTableColumn: 'user_id' }, { table: 'user_selling_preference', joinColumn: 'user_id', mainTableColumn: 'id', joinTable: "user" } ],
        [ { column: 'is_active', operator: '=', value: true }, { column: 'user_id', operator: '=', value: sellerId }, { column: 'is_active', operator: '=', value: true, table: 'user' }, { column: 'is_active', operator: '=', value: true, table: 'user_selling_preference' } ], 
        { selectFields: ['table1.*', 'user_selling_preference.company_name AS main_company_name'] }, null, null))?.[0];
    }
    return seller;
  };
}