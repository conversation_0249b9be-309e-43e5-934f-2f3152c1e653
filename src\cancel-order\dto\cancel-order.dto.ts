import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsOptional } from "class-validator";
import { BaseDto } from "src/base.dto";

export class CancelOrderSettingDto {
    @ApiProperty() @IsNotEmpty() @IsOptional() @IsString() order_cancellation_reminder: string;
    @ApiProperty() @IsNotEmpty() @IsOptional() @IsString() order_cancellation_hours: string;
}

export class SaveCancelOrderSettingDto extends BaseDto {
    @Type(() => CancelOrderSettingDto)
    @ApiProperty() data: CancelOrderSettingDto;
}

export class CancelOrderDto {
    @ApiProperty() @IsNotEmpty()  @IsString() po_number: string;
    @ApiProperty() @IsNotEmpty()  @IsString() type: string;
    @ApiProperty() @IsNotEmpty()  @IsOptional() purchase_order_line_id: Record<string, string>;
}

export class SaveCancelOrderDto extends BaseDto {
    @Type(() => CancelOrderDto)
    @ApiProperty() data: CancelOrderDto;
}