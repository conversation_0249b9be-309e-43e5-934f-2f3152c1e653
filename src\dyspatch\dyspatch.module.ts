import { MiddlewareConsumer, Module } from '@nestjs/common';
import { DyspatchController } from './dyspatch.controller';
// import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import { AdminPermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [DyspatchController],
//   providers: OConstants.ServiceArray
// })

@Module({
  imports: [SharedModule],
  controllers: [DyspatchController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class DyspatchModule {
  configure(consumer: MiddlewareConsumer) {
		consumer
		  .apply(AdminPermissionMiddleware)
		  .forRoutes('/dyspatch');
      consumer
      .apply(LoggerMiddleware)
      .exclude('/dyspatch/get_templates') // exclude get api
      .forRoutes('/dyspatch');
		}
}
