export const configuration = () => ({
    NODE_ENV: process.env.NODE_ENV,
    PORT: parseInt(process.env.PORT, 10) || 8080,
    API_KEY:process.env.API_KEY,
    MASTER_DB:process.env.MASTER_DB,

    LOGGLY_TOKEN:process.env.LOGGLY_TOKEN,
    LOGGLY_SUBDOMAIN:process.env.LOGGLY_SUBDOMAIN,
    LOGGLY_USERNAME:process.env.LOGGLY_USERNAME,
    LOGGLY_PASSWORD:process.env.LOGGLY_PASSWORD,
    LOGGLY_ERROR_TAG:process.env.LOGGLY_ERROR_TAG,
    LOGGLY_REQUEST_RESPONSE_TAG:process.env.LOGGLY_REQUEST_RESPONSE_TAG,

    RAYGUN_API:process.env.RAYGUN_API,
  });