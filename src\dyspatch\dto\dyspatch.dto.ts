import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsString, IsOptional } from "class-validator";
import { BaseDto } from "src/base.dto";

export class DyspatchDto {
    @ApiProperty() @IsNotEmpty() @IsString() template_id: string;
}

export class SaveDyspatchDto extends BaseDto {
    @Type(() => DyspatchDto)
    @ApiProperty() data: DyspatchDto;
}
