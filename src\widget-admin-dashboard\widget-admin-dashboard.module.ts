import { MiddlewareConsumer, Module } from '@nestjs/common';
import { WidgetAdminDashboardController } from './widget-admin-dashboard.controller';
// import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import { AdminPermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,HttpModule, DataBaseService, TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [WidgetAdminDashboardController],
//   providers:  [WidgetAdminDashboardService , ...OConstants.ServiceArray]
// })

@Module({
  imports: [SharedModule],
  controllers: [WidgetAdminDashboardController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class WidgetAdminDashboardModule {
  configure(consumer: MiddlewareConsumer) {
		consumer
		  .apply(AdminPermissionMiddleware)
		  .forRoutes('/widget-admin-dashboard');
      // exclude get api
    consumer
      .apply(LoggerMiddleware)
      .exclude('/widget-admin-dashboard/getNotificationEvents', '/widget-admin-dashboard/getAllPo', '/widget-admin-dashboard/orders', '/widget-admin-dashboard/increaseCreditLimt', '/widget-admin-dashboard/widgetGrid', '/widget-admin-dashboard/achCreditOrder', '/widget-admin-dashboard/bryzosPay', '/widget-admin-dashboard/getAllOpenPo', '/widget-admin-dashboard/getAdminReferenceData', '/widget-admin-dashboard/getSafeUploads', '/widget-admin-dashboard/safeImgixImageKit', '/widget-admin-dashboard/indicator', '/widget-admin-dashboard/getAchCreditPayementApprovalList','/widget-admin-dashboard/external-api-keys', '/widget-admin-dashboard/external-api-key/mapping', '/widget-admin-dashboard/sandbox/external-api-keys', '/widget-admin-dashboard/sandbox/external-api-key/mapping')
      .forRoutes('/widget-admin-dashboard');
		}
}
