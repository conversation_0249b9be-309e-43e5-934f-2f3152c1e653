import { MiddlewareConsumer, Module } from '@nestjs/common';
import { CashInAdvanceDepositController } from './cash-in-advance-deposit.controller';
// import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import { AdminPermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';


// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [CashInAdvanceDepositController],
//   providers: OConstants.ServiceArray
// })

@Module({
  imports: [SharedModule],
  controllers: [CashInAdvanceDepositController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class CashInAdvanceDepositModule {
  configure(consumer: MiddlewareConsumer) {
		consumer
		  .apply(AdminPermissionMiddleware)
		  .forRoutes('/cash_in_advance');
    consumer
		  .apply(LoggerMiddleware)
		  .exclude('/cash_in_advance/custom_deposit') // exclude get api
		  .forRoutes('/cash_in_advance');
		}
}
