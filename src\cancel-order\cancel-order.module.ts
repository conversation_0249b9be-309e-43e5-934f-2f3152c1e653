import { MiddlewareConsumer, Module } from '@nestjs/common';
import { CancelOrderContoller } from './cancel-order.controller';
// // import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import { AdminPermissionMiddleware, PermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';


// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [CancelOrderContoller],
//   providers: OConstants.ServiceArray
// })

@Module({
	imports: [SharedModule],
	controllers: [CancelOrderContoller],
	providers: [SharedModule],
	exports: [SharedModule],
  })

export class CancelOrderModule {

  configure(consumer: MiddlewareConsumer) {
		consumer
		  .apply(AdminPermissionMiddleware)
		  .forRoutes('/cancel_order/order','/cancel_order/settings','/cancel_order/orders');
		consumer
		  .apply(PermissionMiddleware)
		  .forRoutes('/cancel_order/user');
		consumer
		  .apply(LoggerMiddleware)
		  .exclude('/cancel_order/orders') // exclude get api
		  .forRoutes('/cancel_order');
		}
}