import { BaseDto } from '@bryzos/base-library';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDefined,
  IsNotEmpty,
  IsNotEmptyObject,
  IsString,
} from 'class-validator';

export class RemoveSalesTaxDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  po_number: string;
}

export class updateRemoveSalesTaxDto extends BaseDto {
  @Type(() => RemoveSalesTaxDto)
  @ApiProperty()
  data: RemoveSalesTaxDto;
}
