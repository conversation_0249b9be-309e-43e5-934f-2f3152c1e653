import { ApiProperty, getSchemaPath } from "@nestjs/swagger";
import { Entity, BaseEntity, PrimaryGeneratedColumn, Column } from "typeorm";

@Entity()
export class ReferenceDataProductsWidgetNew extends BaseEntity {
    @ApiProperty()
    @PrimaryGeneratedColumn()
    id: number;
    
    @Column({ nullable: true })
    Shape_ID: number;
  
    @Column({ nullable: true })
    Product_ID: number;
  
    @Column({ nullable: true })
    LBS_FT: string;

    @ApiProperty() @Column({ nullable: true }) Order_Increment_Ft : string
    @ApiProperty() @Column({ nullable: true }) Order_Increment_Ea : string
    @ApiProperty() @Column({ nullable: true }) Order_Increment_Lb : string
    @ApiProperty() @Column({ nullable: true }) Order_Increment_CWT : string
    @ApiProperty() @Column({ nullable: true }) Order_Increment_Net_Ton : string
    
    @Column({ nullable: true })
    Key1: string;
  
    @Column({ nullable: true })
    Key2: string;
  
    @Column({ nullable: true })
    Key3: string;
  
    @Column({ nullable: true })
    Key4: string;
  
    @Column({ nullable: true })
    Key5: string;
  
    @Column({ nullable: true })
    Key6: string;
  
    @Column({ nullable: true })
    Key7: string;
  
    @Column({ nullable: true })
    Key8: string;
  
    @Column({ nullable: true })
    Key9: string;
  
    @Column({ nullable: true })
    Key10: string;
  
    @Column({ nullable: true })
    Key11: string;
  
    @Column({ nullable: true })
    Key12: string;
  
    @Column({ nullable: true })
    Key13: string;
  
    @Column({ nullable: true })
    Key14: string;
  
    @Column({ nullable: true })
    Key15: string;
  
    @Column({ nullable: true })
    Key16: string;
  
    @Column({ nullable: true })
    Key17: string;
  
    @Column({ nullable: true })
    Key18: string;
  
    @Column({ nullable: true })
    Key19: string;
  
    @Column({ nullable: true })
    Key20: string;
  
    @Column({ nullable: true })
    Key21: string;
  
    @Column({ nullable: true })
    Key22: string;
  
    @Column({ nullable: true })
    Key23: string;
  
    @Column({ nullable: true })
    Key24: string;
  
    @Column({ nullable: true })
    UI_Description: string;

    @ApiProperty() @Column({ nullable: true }) QUM_Dropdown_Options : string
    @ApiProperty() @Column({ nullable: true }) PUM_Dropdown_Options : string
  
    @ApiProperty()
    @Column({ default: true })
    domestic_material_only: boolean;

    @ApiProperty()
    @Column({ default: false })
    is_safe_product_code: boolean;
    
    @ApiProperty()
    @Column({ default: true })
    is_active: boolean;

    @ApiProperty()
    @Column({ type: 'timestamp', default: () => "CURRENT_TIMESTAMP" })
    created_date:Date;
    
    @ApiProperty()
    @Column({ type: "timestamp",default: () => "CURRENT_TIMESTAMP()", onUpdate: "CURRENT_TIMESTAMP()" })
    time_stamp: Date;

}

@Entity()
export class TiersPerState extends BaseEntity {
    @ApiProperty()
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty()
    @Column({ nullable: false })
    state_abbreviation: string;

    @ApiProperty()
    @Column({ type: 'tinyint', nullable: false })
    tiers: number;
}

@Entity()
export class RegionLookupByZip extends BaseEntity {
    @ApiProperty()
    @PrimaryGeneratedColumn()
    id: number;

    @ApiProperty()
    @Column({ default: null })
    zip_code: string;

    @ApiProperty()
    @Column({ default: null })
    tier_1: boolean;

    @ApiProperty()
    @Column({ default: null })
    tier_2: boolean;

    @ApiProperty()
    @Column({ default: null })
    city: string;

    @ApiProperty()
    @Column({ default: null })
    county: string;

    @ApiProperty()
    @Column({ default: null })
    state: string;

    @ApiProperty()
    @Column({ default: null })
    state_abbreviation: string;

    @ApiProperty()
    @Column({ default: null })
    city_state: string;

    @ApiProperty()
    @Column({ default: true })
    is_active: number;

    @ApiProperty()
    @Column({ type: 'timestamp', default: () => "CURRENT_TIMESTAMP" })
    created_date: Date;
    
    @ApiProperty()
    @Column({ type: "timestamp",default: () => "CURRENT_TIMESTAMP()", onUpdate: "CURRENT_TIMESTAMP()" })
    time_stamp: Date;

    @ApiProperty()
    @Column({ default: null })
    tier_3: string;
}

@Entity()
export class VolumePricingBracketsNew {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ default: null })
    bracket: number;

    @Column({ default: null })
    low_value_lbs: number;

    @Column({ default: null })
    high_value_lbs: number;

    @Column({ default: null })
    default_bracket: boolean;

    @Column({ default: null })
    ui_dropdown_choice: string;

    @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
    value: number;

    @Column({ name: 'is_active', type: 'tinyint', default: 1 })
    is_active: boolean;

    @Column({ name: 'created_date', type: 'datetime', default: () => 'CURRENT_TIMESTAMP' })
    created_date: Date;

    @Column({ name: 'time_stamp', type: 'datetime', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    time_stamp: Date;
}
