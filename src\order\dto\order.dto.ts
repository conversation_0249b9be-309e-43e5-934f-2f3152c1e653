import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, ValidateNested, IsBoolean, IsNumber, IsArray, IsOptional, Min, IsDecimal, IsNotEmptyObject, IsObject  } from "class-validator";
import { BaseDto } from "src/base.dto";



export class CartItemsDto {
    @ApiProperty() @IsNotEmpty() @IsNumber() line_id: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() product_id: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() reference_product_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() qty_unit: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) buyer_price: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) seller_price: number;
    @ApiProperty() @IsNotEmpty() @IsString() price_unit: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) qty: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) total_weight: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) buyer_extended: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) seller_extended: number;
    @ApiProperty() @IsOptional () @IsBoolean() domestic_material_only: boolean;
}

export class AddNewLinesDto {
    @ApiProperty() @IsNotEmpty() @IsString() purchase_order_id: string;

    @ApiProperty() @IsNotEmpty() @IsArray()
    @Type(() => CartItemsDto)
    @ValidateNested({ each: true })
    cart_items: CartItemsDto[];
}

export class SaveNewOrderLinesDto extends BaseDto {
    @Type(() => AddNewLinesDto)
    @ApiProperty() data: AddNewLinesDto;
}

export class UpdateCartItemsDto {
    @ApiProperty() @IsNotEmpty() @IsString() purchase_order_line_id: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() line_id: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() product_id: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() reference_product_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() qty_unit: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) buyer_price : number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) seller_price : number;
    @ApiProperty() @IsNotEmpty() @IsString() price_unit: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) qty: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) total_weight : number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) buyer_extended : number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) seller_extended : number;
    @ApiProperty() @IsOptional () @IsBoolean() domestic_material_only: boolean;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) seller_calculation_price_per_unit : number;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) buyer_calculation_price_per_unit : number;
    @ApiProperty() @IsNotEmpty() @IsString() description : string;
    @ApiProperty() @IsNotEmpty() @IsString() shape : string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.0001) buyer_pricing_lb : number;
}

export class UpdateLinesDto {
    @ApiProperty() @IsNotEmpty() @IsString() purchase_order_id: string;

    @ApiProperty() @IsNotEmpty() @IsArray()
    @Type(() => UpdateCartItemsDto)
    @ValidateNested({ each: true })
    cart_items: UpdateCartItemsDto[];
}

export class UpdateOrderLinesDto extends BaseDto {
    @Type(() => UpdateLinesDto)
    @ApiProperty() data: UpdateLinesDto;
}

export class AddressDto {
    @ApiProperty({ nullable: true}) @IsOptional() @IsString() line1: string;
    @ApiProperty({ nullable: true}) @IsOptional() @IsString() city: string;
    @ApiProperty() @IsNumber() @IsNotEmpty() state_id: number;
    @ApiProperty() @IsString() @IsNotEmpty() zip: string;

}
export class SalesTaxOrderDto {
    @ApiProperty() @IsNotEmpty() @IsDecimal() price: string;
    @ApiProperty() @IsNotEmpty() @IsString() freight_term: string;
    @ApiProperty() @IsNotEmptyObject() @IsObject() @ValidateNested({ each: true }) @Type(() => AddressDto) shipping_details: AddressDto;
    @ApiProperty() @IsNotEmpty() @IsNumber() salesTaxCounter: number;
    @ApiProperty() @IsNotEmpty() @IsString() buyer_id: string;
    cart_items: any
}

export class SaveSalesTaxOrderDto extends BaseDto {
    @Type(() => SalesTaxOrderDto)
    @ApiProperty() data: SalesTaxOrderDto;
}