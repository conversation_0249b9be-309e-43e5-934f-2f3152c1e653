import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsNotEmptyObject,IsObject,ValidateNested,ValidateIf,IsDefined } from "class-validator";
import { BaseDto } from "src/base.dto";



export class SendEmailDataDto {
    @ApiProperty() @ValidateIf(obj => !obj.cc_email) @ValidateIf(obj => !obj.bcc_email) @IsString() @IsNotEmpty({message:'Please Fill anyone of the field from To Email , CC Email & BCC Email'}) to_email: string;
    @ApiProperty() @ValidateIf(obj => !obj.to_email) @ValidateIf(obj => !obj.bcc_email) @IsString() @IsNotEmpty({message:'Please Fill anyone of the field from To Email , CC Email & BCC Email'}) cc_email: string;
    @ApiProperty() @ValidateIf(obj => !obj.cc_email) @ValidateIf(obj => !obj.to_email) @IsString() @IsNotEmpty({message:'Please Fill anyone of the field from To Email , CC Email & BCC Email'}) bcc_email: string;

    @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
    @ApiProperty() @IsNotEmptyObject() @IsObject() po_lines: any;
    @ApiProperty() @IsNotEmpty() @IsNumber() email_type: number;
}

export class SaveSendEmailDataDto extends BaseDto {
    @Type(() => SendEmailDataDto)
    @ApiProperty() data: SendEmailDataDto;
}