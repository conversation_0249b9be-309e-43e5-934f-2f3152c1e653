import { Controller, Get, Response, Post, Body, Patch, Param, Delete, UsePipes, ValidationPipe, Request, Query, DefaultValuePipe, ParseIntPipe } from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto,SaveApproveCompanyDto,SaveUpdateCompanyDetailsDto,SaveCompanyNameDto, SaveResetPasswordDto, HidePendingOnBoardUserDTO, HidePendingOnBoardUser,UpdateUserSpreadDataDTO, UpdateCohortSpreadDataDTO, SaveCohortNameDto, UpdateCompanySpreadDataDTO, SaveUserUpdateDto, CreateWidgetUserDto, SavePreapprovedEmailsDto } from './dto/user.dto';
import { Constants } from 'src/Constants';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;
const meta = Constants.PAGINATION_RESPONSE_TAG;

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('getPendingOnBoardUserRequest')
  async getPendingOnBoardUserRequest(@Response() res) {
    let responseData = {
      [responseTag]: await this.userService.getPendingOnBoardUserRequest()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('getOnboardCompaniesRequests')
  async getOnboardCompaniesRequests(@Response() res) {
    let responseData = {
      [responseTag]: await this.userService.getOnboardCompaniesRequests()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('approveCompany/:action')
  @UsePipes(ValidationPipe)
  async saveCompanyNameAction(@Param('action') action: string,@Body() SaveApproveCompanyDto:SaveApproveCompanyDto, @Response() res){
    let payloadData = SaveApproveCompanyDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveCompanyNameAction(payloadData,action)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('updateCompanyDetails')
  @UsePipes(ValidationPipe)
  async updateCompanyDetails(@Body() saveUpdateCompanyDetails:SaveUpdateCompanyDetailsDto, @Response() res){
    let payloadData = saveUpdateCompanyDetails[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.updateCompanyDetails(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('saveCompanyName')
  @UsePipes(ValidationPipe)
  async saveCompanyName(@Body() saveCompanyNameDto:SaveCompanyNameDto, @Response() res){
    let payloadData = saveCompanyNameDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveCompanyName(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('list')
  async getWidgetUsersList(@Response() res) {
    let responseData = {
      [responseTag]: await this.userService.getWidgetUsersList()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('resetPassword')
  @UsePipes(ValidationPipe)
  async resetPassword(@Body() saveResetPasswordDto:SaveResetPasswordDto, @Response() res){
    let payloadData = saveResetPasswordDto[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.setUserPassword(adminId, payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('hidePendingOnBoardUser')
  @UsePipes(ValidationPipe)
  async hidePendingOnBoardUser(@Body() hdidePendingOnBoardUserDTO: HidePendingOnBoardUserDTO, @Response() res) {
    let payloadData: HidePendingOnBoardUser = hdidePendingOnBoardUserDTO[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.hidePendingOnBoardUser(payloadData)
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('updateUserDiscount')
  @UsePipes(ValidationPipe)
  async updateUserSpread(@Body() updateUserSpreadDataDTO:UpdateUserSpreadDataDTO, @Request() req, @Response() res){
    let payloadData = updateUserSpreadDataDTO[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let accessToken = req.headers['accesstoken'];
    let responseData = {
      [responseTag]: await this.userService.updateUserSpread(adminId, payloadData, accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('updateCohortDiscount')
  @UsePipes(ValidationPipe)
  async updateCohortSpread(@Body() updateCohortSpreadDataDTO:UpdateCohortSpreadDataDTO, @Request() req, @Response() res){
    let payloadData = updateCohortSpreadDataDTO[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let accessToken = req.headers['accesstoken'];
    let responseData = {
      [responseTag]: await this.userService.updateCohortSpread(adminId, payloadData, accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('saveCohortName')
  @UsePipes(ValidationPipe)
  async saveCohortName(@Body() saveCohortNameDto:SaveCohortNameDto, @Response() res){
    let payloadData = saveCohortNameDto[payloadTag];
    let responseData = {
      [responseTag]: await this.userService.saveCohort(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('updateCompanyDiscount')
  @UsePipes(ValidationPipe)
  async updateCompanySpread(@Body() updateCompanySpreadDataDTO:UpdateCompanySpreadDataDTO,@Request() req, @Response() res){
    let payloadData = updateCompanySpreadDataDTO[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.updateCompanySpread(adminId, payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('updateUserData')
  @UsePipes(ValidationPipe)
  async updateUserData(@Body() saveuserUpdateDto:SaveUserUpdateDto,@Request() req, @Response() res){
    let payloadData = saveuserUpdateDto[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.updateUserData(payloadData,adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getDiscountedUsers')
  async getSpreadUsers(@Response() res) {
    let responseData = {
      [responseTag]: await this.userService.getSpreadUsers()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  // Ceate user from "Pending Users" module on AD, server does not create user in cognito as UI does it
  @Post('on-board')
  @UsePipes(ValidationPipe)
  async onBoardWidgetUser(@Body() createWidgetUserDto: CreateWidgetUserDto, @Response() res) {
    let payloadData = createWidgetUserDto[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.onBoardWidgetUser(payloadData, adminId)
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  // Create user from "Create User" module on AD, server creates user in cognito
  @Post('create')
  @UsePipes(ValidationPipe)
  async createWidgetUser(@Body() createWidgetUserDto: CreateWidgetUserDto, @Response() res) {
    let payloadData = createWidgetUserDto[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.createWidgetUser(payloadData, adminId)
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('save-pre-approved-emails')
  @UsePipes(ValidationPipe)
  async savePreapprovedEmails(@Body() savePreApprovedEmailsDto: SavePreapprovedEmailsDto, @Response() res) {
    let payloadData = savePreApprovedEmailsDto[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.userService.savePreapprovedEmails(payloadData, adminId)
    };
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/get-pre-approved-emails')
  async getPreApprovedEmails(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number, @Query('search') search: string = "",@Query('sort', new DefaultValuePipe('created_date:desc,id:desc')) sort: string) {
    let data = await this.userService.getPreApprovedEmails({ page, limit, search, sort});
    let metaData;
    
    if (data?.items) {
      metaData = data.meta;
    }

    let responseData = {
      [responseTag]: data.items,
      [meta]: metaData 
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

}
