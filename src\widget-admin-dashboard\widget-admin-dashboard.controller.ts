import { Controller, Get, Post, Body, Patch, Param, Delete,Request, Response,ValidationPipe, UsePipes, DefaultValuePipe, ParseIntPipe, Query, ParseBoolPipe, UploadedFile, UseInterceptors } from '@nestjs/common';
import { WidgetAdminDashboardService } from './widget-admin-dashboard.service';
import { CreateWidgetAdminDashboardDto } from './dto/create-widget-admin-dashboard.dto';
import { SaveUpdateSellerExtendedPriceDto, SendNotificationEventDto, SendPurchaseOrderNumberDto, AchToBnplDto, saveToBnplDto, saveSafeImgixImageKit, saveSafeImgixImageKitDto, SaveUpdateResalesCertificateDto, CheckEmailValidDto, SaveUpdateSecurityKeyDto, SignedUrl, SaveCompanyResaleCertUploadDto, SaveCompanyResaleCertDeleteDto, SaveMobileReleaseUrlDto, SaveVideoLibraryDto, SaveUpdateVideoLibraryDto, GetVideoTags, SaveVideoTagsDto, SaveUpdateVideoTagsDto, UpdateHideRecordSellerInvoiceDto, ParseBoolOrStringPipe, SaveBnplCreditLimitDto, SaveBnplRequestDto, SaveAchCreditRequestDto, UpdateUserStatusDTo, IncreaseCreditLimitPayloadDto, SaveHolidayCalendarDto, DeleteHolidayEntryDto, SaveExternalApiKeyMappingDto, GetVideoUploadIdDto, GenerateVideoUploadMultipartUrlDto, GeneratetCompleteMultipartUploadUrlDto, SaveChangeGameStateDto, SaveConfidenceLevelConfigDto, UpdateSubscriptionPricingDto } from './dto/update-widget-admin-dashboard.dto';
import { Constants } from '@bryzos/base-library';
import { Constants as LocalConstants } from '../Constants';
import { HideHomePageCarouselDto, SaveHideHomePageCarouselDto, SafeUploadCommentsDto, UpdateSafeUploadComments } from 'src/homepage/dto/homepage-safe-uploads.dto';
import { ResaleCertificateService } from './resale-certificate/resale-certificate.service';
import { CompanyBuyNowPayLaterService } from './company-buy-now-pay-later.service';
import { OrderService } from '../order/order.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';


const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;
const meta = LocalConstants.PAGINATION_RESPONSE_TAG;

@Controller('widget-admin-dashboard')
export class WidgetAdminDashboardController {
  constructor(
    private readonly widgetAdminDashboardService: WidgetAdminDashboardService,
    private readonly resaleCertificateService: ResaleCertificateService,
    private readonly companyBuyNowPayLaterService: CompanyBuyNowPayLaterService,
    private readonly orderService: OrderService
  ) {}

  @Post()
  create(@Body() createWidgetAdminDashboardDto: CreateWidgetAdminDashboardDto) {
    return this.widgetAdminDashboardService.create(createWidgetAdminDashboardDto);
  }

  @Get()
  findAll() {
    return this.widgetAdminDashboardService.findAll();
  }

  @Get('/restartWebsocketServer')
  async restartWebsocket( @Response() res)
  {
      
    let responseData = {
      [responseTag]:await this.widgetAdminDashboardService.restartWebsocketServer()
    };

    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/restartNodeMessageCronServer')
  async restartNodeMessageCronServer( @Response() res)
  {
    
    let responseData = {
      [responseTag]:await this.widgetAdminDashboardService.restartNodeMessageCronServer()
    };

    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

 @Get('getNotificationEvents')
 async getNotificationEvents( @Response() res){
  let responseData = {
    [responseTag]:await this.widgetAdminDashboardService.getDesktopNotificationEvents()
  };
  res.locals.responseData = responseData;
  return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
 }

  @Get('getAllPo')
  async getAllPo(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getAllPo()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/orders')
  async getOrderList(@Response() res, @Query('page',new DefaultValuePipe(0), ParseIntPipe) page: number, @Query('limit',new DefaultValuePipe(0), ParseIntPipe) limit: number, @Query('search') search: string = "") {
    let data = await this.widgetAdminDashboardService.getPoOrderList({ page, limit, search });
    let metaData;
    if (data?.items) {
      metaData = data.meta;
    }
    let responseData = {
      [responseTag]: data.items,
      [meta]: metaData 
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/increaseCreditLimt')
  async getBuyerIncreaseCreditRequest(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number = 0, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number = 0, @Query('search') search: string = "") {
    let data = await this.widgetAdminDashboardService.getBuyerIncreaseCreditRequest({ page, limit, search });
    let meta;

    if (data?.items) {
      meta = data.meta;
      data = data.items;
    }
    let responseData = {
      [responseTag]: data,
      [LocalConstants.PAGINATION_RESPONSE_TAG]: meta
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/widgetGrid')
  async getResaleCertificate(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number = 0, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number = 0, @Query('search') search: string = "") {
    let data = await this.widgetAdminDashboardService.getResaleCertificate({ page, limit, search });
    let meta;

    if (data?.items) {
      meta = data.meta;
      data = data.items;
    }
    let responseData = {
      [responseTag]: data,
      [LocalConstants.PAGINATION_RESPONSE_TAG]: meta
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/achCreditOrder')
  async getAchCreditOrder(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number = 0, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number = 0, @Query('search') search: string = "") {
    let data = await this.widgetAdminDashboardService.getAchCreditOrder({ page, limit, search });
    let meta;

    if (data?.items) {
      meta = data.meta;
      data = data.items;
    }
    let responseData = {
      [responseTag]: data,
      [LocalConstants.PAGINATION_RESPONSE_TAG]: meta
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Get('/bryzosPay')
  async getBuyNowPayLater(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number, @Query('search') search: string = "") {
    let data = await this.widgetAdminDashboardService.getBuyNowPayLater({ page, limit, search });
    let meta;

    if (data?.items) {
      meta = data.meta;
      data = data.items;
    }
    let responseData = {
      [responseTag]: data,
      [LocalConstants.PAGINATION_RESPONSE_TAG]: meta
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/updateSellerExtPrice')
  @UsePipes(ValidationPipe)
  async updateSellerExtended(@Body()  saveUpdateSellerExtendedPriceDto: SaveUpdateSellerExtendedPriceDto, @Response() res){
    let payloadData = saveUpdateSellerExtendedPriceDto[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]:await this.widgetAdminDashboardService.updateSellerExtendedPrice(payloadData,userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/sendNotification')
  @UsePipes(ValidationPipe)
  async sendNotification(@Body() sendNotificationEventDto: SendNotificationEventDto, @Request() req, @Response() res) {
    let payloadData = sendNotificationEventDto[payloadTag];
    let userId = res.locals.authorizedUserId;
    let accessToken = req.headers['accesstoken'];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.callNotificationService(payloadData, userId, accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/sendInvoiceEmail')
  @UsePipes(ValidationPipe)
  async sendEmail(@Body() sendPurchaseOrderNumberDto: SendPurchaseOrderNumberDto, @Response() res) {
    let payloadData = sendPurchaseOrderNumberDto[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.sendEmailInvoice(payloadData, userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('getAllOpenPo')
  async getAllOpenPo(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getAllOpenPo()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/convertAchToBnpl')
  @UsePipes(ValidationPipe)
  async convertAchToBnpl( @Body() achToBnplDto: saveToBnplDto, @Response() res ) //accept po_number and admin id. 
  {
      let payloadData: AchToBnplDto = achToBnplDto[payloadTag];
      let poNumber = payloadData.po_number;
    
      let responseData = {
        [responseTag] : await this.widgetAdminDashboardService.transferAchToBnpl(poNumber)
      };
      
      res.locals.responseData = responseData; 
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('getAdminReferenceData')
  async getReferenceData(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getAdminReferenceData()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getSafeUploads')
  @UsePipes(ValidationPipe)
  async getSafeUploads(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getSafeUploads(),
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/updateCarouselImage')
  @UsePipes(ValidationPipe)
  async saveHideCarouselImage( @Body() saveHideHomePageCarouselDto: SaveHideHomePageCarouselDto, @Response() res ) //accept po_number and admin id. 
  {
    let payloadData: HideHomePageCarouselDto[] = saveHideHomePageCarouselDto[payloadTag];

    let responseData = {
      [responseTag] : await this.widgetAdminDashboardService.saveHideCarouselImage(payloadData)
    };
    
    res.locals.responseData = responseData; 
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/safeImgixImageKit')
  async getSafeImgixImageKit(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getSafeImgixImageKit()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/safeImgixImageKit')
  @UsePipes(ValidationPipe)
  async saveSafeImgixImageKit(@Body() payload: saveSafeImgixImageKitDto, @Response() res) {
    let payloadData: saveSafeImgixImageKit[] = payload[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.saveSafeImgixImageKit(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/indicator')
  async getWidgetMenuIndicator( @Response() res){
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getWidgetMenuIndicator()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/updateSecurityKey')
  @UsePipes(ValidationPipe)
  async updateSecurityKey(@Body() saveUpdateSecurityKey : SaveUpdateSecurityKeyDto, @Response() res) {
    let payloadData = saveUpdateSecurityKey[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateSecurityKey(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('approveRejectResalesCert')
  @UsePipes(ValidationPipe)
  async approveRejectResalesCert(@Body() saveUpdateResalesCertificate: SaveUpdateResalesCertificateDto, @Response() res) {
    const payloadData = saveUpdateResalesCertificate[payloadTag];
    let userId = res.locals.authorizedUserId;

    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.approveRejectResalesCert(userId, payloadData)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getAchCreditPayementApprovalList')
  async getAchCreditPayementApprovalList(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getAchCreditPayementApprovalList()
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/checkEmailValid')
  @UsePipes(ValidationPipe)
  async checkEmailValid(@Body() checkEmailValid : CheckEmailValidDto, @Response() res){
    const payloadData = checkEmailValid[payloadTag];

    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.checkEmailValid(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/getSignedUrl')
  @UsePipes(ValidationPipe)
  async getSignedS3Url(@Body() dto: SignedUrl,@Response() res) {
    let payloadData = dto[payloadTag];
    let responseData = {
      [responseTag]: await this.resaleCertificateService.getSignedS3Url(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Post('/companyResaleCertUpload')
  @UsePipes(ValidationPipe)
  async saveResaleCertificate(@Body() companyResaleCertUploadDto: SaveCompanyResaleCertUploadDto,@Response() res) {
    let payloadData = companyResaleCertUploadDto[payloadTag];
    let adminId = res.locals.authorizedUserId; 
    let responseData = {
      [responseTag]: await this.resaleCertificateService.uploadCompanyResaleCertificate(payloadData,adminId)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Get('/getCompanyResaleCertificate')
  async getCompanyResaleCertificate(@Response() res) {
    let responseData = {
      [responseTag]: await this.resaleCertificateService.getCompanyResaleCertificate()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/deleteCompanyResaleCert')
  @UsePipes(ValidationPipe)
  async DeleteResaleCertificate(@Body() saveCompanyResaleCertDeleteDto: SaveCompanyResaleCertDeleteDto,@Response() res) {
    let payloadData = saveCompanyResaleCertDeleteDto[payloadTag];
    let adminId = res.locals.authorizedUserId; 
    let responseData = {
      [responseTag]: await this.resaleCertificateService.deleteCompanyResaleCertificate(payloadData,adminId)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Get('/getMobileReleaseUrls')
  async getMobileReleaseUrls(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getMobileReleaseUrls()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/setMobileReleaseUrl')
  @UsePipes(ValidationPipe)
  async setMobileReleaseUrl(@Body() mobileReleaseUrlDto : SaveMobileReleaseUrlDto, @Response() res){
    const payloadData = mobileReleaseUrlDto[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.setMobileReleaseUrl(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('/updateTncVersion')
  @UsePipes(ValidationPipe)
  async updateTncVersion(@Request() req, @Response() res) {
    const accessToken = req.headers['accesstoken'];
    const responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateTncVersion(accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Get('/getSafeUploadComments')
  async getSafeUploadComments(@Query('id') id: string, @Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getSafeUploadComments(id),
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/updateSafeUploadComments')
  @UsePipes(ValidationPipe)
  async updateSafeUploadComments( @Body() updateSafeUploadComments: UpdateSafeUploadComments, @Response() res )
  {
    let payloadData: SafeUploadCommentsDto[] = updateSafeUploadComments[payloadTag];

    let responseData = {
      [responseTag] : await this.widgetAdminDashboardService.updateSafeUploadComments(payloadData)
    };
    
    res.locals.responseData = responseData; 
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveVideo')
  @UsePipes(ValidationPipe)
  async saveVideoInLibrary(@Body() saveVideoLibrary : SaveVideoLibraryDto, @Response() res) {
    const payloadData = saveVideoLibrary[payloadTag];
    let adminId = res.locals.authorizedUserId;
    const responseData = {
      [responseTag]: await this.widgetAdminDashboardService.saveVideoInLibrary(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Post('/updateVideo')
  @UsePipes(ValidationPipe)
  async updateVideoInLibrary(@Body() updateVideoLibrary : SaveUpdateVideoLibraryDto, @Response() res) {
    const payloadData = updateVideoLibrary[payloadTag];
    let adminId = res.locals.authorizedUserId;
    const responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateVideoInLibrary(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }
  
  @Delete('removeVideo/:id')
  async removeVideo(@Param('id') id: string, @Response() res) {
    const responseData = {
      [responseTag]: await this.widgetAdminDashboardService.removeVideo(id)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Get('/getVideos')
  async getVideosFromLibrary(@Response() res, @Query('page',new DefaultValuePipe(0), ParseIntPipe) page: number, @Query('limit',new DefaultValuePipe(0), ParseIntPipe) limit: number, @Query('search') search: string = "") {
    let data = await this.widgetAdminDashboardService.getVideosFromLibrary({ page, limit, search });
    let metaData;
    if (data?.items) {
      metaData = data.meta;
    }
    let responseData = {
      [responseTag]: data.items,
      [meta]: metaData 
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/getVideoTags')
  @UsePipes(ValidationPipe)
  async getVideoTags(@Query() query:GetVideoTags,@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getVideoTags(query.video_file_name)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  
  @Post('/saveVideoLibraryTag')
  @UsePipes(ValidationPipe)
  async saveVideoLibraryTag(@Body() saveVideoTag : SaveVideoTagsDto, @Response() res) {
    const payloadData = saveVideoTag[payloadTag];
    const responseData = {
      [responseTag]: await this.widgetAdminDashboardService.saveVideoTag(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Post('/updateVideoLibraryTag')
  @UsePipes(ValidationPipe)
  async updateVideoLibraryTag(@Body() updateVideoLibrary : SaveUpdateVideoTagsDto, @Response() res) {
    const payloadData = updateVideoLibrary[payloadTag];
    const responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateVideoLibraryTag(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(200).json(responseData);
  }

  @Get('/getSellerInvoice')
  async getSellerInvoice(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number, @Query('search') search: string = "",@Query('sort', new DefaultValuePipe('created_date:desc,id:desc')) sort: string,@Query('isShowAll', new DefaultValuePipe(false), ParseBoolPipe) isShowAll: boolean,@Query('emailSent',ParseBoolOrStringPipe) emailSent: boolean|string) {
    let data = await this.widgetAdminDashboardService.getExtractDataofSellerInvoice({ page, limit, search, sort, isShowAll, emailSent});
    let metaData;
    if (data?.items) {
      metaData = data.meta;
    }
    let responseData = {
      [responseTag]: data.items,
      [meta]: metaData 
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/hideUnhideSellerInvoice')
  @UsePipes(ValidationPipe)
  async hideUnhideSellerInvoice(@Body() hideUnhideSellerInvoiceDto : UpdateHideRecordSellerInvoiceDto, @Response() res){
    const payloadData = hideUnhideSellerInvoiceDto[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.hideUnhideSellerInvoice(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveBnplCreditLimit')
  @UsePipes(ValidationPipe)
  async saveBnplCreditLimit(@Body() saveBnplCreditLimit: SaveBnplCreditLimitDto, @Response() res) {
    const payloadData = saveBnplCreditLimit[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.companyBuyNowPayLaterService.saveBnplCreditLimit(payloadData, userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveBnplRequest')
  @UsePipes(ValidationPipe)
  async saveBnplRequest(@Body() saveBnplRequest: SaveBnplRequestDto, @Response() res) {
    const payloadData = saveBnplRequest[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.companyBuyNowPayLaterService.saveBnplApprovalRequest(payloadData, adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/saveAchCreditRequest')
  @UsePipes(ValidationPipe)
  async saveAchCreditRequest(@Body() saveAchCreditRequest: SaveAchCreditRequestDto, @Response() res) {
    const payloadData = saveAchCreditRequest[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.saveAchCreditRequest(payloadData, adminId),
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/getBuyerCreditLimit/:userId')
  async getBuyerCreditLimit(@Response() res, @Param("userId") userId: string) {
    const responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getBuyerCreditLimit(userId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/updateUserStatus')
  @UsePipes(ValidationPipe)
  async updateUserStatus(@Request() req, @Response() res, @Body() updateUserStatus: UpdateUserStatusDTo) {
    const payloadData = updateUserStatus[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let accessToken = req.headers.accesstoken;
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateUserStatus(payloadData, adminId, accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('actionOnIncreaseCreditLimitRequest')
  @UsePipes(ValidationPipe)
  async actionOnIncreaseCreditLimitRequest(@Body() increaseCreditLimitPayload:IncreaseCreditLimitPayloadDto, @Response() res){
    let payloadData = increaseCreditLimitPayload[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.actionOnIncreaseCreditLimitRequest(payloadData, adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('holiday-calendar')
  async getHolidayCalendar(@Response() res) {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getHolidayCalendar()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('holiday-calendar')
  @UsePipes(ValidationPipe)
  async saveHolidayCalendar(@Body() holidayCalendarPayload: SaveHolidayCalendarDto, @Response() res){
    let payloadData = holidayCalendarPayload[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.saveHolidayCalendar(payloadData, adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('remove-holiday')
  @UsePipes(ValidationPipe)
  async deleteHolidayEntry(@Body() deleteHolidayPayload: DeleteHolidayEntryDto, @Response() res){
    let payloadData = deleteHolidayPayload[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.deleteHolidayEntry(payloadData, adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('get-internal-tags')
  async getInternalTags(@Response() res){
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getInternalTags()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/external-api-keys')
  async getExternalApiKeys(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number = 0, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number = 0, @Query('search') search: string = "") {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getExternalApiKeys({ page, limit, search })
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/external-api-key/mapping')
  @UsePipes(ValidationPipe)
  async externalApiKeyMapping(@Body() saveExternalApiKeyMappingDto: SaveExternalApiKeyMappingDto, @Response() res) {
    let payloadData = saveExternalApiKeyMappingDto[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.externalApiKeyMapping(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  //Sandbox API
  @Get('/sandbox/external-api-keys')
  async getSandboxExternalApiKeys(@Response() res, @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number = 0, @Query('limit', new DefaultValuePipe(0), ParseIntPipe) limit: number = 0, @Query('search') search: string = "") {
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.getSandboxExternalApiKeys({ page, limit, search })
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/sandbox/external-api-key/mapping')
  @UsePipes(ValidationPipe)
  async externalApiSandboxKeyMapping(@Body() saveExternalApiKeyMappingDto: SaveExternalApiKeyMappingDto, @Response() res) {
    let payloadData = saveExternalApiKeyMappingDto[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.externalApiSandboxKeyMapping(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/videos/initiate-multipart-upload')
  @UsePipes(ValidationPipe)
  async initiateMultipartUpload(@Body() getVideoUploadIdDto: GetVideoUploadIdDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = getVideoUploadIdDto[payloadTag];

    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.initiateMultipartUpload(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/videos/generate-multipart-presigned-urls')
  @UsePipes(ValidationPipe)
  async generateMultipartPresignedUrls(@Body() generateVideoUploadMultipartUrlDto: GenerateVideoUploadMultipartUrlDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = generateVideoUploadMultipartUrlDto[payloadTag];

    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.generateMultipartPresignedUrls(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/videos/complete-multipart-upload')
  @UsePipes(ValidationPipe)
  async completeMultipartUpload(@Body() generateCompleteMultipartUploadUrlDto: GeneratetCompleteMultipartUploadUrlDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = generateCompleteMultipartUploadUrlDto[payloadTag];

    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.completeMultipartUpload(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/update-game-initial-settings')
  @UsePipes(ValidationPipe)
  async updateGameInitialSettings(@Body() saveChangeGameStateDto: SaveChangeGameStateDto, @Response() res) {
    let payloadData = saveChangeGameStateDto[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateGameInitialSettings(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/update-bom-confidence-config')
  @UsePipes(ValidationPipe)
  async updateBomConfidenceConfig(@Body() saveConfidenceLevelConfigDto: SaveConfidenceLevelConfigDto, @Response() res) {
    let payloadData = saveConfidenceLevelConfigDto[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateBomConfidenceConfig(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/subscriptions/pricing')
  @UsePipes(ValidationPipe)
  async updateSubscriptionPricing(@Body() updateSubscriptionPricingDto: UpdateSubscriptionPricingDto, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = updateSubscriptionPricingDto[payloadTag];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.updateSubscriptionPricing(payloadData, userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Post('upload-file')
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB limit
      },
    }),
  )
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body('file_type') fileType: 'search' | 'pricing',
    @Response() res,
    @Request() req
  ) {
    let userId = res.locals.authorizedUserId;
    let accessToken = req.headers['accesstoken'];
    let responseData = {
      [responseTag]: await this.widgetAdminDashboardService.uploadFile(file, fileType, userId, accessToken)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}

  


