import { IsTrimmed } from "@bryzos/extended-widget-library";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsBoolean, IsNumber, ValidateNested, IsArray, ArrayNotEmpty, IsDateString, IsOptional, Max, Min, ValidateIf, IsEnum, MinLength, IsInt} from "class-validator";
import { BaseDto } from "src/base.dto";
import { Constants } from "src/Constants";

export class CreateUserDto{}
export class ApproveCompanyDto {
    @ApiProperty() @IsNotEmpty()  @IsString() id: string;
    @ApiProperty() @IsNotEmpty()  @IsString() company_name: string;
}

export class SaveApproveCompanyDto extends BaseDto {
    @Type(() => ApproveCompanyDto)
    @ApiProperty() @ArrayNotEmpty() @IsNotEmpty() @IsArray() @ValidateNested({ each: true }) @Type(() => ApproveCompanyDto) data : ApproveCompanyDto;
}

export class UpdateCompanyDetailsDto {
    @ApiProperty() @IsNotEmpty()  @IsNumber() id: number;
    @ApiProperty() @IsNotEmpty()  @IsString() company_name: string;
    @ApiProperty() @IsNotEmpty()  @IsNumber() @IsInt() @Min(0) allowed_api_keys_count: number;
    @ApiProperty() @IsOptional() @IsNotEmpty()  @IsNumber() @IsInt() @Min(0) sandbox_allowed_api_keys_count: number;
}

export class SaveUpdateCompanyDetailsDto extends BaseDto {
    @Type(() => UpdateCompanyDetailsDto)
    @ApiProperty() data: UpdateCompanyDetailsDto;
}

export class CompanyNameDto {
    @ApiProperty() @IsNotEmpty()  @IsString() company_name: string;
}

export class SaveCompanyNameDto extends BaseDto {
    @Type(() => CompanyNameDto)
    @ApiProperty() data: CompanyNameDto;
}


export class ResetPasswordDto {
    @ApiProperty() @IsNotEmpty()  @IsString() email_id: string;
    @ApiProperty() @IsNotEmpty()  @IsString() password: string;
}

export class SaveResetPasswordDto extends BaseDto {
    @Type(() => ResetPasswordDto)
    @ApiProperty() data: ResetPasswordDto;
}

export class HidePendingOnBoardUser {
    @ApiProperty() @IsNotEmpty()  @IsString() id: string;
    @ApiProperty() @IsNotEmpty()  @IsString() hide_reason: string;
}

export class HidePendingOnBoardUserDTO extends BaseDto {
    @Type(() => HidePendingOnBoardUser)
    @ApiProperty() data: HidePendingOnBoardUser;
}

export class UpdateUserSpreadData {
    @ApiProperty() @IsNotEmpty() @IsNumber() user_id:number;
    @ApiProperty() @IsNotEmpty() @IsBoolean() is_discount: boolean;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Max(99) discount_percentage: number;
    // @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0) discount_period: number;
    // @ApiProperty() @IsNotEmpty() @IsDateString() discount_phaseout_startdate: Date;
    // @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0) discount_phaseout_period: number;
    @ApiProperty() @IsNotEmpty() @IsString() discount_pricing: string;
    @ApiProperty() @IsNotEmpty() @IsBoolean() is_overriden: boolean;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Max(99) seller_spread_percentage: number
}

export class UpdateUserSpreadDataDTO extends BaseDto {
    @Type(() => UpdateUserSpreadData)
    @ApiProperty() data: UpdateUserSpreadData;
}

export class UpdateCompanySpreadData {
    @ApiProperty() @IsNotEmpty() @IsNumber() company_id:number;
    @ApiProperty() @IsNotEmpty() @IsBoolean() is_discount: boolean;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Max(99) discount_percentage: number;
    // @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0) @ValidateIf((object, value) => value !== null) discount_period: number;
    // @ApiProperty() @IsNotEmpty() @IsDateString() @ValidateIf((object, value) => value !== null) discount_phaseout_startdate: Date;
    // @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0) discount_phaseout_period: number;
    @ApiProperty() @IsNotEmpty() @IsString() discount_pricing: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Max(99) seller_spread_percentage: number
}

export class UpdateCompanySpreadDataDTO extends BaseDto {
    @Type(() => UpdateCompanySpreadData)
    @ApiProperty() data: UpdateCompanySpreadData;
}
export class UpdateCohortSpreadData {
    @ApiProperty() @IsNotEmpty() @IsNumber() cohort_id:number;
    @ApiProperty() @IsNotEmpty() @IsBoolean() is_discount: boolean;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Max(99) discount_percentage: number;
    // @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0) @ValidateIf((object, value) => value !== null) discount_period: number;
    // @ApiProperty() @IsNotEmpty() @IsDateString() @ValidateIf((object, value) => value !== null) discount_phaseout_startdate: Date;
    // @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0) discount_phaseout_period: number;
    @ApiProperty() @IsNotEmpty() @IsString() discount_pricing: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() @Max(99) seller_spread_percentage: number
}

export class UpdateCohortSpreadDataDTO extends BaseDto {
    @Type(() => UpdateCohortSpreadData)
    @ApiProperty() data: UpdateCohortSpreadData;
}

export class CohortNameDto {
    @ApiProperty() @IsNotEmpty()  @IsString() cohort_name: string;
}

export class SaveCohortNameDto extends BaseDto {
    @Type(() => CohortNameDto)
    @ApiProperty() data: CohortNameDto;
}

export class UserDetailsDto {
    @ApiProperty() @IsNotEmpty()  @IsString() first_name: string;
    @ApiProperty() @IsNotEmpty()  @IsString() last_name: string;
    @ApiProperty() @IsNotEmpty()  @IsString() company_name: string;
    @ApiProperty() @IsNotEmpty()  @IsString() client_company: string;
    @ApiProperty() @IsNotEmpty()  @IsString() @IsEmail() email: string;
    @ApiProperty() @IsNotEmpty()  @IsString() @IsEnum([Constants.BUYER.toUpperCase(), Constants.SELLER.toUpperCase()], { message: 'Invalid user type' }) user_type: string;
    @ApiProperty() @IsNotEmpty()  @IsString() onboard_source: string;
    @ApiProperty() @IsNotEmpty()  @IsBoolean() is_external_api_company_admin: boolean;
}
export class UserUpdateDto extends UserDetailsDto{
    @ApiProperty() @IsNotEmpty()  @IsNumber() user_id: number;
}

export class SaveUserUpdateDto extends BaseDto {
    @Type(() => UserUpdateDto) 
    @ApiProperty({ type: () => UserUpdateDto }) @ValidateNested() data: UserUpdateDto;
}

export class WidgetUserDto {
    @ApiProperty() @IsNotEmpty() @IsString() first_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() last_name: string;
    @ApiProperty() @IsTrimmed() @IsNotEmpty() @IsString() company_name: string;
    @ApiProperty() @IsTrimmed() @IsNotEmpty() @IsString() client_company: string;
    @ApiProperty() @IsNotEmpty() @IsString() email_id: string;
    // Password is validated only if password field is provided in payload while create-user & onBoard old pending user
    @ApiProperty() @IsOptional() @IsString() @MinLength(6) password: string;
    @ApiProperty() @IsNotEmpty() @IsString() user_type: string;
    @ApiProperty() @IsTrimmed() @IsNotEmpty() @IsString() onboard_source: string;
    @ApiProperty() @ValidateIf((o) => o.zip_code !== undefined) @IsNotEmpty() @IsString() zip_code?: string;
    @ApiProperty() @ValidateIf((o) => o.id !== undefined) @IsNotEmpty() @IsString() id?: string;
    @ApiProperty() @ValidateIf((o) => o.company_id !== undefined) @IsNotEmpty() @IsString() company_id?: string;
    @ApiProperty() @ValidateIf((o) => o.cognito_user_name !== undefined) @IsNotEmpty() @IsString() cognito_user_name?: string;
    @ApiProperty() @ValidateIf((o) => o.state_id !== undefined) @IsNotEmpty() @IsNumber() state_id?: number;
    @ApiProperty() @IsOptional() @IsNumber() is_migrated_to_password: number;

}

export class CreateWidgetUserDto extends BaseDto {
    @Type(() => WidgetUserDto)
    @ApiProperty() data: WidgetUserDto;
}

export class PreApprovedEmailsDto {
    @ApiProperty({ description: 'Email address' }) @IsNotEmpty() @IsEmail() email: string; // Validate the email format

    @ApiProperty({ description: 'Cohort name' }) @IsOptional() @IsString() cohort: string; // Validate the cohort as a string
}

export class SavePreapprovedEmailsDto extends BaseDto {
    @ApiProperty({ type: [PreApprovedEmailsDto], description: 'Array of email and cohort objects' })
    @IsArray()
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => PreApprovedEmailsDto)
    data: PreApprovedEmailsDto[]; // Notice the `[]` indicating an array
}

