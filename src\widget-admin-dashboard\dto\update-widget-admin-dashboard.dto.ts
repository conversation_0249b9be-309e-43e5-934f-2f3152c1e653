import { PartialType } from '@nestjs/mapped-types';
import { CreateWidgetAdminDashboardDto } from './create-widget-admin-dashboard.dto';
import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsEmail, IsBoolean, IsArray, IsEnum, IsInt, ValidateNested, ArrayNotEmpty, ArrayUnique, IsDateString, IsObject, IsPositive, Validate, ValidatorConstraint, ValidatorConstraintInterface, IsNumberString } from "class-validator";
import { BaseDto } from "src/base.dto";
import { Constants } from '../../Constants';
import { PipeTransform } from '@nestjs/common';


export class UpdateWidgetAdminDashboardDto extends PartialType(CreateWidgetAdminDashboardDto) {}

export class AchToBnplDto 
{
    @ApiProperty()  @IsNotEmpty() @IsString() po_number : string; 
}

export class UpdateSellerExtendedPriceDto 
{
    @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() po_line_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() actual_seller_extended_price: string;
}


export class SaveUpdateSellerExtendedPriceDto extends BaseDto {
    @Type(() => UpdateSellerExtendedPriceDto)
    @ApiProperty() data: UpdateSellerExtendedPriceDto;
}


export class NotificationEventDto
{
    @ApiProperty() @IsNotEmpty() @IsString() notification_event : string;
    @ApiProperty() @IsString() @IsOptional() po_number: string;
    @ApiProperty() @IsEmail() @IsOptional() email_id: string;
    @ApiProperty() @IsString() @IsOptional() message: string;
    @ApiProperty() @IsString() @IsOptional() @IsEnum([
        Constants.CUSTOM_NOTIFICATION_PRIORTY[0],
        Constants.CUSTOM_NOTIFICATION_PRIORTY[1],
        Constants.CUSTOM_NOTIFICATION_PRIORTY[2],
    ], { message: 'Invalid Priority type' },)

    priority: string;
    @ApiProperty() @IsString() @IsOptional() @IsEnum([
        Constants.CUSTOM_NOTIFICATION_ACTION[0],
        Constants.CUSTOM_NOTIFICATION_ACTION[1],
    ], { message: 'Invalid Action type' },)
    action: string;
    @ApiProperty() @IsString() @IsOptional() @IsEnum([
        Constants.CUSTOM_NOTIFICATION_SEND_TO[0].toUpperCase(),
        Constants.CUSTOM_NOTIFICATION_SEND_TO[1].toUpperCase(),
        Constants.CUSTOM_NOTIFICATION_SEND_TO[2].toUpperCase(),
        Constants.CUSTOM_NOTIFICATION_SEND_TO[3].toUpperCase()],
        { message: 'Invalid Send To type' },
    ) send_to: string;
    @ApiProperty() @IsArray() @IsOptional() send_to_users: number[];
    @ApiProperty() @IsDateString() @IsOptional() expiry_date : string;
}

export class SendNotificationEventDto extends BaseDto
{
    @Type(() => NotificationEventDto)
    @ApiProperty() data : NotificationEventDto;
}


export class PurchaseOrderNumberDto 
{
    @ApiProperty() @IsNotEmpty() @IsString() po_number : string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsOptional() source : string;
}

export class SendPurchaseOrderNumberDto extends BaseDto
{
    @Type(() => PurchaseOrderNumberDto)
    @ApiProperty() data : PurchaseOrderNumberDto;
}
export class saveToBnplDto extends BaseDto {
    @Type(() => AchToBnplDto)
    @ApiProperty() data: AchToBnplDto;
}

export class saveSafeImgixImageKit {
    @ApiProperty() @IsString() @IsNotEmpty()
    id: string;

    @ApiProperty() @IsBoolean() @IsNotEmpty()
    value: boolean;
}
export class saveSafeImgixImageKitDto extends BaseDto {
    @Type(() => saveSafeImgixImageKit)
    @IsArray()
    @ApiProperty() data: saveSafeImgixImageKit[];
}

export class UpdateResalesCertificateDto {
    @ApiProperty() @IsString() @IsNotEmpty() id: string;
    @ApiProperty() @IsBoolean() @IsNotEmpty() approve: boolean;
}
export class SaveUpdateResalesCertificateDto extends BaseDto {
    @Type(() => UpdateResalesCertificateDto)
    @ApiProperty()
    data: UpdateResalesCertificateDto;
}

export class EmailValidDto {
    @ApiProperty() @IsString() @IsNotEmpty() email: string;
}
export class CheckEmailValidDto extends BaseDto {
    @Type(() => EmailValidDto)
    @ApiProperty() data: EmailValidDto;
}

export class UpdateSecurityKeyDto
{
    @ApiProperty() @IsString() @IsNotEmpty() security_key: string;
}

export class SaveUpdateSecurityKeyDto extends BaseDto
{
    @Type(() => UpdateSecurityKeyDto)
    @ApiProperty() data : UpdateSecurityKeyDto;
}
export class GetSignedUrl {
    @ApiProperty() @IsNotEmpty() @IsString() bucket_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() object_key: string;
    @ApiProperty() @IsNotEmpty() @IsInt() expire_time: number;
}
export class SignedUrl extends BaseDto {
    @Type(() => GetSignedUrl)
    @ApiProperty() data: GetSignedUrl;
}
export class ResaleCertUploadDto {
    @ApiProperty() @IsNotEmpty() @IsNumber() state_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() expiration_date: string;
    @ApiProperty() @IsNotEmpty() @IsString() file_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() cerificate_url_s3: string;
}
export class CompanyResaleCertUploadDto {
    @ApiProperty() @IsNotEmpty() @IsString() company_id: string;
    @Type(() => ResaleCertUploadDto)
    @ApiProperty() @IsNotEmpty() @IsArray() @ValidateNested({ each: true }) resale_certificate: ResaleCertUploadDto[];    
}
export class SaveCompanyResaleCertUploadDto extends BaseDto {
    @Type(() => CompanyResaleCertUploadDto)
    @ApiProperty() data: CompanyResaleCertUploadDto;
}

export class CompanyResaleCertDeleteDto {
    @ApiProperty() @IsNotEmpty() @IsString() cert_id: number;
}
export class SaveCompanyResaleCertDeleteDto {
    @Type(() => CompanyResaleCertDeleteDto)
    @ApiProperty() @IsNotEmpty() @IsArray() @ValidateNested({ each: true }) data: CompanyResaleCertDeleteDto[];    
}
export class MobileReleaseUrlDto {
    @ApiProperty() @IsNumber() @IsNotEmpty() id: number;
}
export class SaveMobileReleaseUrlDto extends BaseDto {
    @Type(() => MobileReleaseUrlDto)
    @ApiProperty() data: MobileReleaseUrlDto;
}

export class ThumbnailS3Urls {
    @ApiProperty() @IsString() @IsNotEmpty() intro_desktop: string;
    @ApiProperty() @IsString() @IsNotEmpty() intro_mobile: string;
    @ApiProperty() @IsString() @IsNotEmpty() intro_tablet: string;
    @ApiProperty() @IsString() @IsNotEmpty() thumbnail_app: string;
    @ApiProperty() @IsString() @IsNotEmpty() thumbnail_safe: string;
    @ApiProperty() @IsString() @IsNotEmpty() electron_player: string;
}

export class VideoLibraryDto {
    @ApiProperty() @IsString() @IsNotEmpty() title: string;
    @ApiProperty() @IsString() @IsOptional() caption: string;
    @ApiProperty() @IsString() @IsOptional() description: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() video_s3_url: string;
    @ApiProperty() @Type(() => ThumbnailS3Urls) thumbnail_s3_url: ThumbnailS3Urls;
    @IsArray() @ArrayNotEmpty() @IsString({ each: true }) @IsNotEmpty({ each: true }) @ArrayUnique() tags: string[];
    @ApiProperty() @IsString() @IsOptional() subtitle_s3_url: string;
    @ApiProperty() @IsString() @IsOptional() share_video_url: string;
    @ApiProperty() @IsOptional() @Transform(({ value }) => value === 1) @IsBoolean() @IsNotEmpty() is_large_file: boolean;
    @ApiProperty() @IsString() @IsOptional() internal_tags: string;
}
export class SaveVideoLibraryDto extends BaseDto {
    @Type(() => VideoLibraryDto)
    @ApiProperty() data: VideoLibraryDto;
}

export class updateVideoLibraryDto {
    @ApiProperty() @IsString() @IsOptional() @IsNotEmpty() id: string;
    @ApiProperty() @IsString() @IsOptional() @IsNotEmpty() video_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() title: string;
    @ApiProperty() @IsString() @IsOptional() caption: string;
    @ApiProperty() @IsString() @IsOptional() description: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() video_s3_url: string;
    @ApiProperty() @Type(() => ThumbnailS3Urls) thumbnail_s3_url: ThumbnailS3Urls;
    @ApiProperty() @IsString() @IsNotEmpty() tags: string;
    @ApiProperty() @IsNumber() @IsOptional() @IsNotEmpty() sequence: number;
    @ApiProperty() @IsOptional() @Transform(({ value }) => value === 1) @IsBoolean() show_on_ui: boolean;
    @ApiProperty() @IsString() @IsOptional() subtitle_s3_url: string;
    @ApiProperty() @IsString() @IsOptional() share_video_url: string;
    @ApiProperty() @IsOptional() @Transform(({ value }) => value === 1) @IsBoolean() @IsNotEmpty() is_large_file: boolean;
    @ApiProperty() @IsString() @IsOptional() internal_tags: string;
}

export class SaveUpdateVideoLibraryDto extends BaseDto {
    @Type(() => updateVideoLibraryDto)
    @ApiProperty() @IsArray() @ArrayNotEmpty() data: updateVideoLibraryDto[];
}
export class GetVideoTags {
    @IsString() @IsNotEmpty({ message: 'video_file_name parameter is required' }) video_file_name: string;
}

export class VideoTagsDto {
    @ApiProperty() @IsString() @IsNotEmpty() name: string;
    @ApiProperty() @IsString() @IsNotEmpty() display_title: string;
    @ApiProperty() @IsString() @IsNotEmpty() display_subtitle: string;
    @ApiProperty() @IsString() @IsNotEmpty() query_param: string;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsNotEmpty() show_on_app: boolean;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsNotEmpty() show_on_safe: boolean;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsOptional() @IsNotEmpty() add_at_top: boolean;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsOptional() @IsNotEmpty() shuffle_sequence: boolean;
}

export class SaveVideoTagsDto extends BaseDto {
    @Type(() => VideoTagsDto)
    @ApiProperty() data: VideoTagsDto;
}

export class UpdateVideoTagsDto {
    @ApiProperty() @IsString() @IsNotEmpty() id: string;
    @ApiProperty() @IsString() @IsNotEmpty() name: string;
    @ApiProperty() @IsString() @IsNotEmpty() display_title: string;
    @ApiProperty() @IsString() @IsNotEmpty() display_subtitle: string;
    @ApiProperty() @IsString() @IsNotEmpty() query_param: string;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsNotEmpty() show_on_app: boolean;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsNotEmpty() show_on_safe: boolean;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsOptional() @IsNotEmpty() add_at_top: boolean;
    @ApiProperty() @IsBoolean() @Transform(({ value }) => value === 1) @IsOptional() @IsNotEmpty() shuffle_sequence: boolean;
}

export class SaveUpdateVideoTagsDto extends BaseDto {
    @Type(() => UpdateVideoTagsDto)
    @ApiProperty() @IsArray() @ArrayNotEmpty() data: UpdateVideoTagsDto[];
}
export class HideRecordSellerInvoiceDto{
    @ApiProperty() @IsString() @IsNotEmpty() id: string;
    @ApiProperty() @IsBoolean() @IsNotEmpty() @Transform(({ value }) => value === 1 ? true : value === 0 ? false : value) is_hidden: boolean;
}
export class UpdateHideRecordSellerInvoiceDto extends BaseDto{
    @Type(() => HideRecordSellerInvoiceDto)
    @ApiProperty() data: HideRecordSellerInvoiceDto;
}

export class ParseBoolOrStringPipe implements PipeTransform {
    transform(value: any): boolean | string {
      if (value === 'true' || value == 1) return true;
      if (value === 'false' || value == 0) return false;
      return 'all';
    }
}
export class BnplCreditLimitDto {
    @ApiProperty() @IsNumber() @IsNotEmpty() auth_amount_percentage: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() bryzos_credit_amount: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() charges_date: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() net_terms_days: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() user_id: number;
    @ApiProperty() @IsString() @IsNotEmpty() reason: string;
}
export class SaveBnplCreditLimitDto extends BaseDto {
    @Type(() => BnplCreditLimitDto)
    @ApiProperty() data: BnplCreditLimitDto;
}

export class BnplRequestDto {
    @ApiProperty() @IsNumber() @IsOptional() auth_amount_percentage: number;
    @ApiProperty() @IsNumber() @IsOptional() charges_date: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() @IsEnum([0, 1], { message: "Please provide either 0 or 1" }) is_approved: 0 | 1;
    @ApiProperty() @IsNumber() @IsOptional() net_terms_days: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() requested_credit_limit: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() user_id: number;
}
export class SaveBnplRequestDto extends BaseDto {
    @Type(() => BnplRequestDto)
    @ApiProperty() data: BnplRequestDto;
}

export class AchCreditRequestDto {
    @ApiProperty() @IsString() @IsNotEmpty() user_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() po_number: string;
    @ApiProperty() @IsBoolean() @IsNotEmpty() is_ach_po_approved: boolean;
}
export class SaveAchCreditRequestDto extends BaseDto {
    @Type(() => AchCreditRequestDto)
    @ApiProperty() data: AchCreditRequestDto;
}

export class UserStatusDto {
    @ApiProperty() @IsNumber() @IsNotEmpty() id: number;
    @ApiProperty() @IsBoolean() @IsNotEmpty() is_active: boolean;
}
export class UpdateUserStatusDTo extends BaseDto {
    @Type(() => UserStatusDto)
    @ApiProperty() data: UserStatusDto;
}

export class IncreaseCreditLimitDataDto {
    @ApiProperty() @IsNotEmpty() @IsNumber() user_id: number;
    @ApiProperty() @IsNotEmpty() @IsBoolean() is_approved: boolean;
    @ApiProperty() @IsNotEmpty() @IsString() user_request_increase_credit_id: string;
}

export class IncreaseCreditLimitPayloadDto extends BaseDto {
    @Type(() => IncreaseCreditLimitDataDto)
    @ApiProperty() data: IncreaseCreditLimitDataDto;
}
export class HolidayCalendarDto {
    @ApiProperty() @IsNotEmpty() @IsDateString() holiday_date: string; 
    @ApiProperty() @IsOptional() @IsString() description: string; 
    @ApiProperty() @IsNotEmpty() @IsNumber() days_to_skip: number; 
    @ApiProperty() @IsBoolean() @IsNotEmpty() @Transform(({ value }) => value === 1 ? true : value === 0 ? false : value) is_day_before: boolean;
    @ApiProperty() @IsBoolean() @IsNotEmpty() @Transform(({ value }) => value === 1 ? true : value === 0 ? false : value) is_day_after: boolean;
    @ApiProperty() @IsNotEmpty() @IsString() holiday_start_time: string; 
}

export class SaveHolidayCalendarDto extends BaseDto {
    @ApiProperty({ type: [HolidayCalendarDto] })
    @IsArray()
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => HolidayCalendarDto)
    data: HolidayCalendarDto[];
}

export class DeleteHolidayEntryDto {
    @ApiProperty({ type: [String] })
    @IsArray()
    @ArrayNotEmpty()
    @IsString({ each: true })
    data: string[];
}

export class ExternalApiKeyMappingDto {
    @ApiProperty() @IsNotEmpty() @IsString() api_key_id: string;
    @ApiProperty({ type: [String] }) @IsArray()  @IsString({ each: true }) endpoint: string[];
    @ApiProperty() @IsNotEmpty() @IsNumber() user_slots: number;
  }
  
  export class SaveExternalApiKeyMappingDto extends BaseDto {
    @Type(() => ExternalApiKeyMappingDto)
    @ApiProperty()
    data: ExternalApiKeyMappingDto; 
  }

export class VideoUploadIdDto {
    @ApiProperty() @IsNotEmpty() @IsString() bucket_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() file_name: string;
}

export class GetVideoUploadIdDto extends BaseDto {
    @Type(() => VideoUploadIdDto)
    @ApiProperty() data: VideoUploadIdDto;
}

export class VideoUploadMultipartUrlDto {
    @ApiProperty() @IsNotEmpty() @IsString() bucket_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() file_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() upload_id: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() part_number: number;
    @ApiProperty() @IsNotEmpty() @IsNumber() expire_time: number;

}

export class GenerateVideoUploadMultipartUrlDto extends BaseDto {
    @Type(() => VideoUploadMultipartUrlDto)
    @ApiProperty() data: VideoUploadMultipartUrlDto;
}

export class MultipartUploadPartDto {
    @ApiProperty() @IsNotEmpty() @IsNumber() PartNumber: number;
    @ApiProperty() @IsNotEmpty() @IsString() ETag: string;
}
export class VideoCompleteMultipartUploadUrlDto {
    @ApiProperty() @IsNotEmpty() @IsString() bucket_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() file_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() upload_id: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() expire_time: number;
    @ApiProperty({ type: [MultipartUploadPartDto] }) @IsArray() @ValidateNested({ each: true }) @Type(() => MultipartUploadPartDto) parts: MultipartUploadPartDto[];}

export class GeneratetCompleteMultipartUploadUrlDto extends BaseDto {
    @Type(() => VideoCompleteMultipartUploadUrlDto)
    @IsNotEmpty()  
    @IsObject()  
    @ValidateNested()  
    @ApiProperty() data: VideoCompleteMultipartUploadUrlDto;
}

export class ChangeGameStateDto {
    @ApiProperty() @IsNotEmpty() @IsPositive() initial_ball_speed: number;
    @ApiProperty() @IsNotEmpty() @IsPositive() initial_basket_speed: number;
    @ApiProperty() @IsNotEmpty() @IsPositive() speed_increment_factor: number;
}

export class SaveChangeGameStateDto extends BaseDto {
    @Type(() => ChangeGameStateDto)
    @ApiProperty() data: ChangeGameStateDto;
}

@ValidatorConstraint({ name: 'singleNullMaxMatchCount', async: false })// validate the max_match_count is null only once
export class SingleNullMaxMatchCountConstraint implements ValidatorConstraintInterface {
  validate(value: any) {
    if (!Array.isArray(value)) {
      return false;
    }
    
    const nullMaxCountItems = value.filter(item => item.max_match_count === null);
    return nullMaxCountItems.length === 1;
  }

  defaultMessage() {
    return 'Exactly one configuration must have max_match_count as null';
  }
}

export class ConfidenceLevelConfigDto {
    @ApiProperty() @IsNotEmpty() @IsNumber() min_match_count: number;
    @ApiProperty({ nullable: true }) @IsOptional() @IsNumber() max_match_count: number | null;
    @ApiProperty() @IsNotEmpty() @IsNumber() confidence: number;
}

export class SaveConfidenceLevelConfigDto extends BaseDto {
    @Type(() => ConfidenceLevelConfigDto)
    @ApiProperty({ type: [ConfidenceLevelConfigDto] })
    @IsArray()
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Validate(SingleNullMaxMatchCountConstraint)
    data: ConfidenceLevelConfigDto[];
}

export class SubscriptionPricingDto {
    @ApiProperty() @IsNotEmpty() @IsInt() @IsPositive() min_user_count: number;
    @ApiProperty({ required: false, nullable: true, type: Number }) @IsOptional() @IsInt() @IsPositive() max_user_count: number | null;
    @ApiProperty() @IsNotEmpty() @IsNumberString() subscription_amount: string;
}

export class UpdateSubscriptionPricingDto {
  @ApiProperty({ type: [SubscriptionPricingDto] })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => SubscriptionPricingDto)
  data: SubscriptionPricingDto[];
}
