import { DataBaseService } from '@bryzos/base-library';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GlobalDepositDetails, CustomDepositDetails, UserBuyingPreference, UserAchCredit} from '@bryzos/extended-widget-library';
import { SaveGobalDepositDto, CustomDepositDto } from './dto/cash-in-advance-deposit.dto';
import { Utils } from 'src/utils';


@Injectable()
export class CashInAdvanceDepositService {
  private dbServiceObj = new DataBaseService();

  constructor(
    @InjectRepository(GlobalDepositDetails) private readonly globalDepositDetailsRepository: Repository<GlobalDepositDetails>,
    @InjectRepository(CustomDepositDetails) private readonly customDepositDetailRepository: Repository<CustomDepositDetails>,
    @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    @InjectRepository(UserAchCredit) private readonly UserAchCreditRepository: Repository<UserAchCredit>,

    private readonly utils: Utils,

  ) {}

  async findAll() {
    let response = null;
    let globalDeposit = await this.dbServiceObj.findAll(this.globalDepositDetailsRepository);
    if (globalDeposit.length > 0) {
      response = globalDeposit
    }
    return response;
  }

  async saveGlobalDepositData(adminId, gobalDepositDto: SaveGobalDepositDto)
  {
    let response = null;
    let update = await this.dbServiceObj.markInActive('is_active', true, this.globalDepositDetailsRepository);
    let globalDepositData = await this.dbServiceObj.saveWithOutMapper(gobalDepositDto, adminId, this.globalDepositDetailsRepository);
    
    if(globalDepositData) {
      response = "Successful";
    }

    return response;
  }

  async findAllCustomDepositData()
  {
    let response = null;
    let achUserData = await this.dbServiceObj.findAll(this.UserAchCreditRepository, { "created_date": "DESC" });
    let userBuyingPreference = await this.dbServiceObj.findAll(this.userBuyingPreferenceRepository);

    let customDepositData = await this.dbServiceObj.findAll(this.customDepositDetailRepository);

    if (achUserData.length > 0) {
      return achUserData
      .filter(data => data.is_approved) // filter out unapproved records
      .filter(data => userBuyingPreference.some(preference => preference.user_id === data.user_id))
      .map(data => {
        const userPreference = userBuyingPreference.find(preference => preference.user_id === data.user_id);
        const customDeposit = customDepositData.find(deposit => deposit.user_id === data.user_id);
        const fullName = `${userPreference?.first_name || ""} ${userPreference?.last_name || ""}`;
        let inputDateString = this.utils.convertUtcToCst(data.created_date);
        const formattedDate = inputDateString.format("M/D/YY hh:mm A");
        return {
          id: data.id,
          user_id: data.user_id,
          is_ach_approved: data.is_approved,
          company_name: userPreference?.company_name || null,
          user_name: fullName.trim() || null,
          deposit_percentage: customDeposit?.deposit_percentage || null,
          created_date : formattedDate,
          is_active: data.is_active
        }
      });
    }
    return achUserData;
  }
 
  async saveCustomDepositData(adminId, customDepositDto: CustomDepositDto)
  {
    
    let response = null;
    let userId=customDepositDto.user_id;
    customDepositDto.admin_id = adminId;
    await this.dbServiceObj.markInActive('user_id', userId, this.customDepositDetailRepository);
    let customDepositData = await this.dbServiceObj.saveWithOutMapper(customDepositDto, userId, this.customDepositDetailRepository);
    if(customDepositData) {
      response = "Successful";
    }
    return response;
  }
}
