import { BaseLibraryService, DataBaseService } from '@bryzos/base-library';
import { InjectRepository } from '@nestjs/typeorm';
import { Inject } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Constants } from './Constants';
import * as moment from "moment-timezone";
import { Utils } from "./utils";
import { AwsQueue } from "./AwsQueue";

const axios = require('axios');

export class Balance {
  private dbServiceObj = new DataBaseService();

  constructor(
    @Inject(BaseLibraryService)
    private readonly baseLibraryService: BaseLibraryService,
    private readonly awsQueue: AwsQueue,
    private readonly utils: Utils
  ) {}

  cancelBalanceTransaction = (transactionId, balanceKey) =>
    new Promise(async function (resolve, reject) {

      let cancelResponse = null;
      const cancelUrl =
        process.env.BALANCE_TRANSACTION_URL +
        '/' +
        transactionId +
        '/cancel';

      const axios = require('axios');

      let cancelData = {
        method: 'post',
        maxBodyLength: Infinity,
        url: cancelUrl,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          'x-balance-key': balanceKey,
        },
      };

      axios
        .request(cancelData)
        .then((response) => {
          cancelResponse = response.data;
          resolve(cancelResponse);
        })
        .catch((error) => {
          console.error(error.response.data);
          resolve(cancelResponse);
        });
    });

    async getBalanceKey() {
        return await this.baseLibraryService.getSecretValue(process.env.SM_ENV,Constants.VENDOR_BALANCE, Constants.BALANCE_SECRET_KEY);
    }

  async getTransactionInfo(transactionId, userId) {
    let response = {};
    if (transactionId) {
    try {
        let date_time = moment().format('YYYY-MM-DD HH:mm:ss');
        let transaction_url = process.env.BALANCE_TRANSACTION_URL + `/${transactionId}` + "/info";
        let balance_key = await this.baseLibraryService.getSecretValue(process.env.SM_ENV, Constants.VENDOR_NAME_BALANCE, Constants.SK);
        if (balance_key) {
          let balanceRes = (await axios.request({
            method: 'get',
            url: transaction_url,
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': balance_key,
            },
            }))?.data;

          let payload = this.encode("null");
          let tag = 'GET_TRANSACTION_INFO_ERROR_TAG';
          let balance_error = this.checkForErrors(balanceRes, payload, transaction_url, userId, tag);
          if (balance_error && typeof (balance_error) === "object" && 'error_message' in balance_error) {
            return { "error_message": balanceRes.balance_error };
          }

          if (typeof balanceRes === "object" && 'bank' in balanceRes) {
            response['routing_number'] = balanceRes['bank']?.['bankTransferDetails']?.['routingNum'];
            response['account_number'] = balanceRes['bank']?.['bankTransferDetails']?.['accountNum'];
            this.awsQueue.balance_log(this.encode(JSON.stringify(transaction_url)), this.encode("null"), this.encode("null"), userId, date_time, this.encode('ADMIN_DASHBOARD'), this.encode('GET_TRANSACTION_INFO_TAG'));
          }
        }
      } catch (e) {
        console.log(e);
        // Log:: info("Exception 3");
        // Log:: info(JSON.stringify(e));
      }
    }
    return response;
  }

  checkForErrors(balanceRes, payload, request, userId, tag) {
    let response = {};
    let date_time = moment().format('YYYY-MM-DD HH:mm:ss');
    if (!balanceRes || (typeof balanceRes === "object" && 'error' in balanceRes || 'status' in balanceRes)) {

      if ('status' in balanceRes) {
        if (balanceRes['status'] >= 200 && balanceRes['status'] <= 299) {
          return response;
        }
      }

      try {
        // this will log in balance_log_YYYYMM table 
        this.awsQueue.balance_log(this.encode(payload), this.encode(JSON.stringify(balanceRes)), this.encode('ADMIN_DASHBOARD'), userId, date_time, this.encode(request), this.encode(tag));

      } catch (e) {
        console.log(e);
        // Log:: info("Exception 1 : Check for errors");
        // Log:: info(JSON.stringify(e));
      }
      if (!('message' in balanceRes)) {
        balanceRes['message'] = 'Payment Gateway ERROR';
      }
      response['error_message'] = balanceRes['message'];
    }
    return response;
  }

  encode(data) {
    return Buffer.from(data).toString('base64');
  }

  async getBalanceCreditLimit(balanceBuyerId) {
    let balanceKey = await this.getBalanceKey();
    if (!balanceKey) {
      return null;
    }

    const buyerLimit = { balance_credit_limit: 0, balance_available_credit_limit: 0, status: 'NA' };
    const getQualificationUrl = `${process.env.BALANCE_QUALIFICATION_BASE_URL}/${balanceBuyerId}/creditLimit`;

    try {
      const data = { method: 'get', url: getQualificationUrl, headers: { 'Content-Type': 'application/json', 'x-api-key': balanceKey } };
      const balanceResponse = (await axios.request(data))?.data;

      if (!balanceResponse){
        return buyerLimit;
      }

      buyerLimit.balance_credit_limit = Math.round((balanceResponse.maxCreditLimit / 100) * 100) / 100;
      buyerLimit.balance_available_credit_limit = Math.round((balanceResponse.creditLimit / 100) * 100) / 100;
      buyerLimit.status = balanceResponse.status;
    } catch (error) {
      console.error(error);
      return buyerLimit;
    }

    return buyerLimit;
  }

  //create transaction curl
  async createTransaction(data) {
    const getTransactionLinkUrl = process.env.BALANCE_TRANSACTION_URL;

    let balanceKey = await this.getBalanceKey();

    if (!balanceKey) {
      return {
        error_message: 'something went wrong while fetching balance key!',
      };
    }

    let req = {
      method: 'post',
      url: getTransactionLinkUrl,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        'x-api-key': balanceKey,
      },
      data: data,
    };

    let transactionResponse = null;

    try {
      let responseNew = await axios.request(req);

      if (!responseNew)
        return {
          error_message:
            'Failed to create transaction. unable to fetch data from api!',
        };

      transactionResponse = responseNew.data;
    } catch (error) {
      console.error(error);
      return {
        error_message:
          'Failed to create transaction. unable to fetch data from api!',
      };
    }

    return transactionResponse;
  }

  async financeTransaction(transactionId) {
    const getTransactionLinkUrl =
      process.env.BALANCE_TRANSACTION_URL + '/' + transactionId + '/finance';

    let balanceKey = await this.getBalanceKey();

    if (!balanceKey) {
      return {
        error_message: 'something went wrong while fetching balance key!',
      };
    }

    let req = {
      method: 'post',
      url: getTransactionLinkUrl,
      headers: {
        Accept: 'application/json',
        'x-api-key': balanceKey,
      },
    };

    let createFinanceResponse = null;

    try {
      let responseNew = await axios.request(req);

      if (!responseNew)
        return {
          error_message:
            'Financing transaction failed. unable to fetch data from api!',
        };

      createFinanceResponse = responseNew.data;
    } catch (error) {
      console.error(error);
      return {
        error_message:
          'Financing transaction failed. unable to fetch data from api!',
      };
    }
    return createFinanceResponse;
  }

  async confirmTransaction(transactionId, data) {
    const getTransactionLinkUrl =
      process.env.BALANCE_TRANSACTION_URL + '/' + transactionId + '/confirm';

    let balanceKey = await this.getBalanceKey();

    if (!balanceKey) {
      return {
        error_message: 'something went wrong while fetching balance key!',
      };
    }

    let req = {
      method: 'post',
      url: getTransactionLinkUrl,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'x-api-key': balanceKey,
      },
      data: data,
    };

    let transactionResponse = null;

    try {
      let responseNew = await axios.request(req);

      if (!responseNew)
        return {
          error_message:
            'Transaction confirmation failed. unable to fetch data from api!',
        };

      transactionResponse = responseNew.data;
    } catch (error) {
      console.error(error);
      return {
        error_message:
          'Transaction confirmation failed. unable to fetch data from api!',
      };
    }
    return transactionResponse;
  }
}
