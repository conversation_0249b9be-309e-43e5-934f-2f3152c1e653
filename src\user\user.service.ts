import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseLibraryService, DataBaseService, AwsUtilityV3, Constants as libConstants } from '@bryzos/base-library';
import { UserOnboradPendingRequests,UserPendingCompanyRequests,UserMainCompany,UserBuyingPreference,UserSellingPreference, User, AdminLogSetPassword, AdminLogSpread, ReferenceDataUserOnboardedAppVersion, AdminLogNotification, ReferenceDataSpreadDefaultValues, WidgetUser, EmailVerificationStatus, CompanyResaleCertificate,UserResaleCertificate, BryzosLogger, SpreadActiveUsersView, ReferenceDataDeliveryReceivingAvailabilityDetails,  WidgetTermsCondtionUserActions, UserAchCredit, UserDeliveryReceivingAvailabilityDetails, Constants as LibConstants, UserDeliveryReceivingAvailibitityDetailsMapper, ReferenceDataSalesTax, ReferenceDataSettings, SignUpPreApprovedEmail, SignupUtility, ExternalApiKey, SandboxExternalApiKey, UserSubscriptionEmailInvitations } from '@bryzos/extended-widget-library';
import { CohortNameDto, CompanyNameDto, HidePendingOnBoardUser, PreApprovedEmailsDto, ResetPasswordDto, UpdateCohortSpreadData, UpdateCompanyDetailsDto, UpdateCompanySpreadData, UpdateUserSpreadData, UserUpdateDto, WidgetUserDto } from './dto/user.dto';
import { Constants } from 'src/Constants';
import { HttpService } from '@nestjs/axios';
import { add, addDays, format, parseISO } from 'date-fns';
import { Utils } from 'src/utils';
import { PriceFactorUpdateDto } from '@bryzos/giss-common-lib';
import ShortUniqueId from 'short-unique-id';
import { AwsQueue } from 'src/AwsQueue';
import { utcToZonedTime } from 'date-fns-tz';
import { v4 as uuidv4 } from 'uuid';

const payloadTag = Constants.PAYLOAD_TAG;
const responseErrorTag = Constants.ERROR_TAG;
@Injectable()
export class UserService {
  private dbServiceObj = new DataBaseService()
  private httpService = new HttpService()
  private uniqid = new ShortUniqueId()
  constructor(
    private readonly awsUtility:AwsUtilityV3,
    private readonly utils:Utils,
    private readonly awsQueue : AwsQueue,
    private readonly signupUtility: SignupUtility,
    @InjectRepository(UserOnboradPendingRequests) private readonly userOnboradPendingRequestsRepository: Repository<UserOnboradPendingRequests>,
    @InjectRepository(UserPendingCompanyRequests) private readonly userPendingCompanyRequestsRepository: Repository<UserPendingCompanyRequests>,
    @InjectRepository(UserMainCompany) private readonly userMainCompanyRepository: Repository<UserMainCompany>,
    @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    @InjectRepository(UserSellingPreference) private readonly userSellingPreferenceRepository: Repository<UserSellingPreference>,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(AdminLogSetPassword) private readonly adminLogSetPasswordRepository: Repository<AdminLogSetPassword>,
    @InjectRepository(AdminLogNotification) private readonly adminLogNotificationRepository: Repository<AdminLogNotification>,
    @Inject(BaseLibraryService) private readonly baseLibraryService : BaseLibraryService,
    @InjectRepository(AdminLogSpread) private readonly adminLogSpreadRepository : AdminLogSpread,
    @InjectRepository(ReferenceDataUserOnboardedAppVersion) private readonly referenceDataUserOnboardedAppVersionRepository : ReferenceDataUserOnboardedAppVersion,
    @InjectRepository(ReferenceDataSpreadDefaultValues) private readonly referenceDataSpreadDefaultValuesRepository : ReferenceDataSpreadDefaultValues,
    @InjectRepository(WidgetUser) private readonly widgetUserRepository: WidgetUser,
    @InjectRepository(EmailVerificationStatus) private readonly emailVerificationStatusRepository: EmailVerificationStatus,
    @InjectRepository(CompanyResaleCertificate) private readonly companyResaleCertificateRepository: CompanyResaleCertificate,
    @InjectRepository(UserResaleCertificate) private readonly userResaleCertificateRepository: UserResaleCertificate,
    @InjectRepository(SpreadActiveUsersView) private readonly spreadActiveUsersView: Repository<SpreadActiveUsersView>,
    @InjectRepository(ReferenceDataSalesTax) private readonly referenceDataSalesTaxRepository: Repository<ReferenceDataSalesTax>,
    @InjectRepository(UserAchCredit) private readonly userAchCreditRepository: Repository<UserAchCredit>,
    @InjectRepository(ReferenceDataDeliveryReceivingAvailabilityDetails) private readonly referenceDataDeliveryReceivingAvailabilityDetailsRepository: Repository<ReferenceDataDeliveryReceivingAvailabilityDetails>,
    @InjectRepository(UserDeliveryReceivingAvailabilityDetails) private readonly userDeliveryReceivingAvailabilityDetailsRepository: Repository<UserDeliveryReceivingAvailabilityDetails>,
    @InjectRepository(WidgetTermsCondtionUserActions) private readonly widgetTermsCondtionUserActionsRepository: Repository<WidgetTermsCondtionUserActions>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(SignUpPreApprovedEmail) private readonly signupPreApprovedEmailRepository: Repository<SignUpPreApprovedEmail>,
    @InjectRepository(ExternalApiKey) private readonly externalApiKeyRepository: Repository<ExternalApiKey>,
    @InjectRepository(SandboxExternalApiKey) private readonly sandboxExternalApiKeyRepository: Repository<SandboxExternalApiKey>,
    @InjectRepository(UserSubscriptionEmailInvitations) private readonly userSubscriptionEmailInvitationsRepository: Repository<UserSubscriptionEmailInvitations>,

  ){}

  async getPendingOnBoardUserRequest() {
    const selectCustomFields = ["id", "user_type", "first_name", "last_name", "company_name", "client_company", "email_id", "zip_code", "show_password", 'DATE_FORMAT(CONVERT_TZ(created_date,"UTC","America/Chicago"), "%m/%d/%y %h:%i %p") as onboarding_request_date'];
    let response = null;
    const getAllPendingOnBoardedUsers = await this.dbServiceObj.selectCustomFieldsOrderByCreatedDate(this.userOnboradPendingRequestsRepository,'is_onboarded','false',selectCustomFields,"DESC");
    
    if(getAllPendingOnBoardedUsers && getAllPendingOnBoardedUsers.length > 0){
      response = getAllPendingOnBoardedUsers;
    }
    return response;
  }

  async getOnboardCompaniesRequests() {
    let response = null;
    const selectCustomFields = ["id", "company_name", "is_approved"];
    const conditions1 = [ "is_approved IS NOT NULL and is_active = true"];
    const subquerySelectFields = "company_name"; // Dynamic fields

    const getAllPendingCompanyRequest = await this.dbServiceObj.findRecordsWithDynamicConditions(this.userPendingCompanyRequestsRepository, selectCustomFields, conditions1, subquerySelectFields );

    if(getAllPendingCompanyRequest && getAllPendingCompanyRequest.length > 0){
      response = getAllPendingCompanyRequest;
    }else{
      response = [];
    }
    return response;
  }

  async saveCompanyNameAction(payloads,action){
    let response = null;
    let checkCompanyExist=[];
    let approveIds=[];
    let insertCompany=[];

    for(let payload of payloads){
      let addCompanyInMainList={};
      approveIds.push(payload.id);
      if(checkCompanyExist.includes(payload.company_name.trim()) == false){
        checkCompanyExist.push(payload.company_name.trim());
        addCompanyInMainList["company_name"]=payload.company_name.trim();
        insertCompany.push(addCompanyInMainList);
      }
    }

    let filteredInsertCompanies=null;
    let existCompanyList =[];
    if(action=="true"){
      let checkCompanys = await this.dbServiceObj.findManyByWhereIn(this.userMainCompanyRepository,"company_name",checkCompanyExist);
      if(checkCompanys && checkCompanys.length>0){
        for(let company of checkCompanys){
          existCompanyList.push(company.company_name.toLowerCase());
        }
      }
      filteredInsertCompanies=insertCompany.filter(item => !existCompanyList.includes(item.company_name.toLowerCase()));
      if(existCompanyList.length > 0){
        let updateConditions={"company_name":existCompanyList};
        let updateDto={"is_active":false};
        await this.dbServiceObj.updateByMultipleWhereIn(updateDto,updateConditions,this.userPendingCompanyRequestsRepository);
        response = "Selected companies were already approved!";
      }
      if(filteredInsertCompanies.length > 0){
        if (approveIds.length === 1) {

          let updateDto = { is_approved: true, ...insertCompany[0] };
          await this.dbServiceObj.updateByColumnId(this.userPendingCompanyRequestsRepository, updateDto, "id", approveIds[0]);
          await this.dbServiceObj.saveData(filteredInsertCompanies, this.userMainCompanyRepository);

          let updateDeActivateConditions = { "company_name": checkCompanyExist };
          let updateDeActivateDto = { "is_active": false };
          await this.dbServiceObj.updateByMultipleWhereIn(updateDeActivateDto, updateDeActivateConditions, this.userPendingCompanyRequestsRepository);
          response = "Approved Successfully!";

        } else {
        let updateConditions={"id":approveIds};
        let updateDto={"is_approved":true};
        await this.dbServiceObj.updateByMultipleWhereIn(updateDto,updateConditions,this.userPendingCompanyRequestsRepository);
        await this.dbServiceObj.saveData(filteredInsertCompanies,this.userMainCompanyRepository);
        
        let updateDeActivateConditions={"company_name":checkCompanyExist};
        let updateDeActivateDto={"is_active":false};
        await this.dbServiceObj.updateByMultipleWhereIn(updateDeActivateDto,updateDeActivateConditions,this.userPendingCompanyRequestsRepository);
        response = "Approved Successfully!";
      }
      }
    }else if(action == "false"){
      let updateConditions={"company_name":checkCompanyExist}
      let updateDto={"is_active":false,"is_approved":false}
      await this.dbServiceObj.updateByMultipleWhereIn(updateDto,updateConditions,this.userPendingCompanyRequestsRepository);
      response = "Rejected Successfully!";
    }
    return response;
  }

  async updateCompanyDetails(payload){
    let response = null;
    let sanatizeName = await this.sanatizeCompanyName(payload.company_name);
    let checkCondition = {
      "company_name":sanatizeName
    }

    const existInPendingCompany = await this.dbServiceObj.findOneByMultipleWhere(this.userPendingCompanyRequestsRepository, checkCondition);
    if (existInPendingCompany) {
      return { error_message: "The company " + payload.company_name + " already exists in pending company list!" };
    }
    const createdApiKeys = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.externalApiKeyRepository,{'company_id' : payload.id, 'is_active' : true, 'status': true});
    if (createdApiKeys.length > payload.allowed_api_keys_count) {
      return {error_message : "The number of allowed API keys must not be less than the active keys." };
    }
    payload.sandbox_allowed_api_keys_count = !payload?.sandbox_allowed_api_keys_count ? 0 : payload.sandbox_allowed_api_keys_count 
    const createdSandboxApiKeys = await this.dbServiceObj.findManyByMultipleWhereAndSelect(this.sandboxExternalApiKeyRepository,{'company_id' : payload.id, 'is_active' : true, 'status': true});
    if (createdSandboxApiKeys.length > payload?.sandbox_allowed_api_keys_count) {
      return {error_message : "The number of allowed API keys for sandbox must not be less than the active sandbox keys." };
    }

    const checkCompanyexist = await this.dbServiceObj.findOneByMultipleWhere(this.userMainCompanyRepository,checkCondition);
    
    if(!checkCompanyexist){
      let updateDto = {"company_name":sanatizeName, "allowed_api_keys_count" : payload.allowed_api_keys_count, "sandbox_allowed_api_keys_count" : payload.sandbox_allowed_api_keys_count};
      let whereColumns = {"id":payload.id};
      const updateCompanyName = await this.dbServiceObj.updateByMultipleWhere(updateDto,whereColumns,this.userMainCompanyRepository);
      if(updateCompanyName){
        let updateUserSettingDto = {"company_name":sanatizeName};
        let whereUserSettingColumns = {"company_id":payload.id};
        await this.dbServiceObj.updateByMultipleWhere(updateUserSettingDto,whereUserSettingColumns,this.userBuyingPreferenceRepository);
        await this.dbServiceObj.updateByMultipleWhere(updateUserSettingDto,whereUserSettingColumns,this.userSellingPreferenceRepository);
        response = "Company details updated successfully!";
      }
    }else{
      const company = await this.dbServiceObj.findOneByMultipleWhere(this.userMainCompanyRepository, { id: payload.id });
  
      if (company) {
        const _companyName = company.company_name.toLowerCase().trim();
        const _sanatizeName = sanatizeName.toLowerCase();

        if (_companyName === _sanatizeName) {
          let updateDto = { company_name: sanatizeName };
          let updateCondition = { company_id: payload.id };

          const mainCompanyUpdateResult = await this.dbServiceObj.updateByMultipleWhere({ "company_name": sanatizeName, "allowed_api_keys_count" : payload.allowed_api_keys_count, "sandbox_allowed_api_keys_count" : payload.sandbox_allowed_api_keys_count }, { id: payload.id }, this.userMainCompanyRepository);
          
          if (!mainCompanyUpdateResult) {
            return "Unable to update company name!";
          }

          await this.dbServiceObj.updateByMultipleWhere(updateDto, updateCondition, this.userBuyingPreferenceRepository);
          await this.dbServiceObj.updateByMultipleWhere(updateDto, updateCondition, this.userSellingPreferenceRepository);

          return "Company details updated successfully!";
        }
      }

      response = { error_message : "The company "+payload.company_name+" already exists in our list!" } ;
    }
    return response;
  }

  async saveCompanyName(payload:CompanyNameDto){
    let response=null;
    let sanatizeName = await this.sanatizeCompanyName(payload.company_name);
    payload["company_name"]=sanatizeName;
    if(payload["company_name"] != ""){
      let checkCompany = await this.dbServiceObj.findOneByMultipleWhere(this.userMainCompanyRepository,payload);
      if(!checkCompany){
        let checkPendingrequest = await this.dbServiceObj.findOneByMultipleWhere(this.userPendingCompanyRequestsRepository,payload);
        if(checkPendingrequest){
          let updateConditions={"company_name":sanatizeName};
          let updateDto={"is_active":false};
          await this.dbServiceObj.updateByMultipleWhere(updateDto,updateConditions,this.userPendingCompanyRequestsRepository);
        }
        await this.dbServiceObj.saveData(payload,this.userMainCompanyRepository);
        response = "Added Successfully!";
      }else{
        response = {"error_message":payload.company_name+" already exist"};
      }
    }else{
      response = {"error_message":"Please enter valid company name"};
    }
    return response;
  }

  async sanatizeCompanyName(companyName){
    return companyName.replace(/<[^>]*>/g, '').trim();
  }

  async getWidgetUsersList(){
    let response = null;
    const leftJoin = { "user" : "company_id" };
    const selectCustomFields = ['table2.*', 'table2.is_buyer_spread AS disc_is_discounted', 'table2.base_pricing_column AS disc_discount_pricing_column',  'table2.buyer_spread_rate AS disc_discount_rate', 'table2.is_buyer_spread_overriden AS disc_is_discount_var_overriden', 'table2.deprecated_disc_discount_phaseout_startdate AS disc_discount_phaseout_startdate', 'table2.deprecated_disc_discount_phaseout_period AS disc_discount_phaseout_period', 'table2.deprecated_disc_discount_period AS disc_discount_period', 'DATE_FORMAT(CONVERT_TZ(table2.created_date,"UTC","America/Chicago"), "%m/%d/%y %h:%i %p") as joining_date', "table1.company_name as company_name"];
    
    let conditions = {};
    const condition1_attributes = {"operator":"NOT NULL","table":"table2"};
    conditions["id"] = condition1_attributes;
    const combineAndConditions = {conditions};
    const getUsers = await this.dbServiceObj.FindByMultipleWhereComparisonANDLeftJoinById(this.userMainCompanyRepository,leftJoin,combineAndConditions,null,selectCustomFields,{'table2.created_date': 'DESC'},false);
    
    if(getUsers && getUsers.length > 0){
      response=getUsers;
    }
    return response;
  }

  async setUserPassword(adminId: string, resetPasswordDto: ResetPasswordDto) {
    //get user from DB
    let response = null;
    let user = await this.dbServiceObj.findOne(this.userRepository, 'email_id', resetPasswordDto.email_id);
    if(user) {
      let cognitoUserName = user.cognito_user_name;
      let userEmail = user.email_id;

      const insert = {
        email_id: resetPasswordDto.email_id,
        admin_id: adminId,
        is_active: true,
        cognito_response: null
      };

      try {
        const result = await this.awsUtility.adminSetUserPassword(cognitoUserName,resetPasswordDto.password);
        
        // Log in admin log table
        insert.cognito_response = JSON.stringify(result);
        this.dbServiceObj.saveData(insert, this.adminLogSetPasswordRepository);

        //sign out user from all devices
        const globalSignOut = await this.awsUtility.userGlobalSignOut(cognitoUserName);

        if (globalSignOut) {
          BryzosLogger.log(JSON.stringify({"response": {"cognito_result":globalSignOut, "emailId": resetPasswordDto.email_id,'event': 'Global Signout'}}), process.env.LOGGLY_ADMIN_CHANGE_PASSWORD_TAG);
          response = "Password reset successful";

          // password never changed = 0 (Only for old/existing pending users)
          // reset password = 1 (via A.D. Reset)
          // passwordless migrated done (user self set) = 2 (Password has been set by user).
          await this.dbServiceObj.updateWithoutMapper({ 'is_migrated_to_password': 1}, 'email_id', userEmail, this.userRepository);
        } else {
          response = "Something went wrong";
        }
        const socketData = { 'email_id': resetPasswordDto.email_id, 'device_id': "AD Reset Password" };
        const forceLogoutUserResponse = await this.utils.sendWebsocketEvent(socketData, 'passwordChanged');
        if (!forceLogoutUserResponse) {
          return { [responseErrorTag]: "Sorry unable to force logout user" };
        }

        //sign out user from all devices end
  
      } catch (error) {
        // Log in admin log table
        insert.cognito_response = JSON.stringify(error);
        this.dbServiceObj.saveData(insert, this.adminLogSetPasswordRepository);
        response = "Something went wrong";
      }
    }
    return response;
  }

  async hidePendingOnBoardUser(userData: HidePendingOnBoardUser) {
    const hideUserResult = await this.dbServiceObj.updateByColumnId(this.userOnboradPendingRequestsRepository, { is_active: 0, hide_reason: userData.hide_reason }, 'id', userData.id);
    if (!hideUserResult) {
      return "Something went wrong";
    }

    return "User has been hidden successfully";
  }

  async updateUserSpread(adminId:number,payload:UpdateUserSpreadData, accessToken:string){
    const currentDate = new Date();
    const userId = payload.user_id;
    // let discountPhaseOutStartDate:string = null;
    
    // const currentTime = new Date().toUTCString().split(' ')[4];
    // discountPhaseOutStartDate = payload.discount_phaseout_startdate ? `${payload.discount_phaseout_startdate} ${currentTime}` : null;
    
    // if(payload.is_discount && currentDate > new Date(discountPhaseOutStartDate)){
    //   return {"error_message":"The discount phase-out date should be later than today's date"}
    // }

    const userData = await this.dbServiceObj.findOne(this.userRepository,"id",userId.toString());
    if(!userData){ return { error_message : "Unable to provide spread to inactive user"} }
    
    let updateDto = {};
    let buyerSpreadPercentage = payload.discount_percentage;
    let sellerSpreadPercentage = payload.seller_spread_percentage;
    let sellerSpreadRate = 1 + Number(sellerSpreadPercentage)/100;

    if(payload.is_overriden === false && userData.is_buyer_spread_overriden){
      const usersCohortData = await this.dbServiceObj.findOne(this.referenceDataUserOnboardedAppVersionRepository,"onboarded_app",userData.onboarded_app);
      updateDto = await this.signupUtility.spreadSettingsVerifications(usersCohortData)
      if(updateDto !== false){
        updateDto["is_buyer_spread"] = true;
        updateDto["is_buyer_spread_overriden"] = payload.is_overriden;
        buyerSpreadPercentage = usersCohortData.buyer_spread_percentage;
      }else{
        const userCompanyData = await this.dbServiceObj.findOne(this.userMainCompanyRepository,"id",userData.company_id);
        updateDto = await this.signupUtility.spreadSettingsVerifications(userCompanyData);
        if(updateDto !== false){
          updateDto["is_buyer_spread"] = true;
          updateDto["is_buyer_spread_overriden"] = payload.is_overriden;
          buyerSpreadPercentage = userCompanyData.buyer_spread_percentage;
        }else{
          
          updateDto = {};
          updateDto["is_buyer_spread"] = payload.is_discount;
          updateDto["buyer_spread_rate"] = 1 - Number(payload.discount_percentage)/100;
          // updateDto["disc_discount_period"] = payload.discount_period;
          // updateDto["disc_discount_phaseout_startdate"] = discountPhaseOutStartDate;
          // updateDto["disc_discount_phaseout_period"] = payload.discount_phaseout_period;
          updateDto["base_pricing_column"] = payload.discount_pricing;
          updateDto["is_buyer_spread_overriden"] = payload.is_overriden;
          updateDto["seller_spread_rate"] = sellerSpreadRate;
        }
      }
    }else{

      const usersCohortData = await this.dbServiceObj.findOne(this.referenceDataUserOnboardedAppVersionRepository,"onboarded_app",userData.onboarded_app);
      const isCohortSpreadData = await this.signupUtility.spreadSettingsVerifications(usersCohortData)
      
      const userCompanyData = await this.dbServiceObj.findOne(this.userMainCompanyRepository,"id",userData.company_id);
      const isCompanySpreadData = await this.signupUtility.spreadSettingsVerifications(userCompanyData);

      if ((isCohortSpreadData != false || isCompanySpreadData != false) && payload.is_overriden === false && userData.is_buyer_spread_overriden === 0 ){
        return {"error_message":"Please ensure that the 'Overridden' checkbox is selected before updating the user spread data"};
      }
      
      updateDto["is_buyer_spread"] = payload.is_discount;
      updateDto["buyer_spread_rate"] = 1 - Number(buyerSpreadPercentage)/100;
      // updateDto["disc_discount_period"] = payload.discount_period;
      // updateDto["disc_discount_phaseout_startdate"] = discountPhaseOutStartDate;
      // updateDto["disc_discount_phaseout_period"] = payload.discount_phaseout_period;
      updateDto["base_pricing_column"] = payload.discount_pricing;
      updateDto["is_buyer_spread_overriden"] = payload.is_overriden;
      updateDto["seller_spread_rate"] = sellerSpreadRate;
    }

    const updateUserSpreadData = await this.dbServiceObj.updateByColumnId(this.userRepository,updateDto,'id',userId);
    if(updateUserSpreadData){
      updateDto["admin_id"] = adminId;
      updateDto["user_id"] = userId;
      updateDto["buyer_spread_percentage"] = buyerSpreadPercentage;
      updateDto["source"]=Constants.USER_UPDATE_SPREAD_DATA;

      await this.dbServiceObj.saveData(updateDto,this.adminLogSpreadRepository);

      let sendWebsocketEvent = false;
      if(userData.is_buyer_spread != updateDto["is_buyer_spread"] || 100 - Number(userData.buyer_spread_rate)*100 != buyerSpreadPercentage || userData.base_pricing_column != updateDto["base_pricing_column"] ){
        sendWebsocketEvent = true;
      }

      if(sendWebsocketEvent){
        // const sendDataToPusherNotification = {
        //   users:  [userData]
        // }
        // this.spreadPriceChangeSendPusherNotification(sendDataToPusherNotification, adminId, accessToken)

        const isBuyerSpread = updateDto["is_buyer_spread"];
        const buyerSpreadRate = updateDto["buyer_spread_rate"];
        const basePricingColumn = updateDto["base_pricing_column"];
    
        this.sendPriceFactorToSocket(isBuyerSpread, buyerSpreadRate, basePricingColumn, [userData.email_id]);    
      }
      return "The user spread data has been updated successfully";
    }else{
      return {"error_message":"Something went wrong"};
    }
  }

  async sendPriceFactorToSocket(isDiscounted: boolean, discountRate: number, discountPricingColumn: string, emailIds: string[]) {
    try {
      const payload: PriceFactorUpdateDto = { isDiscounted, discountRate, discountPricingColumn, emailIds };
      const response = (await this.httpService.axiosRef.post(`${process.env.GISS_WS_SERVER}/emitPriceFactorChangeEvent`, payload))?.data;
      return response;
    } catch (err) {
      return { [responseErrorTag]: err?.message ?? "unable to call emit price-factor change" }
    }
  }

  async updateCompanySpread(adminId:number,payload:UpdateCompanySpreadData) {
    if(!payload.is_discount){
      payload.discount_percentage = null;
      payload.discount_pricing = null;
    }

    let sellerSpreadPercentage = payload.seller_spread_percentage;
    let sellerSpreadRate = 1 + Number(sellerSpreadPercentage)/100;

    const companyId = payload.company_id;

    const beforeUpdateCompanyData = await this.dbServiceObj.findOne(this.userMainCompanyRepository,"id",companyId.toString());
    let sendWebsocketEvent = false;
    
    let updateDto = {};
    updateDto["buyer_spread_percentage"] = payload.discount_percentage;
    updateDto["base_pricing_column"] = payload.discount_pricing;
    updateDto["seller_spread_rate"] = sellerSpreadRate;

    const updateCompanySpreadData = await this.dbServiceObj.updateByColumnId(this.userMainCompanyRepository,updateDto,'id',companyId);
    if(!updateCompanySpreadData) { return {"error_message":"Something went wrong"}; }

    const companyBuyers = await this.dbServiceObj.findManyWithWhere(this.userRepository,"company_id",companyId.toString(),"type",Constants.BUYER);

    let filteredArray = companyBuyers.filter(obj => !(obj.is_buyer_spread_overriden));
    updateDto["is_buyer_spread"] = payload.is_discount;
    
    const userUpdateDtoArray = await this.assignSpreadToUsers(filteredArray,updateDto);
    const updatingUsersSpreadData = await this.dbServiceObj.saveData(userUpdateDtoArray,this.userRepository);

    if(Number(updatingUsersSpreadData.length) != Number(userUpdateDtoArray.length)) { 
      return { error_message: "An issue occurred where the company's spread data was updated, but the changes did not reflect for certain buyers" };
    }
    
    let insertAdminLogArray = [];
    let userIds = [];
    for(let adminLog of userUpdateDtoArray) {
      adminLog["admin_id"] = adminId;
      adminLog["user_id"]=adminLog.id;
      adminLog["company_id"]=companyId;
      adminLog["buyer_spread_percentage"]=payload.discount_percentage;
      adminLog["source"]=Constants.COMPANY_UPDATE_SPREAD_DATA;
      userIds.push(adminLog.id);
      delete(adminLog.id);
      insertAdminLogArray.push(adminLog);
    }

    this.dbServiceObj.saveData(insertAdminLogArray,this.adminLogSpreadRepository);

    if(beforeUpdateCompanyData.buyer_spread_percentage != payload.discount_percentage || beforeUpdateCompanyData.base_pricing_column != payload.discount_pricing ){
      sendWebsocketEvent = true;
    }else if(filteredArray.filter(obj => !(obj.is_buyer_spread)).length > 0){
      filteredArray = filteredArray.filter(obj => !(obj.is_buyer_spread));
      sendWebsocketEvent = true;
    }else{
      sendWebsocketEvent = false;
    }

    if(sendWebsocketEvent){
      const sendUsersToWebsocket = filteredArray.filter(item => userIds.includes(item.id));

      const isBuyerSpread = payload.is_discount;
      const buyerSpreadRate = 1 - payload.discount_percentage / 100;
      const basePricingColumn = payload.discount_pricing;
      const emailIds = sendUsersToWebsocket.map(user => user.email_id);
      const response = await this.sendPriceFactorToSocket(isBuyerSpread, buyerSpreadRate, basePricingColumn, emailIds);
    }
    
    return  "Company spread details updated successfully";
  }

  async updateCohortSpread(adminId:number,payload:UpdateCohortSpreadData, accessToken:string){
    if(!payload.is_discount){
      payload.discount_percentage = null;
      payload.discount_pricing = null;
    }

    const cohortId = payload.cohort_id;
    let sellerSpreadPercentage = payload.seller_spread_percentage;
    let sellerSpreadRate = 1 + Number(sellerSpreadPercentage)/100;

    const beforeUpdateCohortData = await this.dbServiceObj.findOne(this.referenceDataUserOnboardedAppVersionRepository,"id",cohortId.toString());
    let sendWebsocketEvent = false;
    if(beforeUpdateCohortData.buyer_spread_percentage != payload.discount_percentage || beforeUpdateCohortData.base_pricing_column != payload.discount_pricing ){
      sendWebsocketEvent = true;
    }

    let updateDto = {};
    updateDto["buyer_spread_percentage"] = payload.discount_percentage;
    updateDto["base_pricing_column"] = payload.discount_pricing;
    updateDto["seller_spread_rate"] = sellerSpreadRate;

    const updateCohortSpreadData = await this.dbServiceObj.updateByColumnId(this.referenceDataUserOnboardedAppVersionRepository,updateDto,'id',cohortId);
    if(!updateCohortSpreadData) { return {"error_message":"Something went wrong"};  }

      const getCohortdetails = await this.dbServiceObj.findOne(this.referenceDataUserOnboardedAppVersionRepository,"id",cohortId.toString());

      const cohortName = getCohortdetails.onboarded_app;

      const cohortBuyers = await this.dbServiceObj.findManyWithWhere(this.userRepository,"onboarded_app",cohortName,"type",Constants.BUYER);

      const filteredArray = cohortBuyers.filter(obj => !(obj.is_buyer_spread_overriden));
      const userEmails: string[] = filteredArray.map(buyer => buyer.email_id);

      updateDto["is_buyer_spread"] = payload.is_discount;
      const userUpdateDtoArray = await this.assignSpreadToUsers(filteredArray,updateDto);

      const updatingUsersSpreadData = await this.dbServiceObj.saveData(userUpdateDtoArray,this.userRepository);

      if(Number(updatingUsersSpreadData.length) != Number(userUpdateDtoArray.length)) 
      { return {"error_message":"An issue occurred where the cohort spread data was updated, but the changes did not reflect for certain buyers"} }

      let insertAdminLogArray = [];
      for(let adminLog of userUpdateDtoArray){
        adminLog["admin_id"] = adminId;
        adminLog["user_id"] = adminLog.id;
        adminLog["cohort"] = cohortName;
        adminLog["buyer_spread_percentage"] = payload.discount_percentage;
        adminLog["source"] = Constants.COHORT_UPDATE_SPREAD_DATA;
        delete(adminLog.id);
        insertAdminLogArray.push(adminLog);
      }

      await this.dbServiceObj.saveData(insertAdminLogArray,this.adminLogSpreadRepository);

      if(sendWebsocketEvent){
        const isBuyerSpread = payload.is_discount;
        const buyerSpreadRate = 1 - Number(payload.discount_percentage) / 100;
        const basePricingColumn = payload.discount_pricing;
        await this.sendPriceFactorToSocket(isBuyerSpread, buyerSpreadRate, basePricingColumn, userEmails)
      }

      return "Cohort spread details updated successfully";
  }

  async saveCohort(payload:CohortNameDto){
    let response = null;
    let sanatizeName = await this.sanatizeCompanyName(payload.cohort_name);
    payload["cohort_name"] = sanatizeName;
    if(payload["cohort_name"] != ""){
      const checkCondition = {onboarded_app:sanatizeName}
      let checkCohort = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataUserOnboardedAppVersionRepository,checkCondition);
      if(!checkCohort){
        const saveDto = {
          "onboarded_app":sanatizeName,
          "onboarded_type":sanatizeName
        };
        await this.dbServiceObj.saveData(saveDto,this.referenceDataUserOnboardedAppVersionRepository);
        response = "Added Successfully!";
      }else{
        response = {"error_message":payload.cohort_name+" already exist"};
      }
    }else{
      response = {"error_message":"Please enter valid cohort name"};
    }
    return response;
  }

  async assignSpreadToUsers(filteredArray:any,spreadData:any){
    const userUpdateDtoArray = [];
    const getAllCohorts = await this.dbServiceObj.findAll(this.referenceDataUserOnboardedAppVersionRepository);
    const cohortArray = {};
    for(const cohort of getAllCohorts){
      cohortArray[cohort.onboarded_app] = cohort;
    }
    
    for(const user of filteredArray) {
      let userUpdateObject = {};
      if(cohortArray[user.onboarded_app] && typeof cohortArray[user.onboarded_app] === "object") {
        let isCohort = await this.signupUtility.spreadSettingsVerifications(cohortArray[user.onboarded_app]);
        isCohort === false ? userUpdateObject = await this.signupUtility.spreadSettingsVerifications(spreadData) : userUpdateObject = isCohort;
      } else {
        userUpdateObject = await this.signupUtility.spreadSettingsVerifications(spreadData);
      }
      if(userUpdateObject === false) {
        userUpdateObject = {};
      };
      userUpdateObject["id"] = user.id;
      userUpdateObject["is_buyer_spread"] = spreadData.is_buyer_spread;
      user.is_buyer_spread == 0 && spreadData.is_buyer_spread == false ? null : userUpdateDtoArray.push(userUpdateObject);

    }
    return userUpdateDtoArray;
  }

  async updateUserData(payload:UserUpdateDto,adminId:string){
    const updateDto = {};
    updateDto["first_name"] = payload.first_name;
    updateDto["last_name"] = payload.last_name;
    updateDto["client_company"] = payload.client_company;
    updateDto["email_id"] = payload.email;
    updateDto["type"] = payload.user_type;
    updateDto["onboarded_app"] = payload.onboard_source;
    const companyName = payload.company_name.trim();
    updateDto["company_name"] = companyName;
    updateDto["is_external_api_company_admin"] = payload.is_external_api_company_admin;
    
    const userId = payload.user_id; 
    updateDto["id"] = userId;
    
    const userBeforeUpdateData = await this.dbServiceObj.findOne(this.userRepository,"id",userId.toString());
    if(!userBeforeUpdateData){ return { error_message : " Cannot update users who are marked as inactive" } };

    
    updateDto["created_date"] = userBeforeUpdateData.created_date;
    updateDto["is_buyer_spread_overriden"] = userBeforeUpdateData.is_buyer_spread_overriden;

    await this.signupUtility.getSpreadSettings(updateDto);

    const source = updateDto["source"];
    if(source === Constants.DEFAULT_DISCOUNT_UPDATE_DATA){ updateDto["is_buyer_spread"] = false }

    // if( updateDto["company_id"] == userBeforeUpdateData["company_id"] && updateDto["onboarded_app"] == userBeforeUpdateData["onboarded_app"] ){ return { error_message : "No update provided in user data" } }

    const buyerSpreadPercentage = updateDto.hasOwnProperty("buyer_spread_percentage") ? updateDto["buyer_spread_percentage"] : null
    delete(updateDto["created_date"]);
    delete(updateDto["company_name"]);
    delete(updateDto["source"]);
    
    this.dbServiceObj.saveData(updateDto,this.userRepository);

    if(updateDto["company_id"] != userBeforeUpdateData["company_id"]){
      await this.updateUserResaleCertificateByCompany(userId,updateDto["company_id"],userBeforeUpdateData["company_id"]);
    }

    const userType = payload.user_type?.toLocaleLowerCase();
    const preferenceUpdateDto = { company_name: payload.company_name, client_company: payload.client_company, company_id: updateDto["company_id"] };
    const preferenceCondition = { user_id: userId };

    if (userType === Constants.BUYER.toLocaleLowerCase()) {
      await this.dbServiceObj.updateByMultipleWhere(preferenceUpdateDto, preferenceCondition, this.userBuyingPreferenceRepository);
    } else if (userType === Constants.SELLER.toLocaleLowerCase()) {
      await this.dbServiceObj.updateByMultipleWhere(preferenceUpdateDto, preferenceCondition, this.userSellingPreferenceRepository);
    }

    if(buyerSpreadPercentage != null && (updateDto["company_id"] != userBeforeUpdateData["company_id"] || updateDto["onboarded_app"] != userBeforeUpdateData["onboarded_app"])){

      const adminLog = {
        'admin_id' : adminId,
        'user_id' : updateDto["id"],
        'is_buyer_spread' : updateDto["is_buyer_spread"],
        'buyer_spread_percentage' : buyerSpreadPercentage,
        'buyer_spread_rate' : updateDto["buyer_spread_rate"],
        // 'disc_discount_period' : updateDto["disc_discount_period"],
        // 'disc_discount_phaseout_startdate' : updateDto["disc_discount_phaseout_startdate"],
        // 'disc_discount_phaseout_period' : updateDto["disc_discount_phaseout_period"],
        'base_pricing_column' : updateDto["base_pricing_column"],
        'is_buyer_spread_overriden' : updateDto["is_buyer_spread_overriden"],
        'source' : source,
        'cohort': updateDto['onboarded_app'],
        'company_id': updateDto["company_id"]
      }

      let sendWebsocketEvent = false;
      if(userBeforeUpdateData.is_buyer_spread != updateDto["is_buyer_spread"] || 100 - Number(userBeforeUpdateData.buyer_spread_rate)*100 != buyerSpreadPercentage || userBeforeUpdateData.base_pricing_column != updateDto["base_pricing_column"] ){
        sendWebsocketEvent = userBeforeUpdateData.is_buyer_spread == false && updateDto["is_buyer_spread"] === false ? false : true;
      }

      if(sendWebsocketEvent){
        this.sendPriceFactorToSocket(updateDto["is_buyer_spread"], updateDto["buyer_spread_rate"], updateDto["base_pricing_column"], [userBeforeUpdateData.email_id]);
      }

      this.dbServiceObj.saveData(adminLog,this.adminLogSpreadRepository);

    }
    return "User details updated successfully";
  }

  async updateUserResaleCertificateByCompany(userId:number,newCompanyId:string,OldCompanyId:string){
    const getCompanyResaleCertData = await this.dbServiceObj.findManyByWhereIn(this.companyResaleCertificateRepository,"company_id",[newCompanyId,OldCompanyId]);

    const newCompanyUserResaleCertDto = [];
    const oldCompanyStateIds = [];    
    const newCompanyStateIds = [];
    for(const certData of getCompanyResaleCertData){
      const newData = Object.assign({}, certData);
      if(certData.company_id === OldCompanyId){ 
        oldCompanyStateIds.push(newData.state_id) 
      }else{
        newCompanyStateIds.push(certData.state_id);
        delete newData["admin_id"];
        delete newData["is_active"];
        delete newData["created_date"];
        delete newData["time_stamp"];
        delete newData["deleted_by"];
        delete newData["id"];
        newData["user_id"] = userId;
        newData["is_deletable"] = false;
        newCompanyUserResaleCertDto.push(newData);
      }
    }

    let stateIds = [...oldCompanyStateIds, ...newCompanyStateIds];
    stateIds = [...new Set(stateIds)];
    const oldCompanyResaleCerts = await this.dbServiceObj.findManyWithWhere(this.userResaleCertificateRepository,"company_id",OldCompanyId,"user_id",userId.toString());
    
    let oldCertDeleteDto = [];
    let userUploadedCertData = [];
    if(oldCompanyResaleCerts && oldCompanyResaleCerts.length > 0){
      oldCertDeleteDto = oldCompanyResaleCerts.map(obj => {
        if(!stateIds.includes(obj.state_id)){ 
          const userUploadCerts = Object.assign({}, obj);
          delete userUploadCerts["is_active"];
          delete userUploadCerts["created_date"];
          delete userUploadCerts["time_stamp"];
          delete userUploadCerts["id"];
          userUploadCerts["is_deletable"] = true;
          userUploadCerts.company_id = newCompanyId;
          userUploadedCertData.push(userUploadCerts) 
        }
        return {
          id : obj.id,
          is_active : false
        }
      })
    }

    const userUploadAndCompanyCertsDto = [...newCompanyUserResaleCertDto, ...userUploadedCertData]

    oldCertDeleteDto.length > 0 ? await this.dbServiceObj.saveData(oldCertDeleteDto,this.userResaleCertificateRepository) : null;
    userUploadAndCompanyCertsDto.length > 0 ? await this.dbServiceObj.saveData(userUploadAndCompanyCertsDto,this.userResaleCertificateRepository) : null;
  }

  async getSpreadUsers() {
    const discountedUsers = await this.dbServiceObj.findAllWithoutIsActive(this.spreadActiveUsersView);
    if (discountedUsers?.length) {
      return discountedUsers.map(user => ({ 
        ...user, 
        is_discount_overriden: user.is_buyer_spread_overriden ? "Yes" : "No",
        is_discounted: user.is_buyer_spread,
        discount_rate: user.buyer_spread_rate,
        discount_pricing_column: user.base_pricing_column,
        initial_constant_discount_period: user.deprecated_initial_constant_discount_period,
        discount_compression_starting_date: user.deprecated_discount_compression_starting_date,
        discount_compression_period: user.deprecated_discount_compression_period,

       }));
    } else {
      return { [responseErrorTag]: "Sorry no data found" };
    }
  }

  async createWidgetUser(userData: WidgetUserDto, adminId: string) {

    let response: any = null

    const pendingRequestAvailable = await this.signupUtility.checkUserEmailAlreadyExist(userData.email_id);

    //Request is received from "Create User" module, so make sure we don't insert duplicate email id
    if(!userData.id) {
      if (pendingRequestAvailable.hasOwnProperty("is_in_onBoard_request") && pendingRequestAvailable['is_in_onBoard_request']) {
        response = { error_message: "This email-id already exist in your on-board pending requests" };
        return response;
      }
    } else {
      //Below condition check's if user is present in on-board requests and have they signed up after change in signup flow ? If yes, we can only make that user from pending user list i.e. user/on-board API
      if (pendingRequestAvailable.hasOwnProperty("show_password") && pendingRequestAvailable['show_password'] === 0 ) {
        response = { error_message: "This email-id already exist in your on-board pending requests" };
        return response;
      }

      //This condition is added to handle existing user in on-board list. New user's are handled by user/on-board API
      if (userData.zip_code) {
        const isZipCodeValidate = await this.signupUtility.checkZipCodeValidation(userData.zip_code)

        if(isZipCodeValidate.hasOwnProperty("error_message")){ return isZipCodeValidate }
        
        userData.state_id = isZipCodeValidate.state_id;
      }
    }

    const isCompanyExists = await this.signupUtility.validateAndSaveCompany(userData.company_name);

    if(isCompanyExists.hasOwnProperty('error_message')) {
      return isCompanyExists;
    }
    userData.company_id = isCompanyExists["company_id"];

    //If request comes from "Create user" module, we need to make user in cognito as well. For onboard pending request's UI makes user in cognito
    let cognitoResponse = null;
    cognitoResponse = await this.createCognitoUser(userData);
    if (typeof cognitoResponse === 'object' && cognitoResponse?.error_message) {
      response = cognitoResponse;
      return response;
    } else {
      userData.cognito_user_name = cognitoResponse;
    }
    userData.is_migrated_to_password = 1;
    
    const widgetUser = await this.createWidgetUserData(userData, adminId);
    if (widgetUser?.error_message) {
      return widgetUser;
    } else if(widgetUser){
      response = 'User Created';
    }
    
    return response;
  }

  async createCognitoUser(userData: WidgetUserDto) {
    let response = null;
    let cognitoUserName = null;
    const email = userData.email_id;
    const firstName = userData.first_name;
    const lastName = userData.last_name;
    const password = userData.password;
    const userType = userData.user_type;

    const checkUserExist = await this.dbServiceObj.findOne(this.userRepository, 'email_id', email);
    if (checkUserExist) {
      response = { error_message: "User already exist!" };
      return response;
    }

    try {
      const userAttributes = [
        {
          "Name": LibConstants.AWS_USER_ATTRIBUTE_EMAIL,
          "Value": email,
        },
        {
          "Name": LibConstants.AWS_USER_ATTRIBUTE_EMAIL_VERIFIED,
          "Value": process.env.AWS_COGNITO_EMAIL_VERIFIED,
        },
        {
          "Name": process.env.AWS_USER_ATTRIBUTE_FIRST_NAME,
          "Value": firstName,
        },
        {
          "Name": process.env.AWS_USER_ATTRIBUTE_LAST_NAME,
          "Value": lastName,
        },
        {
          "Name": process.env.AWS_USER_ATTRIBUTE_USER_TYPE,
          "Value": userType,
        },
      ];

      const validationData = [
        {
          "Name": LibConstants.AWS_USER_ATTRIBUTE_EMAIL,
          "Value": email,
        }
      ]

      const resultCognito = await this.awsUtility.adminCreateUser(password, email, userAttributes, validationData);
      const user = resultCognito?.User;

      if (user) {
        cognitoUserName = user.Username;
      }
    } catch (error) {
      if (error?.name === 'UsernameExistsException') {
        response = { error_message: 'User already exists in cognito!' };
      } else {
        response = { error_message: 'Something went wrong!' };
      }
      BryzosLogger.log(JSON.stringify(error.stack), 'ONBOARD_LOGGLY_LOG_ERROR_TAG');

      return response;
    }
    return cognitoUserName;
  }

  async createWidgetUserData(userData: WidgetUserDto, adminId: string) {
    try{
      let bryzosTermsConditionId = null;
      let acceptedTermsAndCondition = null;
      let cognitoUserName = null;
  
      if (userData?.id) {
        const getTcVersion = await this.dbServiceObj.findOne(this.userOnboradPendingRequestsRepository, "id", userData.id, true);     
        if (getTcVersion && userData.email_id === getTcVersion.email_id) {
          bryzosTermsConditionId = getTcVersion.bryzos_terms_condtion_id;
          acceptedTermsAndCondition = getTcVersion.accepted_terms_and_condition;
          cognitoUserName = getTcVersion.cognito_user_name ?? null;
        }
      } 
      
      const checkEmailValid = await this.dbServiceObj.findOne(this.emailVerificationStatusRepository, "email_id", userData.email_id, true);
      const existingPreApprovedEmail =  await this.dbServiceObj.findOne(this.signupPreApprovedEmailRepository, "email_id", userData.email_id, true);

      if (checkEmailValid && checkEmailValid.status === libConstants.INVALID) {
        await this.dbServiceObj.updateByMultipleWhere({ 'status': libConstants.VALID }, { 'email_id': userData.email_id }, this.emailVerificationStatusRepository);
      }

      if (existingPreApprovedEmail && existingPreApprovedEmail.is_signed_up === 0) {
        await this.dbServiceObj.updateByMultipleWhere({ 'is_signed_up': true }, { 'email_id': userData.email_id }, this.signupPreApprovedEmailRepository);
      }

      let currentDate = new Date();
      const insertUserData: any = {
        email_id: userData.email_id,
        first_name: userData.first_name,
        last_name: userData.last_name,
        is_email_verified: process.env.AWS_COGNITO_EMAIL_VERIFIED === "TRUE",
        type: userData.user_type,
        cognito_user_name: userData.cognito_user_name ?? cognitoUserName,
        client_company: userData.client_company,
        pusher_id: this.uniqid.rnd(13),
        accepted_terms_and_condition: acceptedTermsAndCondition,
        onboarded_app: userData.onboard_source,
        company_id: userData.company_id,
        company_name: userData.company_name,
        created_date: format(utcToZonedTime(currentDate, 'UTC'), 'yyyy-MM-dd HH:mm:ss'),
        is_migrated_to_password: userData?.is_migrated_to_password ?? 2
      }
  
  
      if (userData.user_type === LibConstants.BUYERROLE) {
        await this.signupUtility.getSpreadSettings(insertUserData);
      }
  
      delete (insertUserData.created_date);
      delete (insertUserData.company_name);
      delete (insertUserData.source);
  
      const saveUser = (await this.dbServiceObj.saveData(insertUserData, this.userRepository)).id;
      if (saveUser) {
        if (userData.user_type === LibConstants.BUYERROLE) {
          const adminLog = {
            'admin_id' : adminId,
            'user_id' : saveUser,
            'disc_is_discounted' : insertUserData.disc_is_discounted,
            'disc_discount_percentage' : insertUserData.disc_discount_percentage,
            'disc_discount_rate' : insertUserData.disc_discount_rate,
            'seller_spread_rate' : insertUserData.seller_spread_rate,
            'disc_discount_period' : insertUserData.disc_discount_period,
            'disc_discount_phaseout_startdate' : insertUserData.disc_discount_phaseout_startdate,
            'disc_discount_phaseout_period' : insertUserData.disc_discount_phaseout_period,
            'disc_discount_pricing_column' : insertUserData.disc_discount_pricing_column,
            'disc_is_discount_var_overriden' : insertUserData.disc_is_discount_var_overriden,
            'source' : LibConstants.ONBOARD_TYPE_AD_CREATE_USER,
          }
          
          await this.dbServiceObj.saveData(adminLog,this.adminLogSpreadRepository);
        }
        
        await this.signupUtility.saveUniqueKeyForUser(saveUser);
        const saveUserPreference = await this.signupUtility.saveUserPreference(userData, saveUser);
        if(saveUserPreference?.error_message){
          return saveUserPreference;
        }
  
        if (userData?.id) {
          const orderboardUserid = userData.id;
          const updateOnboardPendingData = {
            is_onboarded: true,
            user_id: saveUser,
            is_active: false,
          }
          const whereColumns = { id: orderboardUserid }
          await this.dbServiceObj.updateByMultipleWhere(updateOnboardPendingData, whereColumns, this.userOnboradPendingRequestsRepository)
        }
  
        if (acceptedTermsAndCondition && bryzosTermsConditionId) {
          const insertTncData = {
            user_id: saveUser,
            bryzos_terms_condtion_id: bryzosTermsConditionId,
            terms_conditions_version: acceptedTermsAndCondition,
          }
          await this.dbServiceObj.saveData(insertTncData, this.widgetTermsCondtionUserActionsRepository)
        }

        // Check if the user has a pending subscription invitation
        const userSubscriptionInviteData = await this.dbServiceObj.findOneByMultipleWhere(this.userSubscriptionEmailInvitationsRepository, {'email_id': userData.email_id, 'status': LibConstants.SUBSCRIPTION_INVITE_STATUS_PENDING} );

        if (userSubscriptionInviteData) {
          // If a pending invitation exists, update its status to 'Accepted'
          await this.dbServiceObj.updateByMultipleWhere({ 'status': LibConstants.SUBSCRIPTION_INVITE_STATUS_ACCEPTED }, { 'id': userSubscriptionInviteData.id }, this.userSubscriptionEmailInvitationsRepository);
        }

      }else{
        return { error_message: 'Something went wrong' };
      }
      return saveUser;
    } catch (error) {
      BryzosLogger.log(JSON.stringify(error.stack), 'ONBOARD_LOGGLY_LOG_ERROR_TAG');
      return { error_message: 'Something went wrong' };
    }
  }

  async savePreapprovedEmails(preApprovedEmailsDto: PreApprovedEmailsDto[], adminId: string) {
    let response = null;
    try {
       
      // Fetch the admin's email based on adminId
      let adminEmail = (await this.dbServiceObj.findOne(this.userRepository, 'id', adminId))?.email_id;

      // Get all pre-approved emails already in the database
      let existingPreApprovedEmails = await this.dbServiceObj.findAll(this.signupPreApprovedEmailRepository);

      // Get all valid cohorts from the reference_data_user_onboarded_app_version table
      let validCohorts = await this.dbServiceObj.findAll(this.referenceDataUserOnboardedAppVersionRepository);

      let emailsToInsert: any[] = [];
      let errors: string[] = [];

      for (let { email, cohort } of preApprovedEmailsDto) {
        const emailExists = existingPreApprovedEmails.some(existingEmail => existingEmail.email_id === email);
        let cohortExists = true;
        if(cohort){
          cohortExists = validCohorts.some(validCohort => validCohort.onboarded_app === cohort);
        }
        if (!cohortExists) {
          // Add cohort-error object to errors array if cohort doesn't exist
          errors.push(`${cohort}`);
        } else if (!emailExists) {
          // Prepare the email data for insertion if cohort exists and email doesn't already exist
          let insertPreApprovedEmail = {
            email_id: email,
            cohort: cohort,
            added_by: adminEmail
          };
          emailsToInsert.push(insertPreApprovedEmail);
        }
      }

      if (emailsToInsert.length > 0) {
        // Insert valid email and cohort data
        await this.dbServiceObj.saveData(emailsToInsert, this.signupPreApprovedEmailRepository);
        let emailWord = emailsToInsert.length === 1 ? "email" : "emails";
        response = `${emailsToInsert.length} ${emailWord} inserted successfully`;
      } else {
        response = 'No new data to insert';
      }

      // If there are errors, append them to the response
      if (errors.length > 0) {
        const errorsString = errors.join(', '); // Convert array to a single string
        response = response ? `${response}. However, some errors occurred. Errors: ` : '';
        response += `Cohort ${errorsString} does not exist!`; // Append the error string directly
        response = {error_message : response};
      }

    } catch (error) {
      BryzosLogger.log(JSON.stringify(error.stack), 'EXTENDED_ADMIN_ERROR_TAG');
      return { error_message: 'Something went wrong' };
    }

    return response;
  }


  async getPreApprovedEmails(options: { page: number, limit: number, search: string, sort: string }){
    let meta;
    let items;

    let conditions = [
      {column: "is_active", operator:"=", value: true},
      { column: ['email_id'], operator: 'LIKE', value: options.search }
    ]

    const orderBy = { 'created_date': 'DESC' };

    let getPreApprovedEmails = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.signupPreApprovedEmailRepository,null,conditions,null,orderBy,null,options);

    meta = getPreApprovedEmails?.meta ?? null 
    items = getPreApprovedEmails.hasOwnProperty('items') ? getPreApprovedEmails.items : getPreApprovedEmails;

    return { items : items,  meta : meta};
  }
  
  async onBoardWidgetUser(userData: WidgetUserDto, adminId: string) {
    let response: any = null

    const isZipCodeValidate = await this.signupUtility.checkZipCodeValidation(userData.zip_code)

    if(isZipCodeValidate.hasOwnProperty('error_message')){ return isZipCodeValidate }
    
    userData.state_id = isZipCodeValidate.state_id;
    
    const companyExists = await this.signupUtility.validateAndSaveCompany(userData.company_name);
    if(companyExists.hasOwnProperty('error_message')){
      return companyExists;
    }
    userData.company_id = companyExists['company_id'];

    const widgetUser = await this.createWidgetUserData(userData, adminId);
    if (widgetUser?.error_message) {
      return widgetUser;
    } else if(widgetUser){
      const referenceId = widgetUser.toString();
      const event = Constants.NEW_USER_SIGNUP;
      let  messageBody = "New User Signup Email";
      this.awsQueue.sendDataToAwsQueue(referenceId, event, messageBody);
      response = 'User Created';
    }
    
    return response;
  }
}
