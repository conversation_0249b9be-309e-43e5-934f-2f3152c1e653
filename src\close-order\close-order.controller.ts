import { Controller, Get, Post, Body, Response, ValidationPipe, UsePipes} from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { Constants } from 'src/Constants';
import { CloseOrderService } from './close-order.service';
import { SaveCloseOrderDto, SaveAchCloseOrderDto } from './dto/close-order.dto';


const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@ApiTags('Close Order')
@Controller('close_order')
export class CloseOrderContoller {
  constructor(private closeOrderService: CloseOrderService) {}

  @Post('order')
  @UsePipes(ValidationPipe)
  async closeOrder(@Body() closeOrderDto:SaveCloseOrderDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = closeOrderDto[payloadTag];
    let responseData = {
      [responseTag]: await this.closeOrderService.closeorderByAdmin(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/buyer_order')
  async getBuyerOpenOrder(@Response() res) {
    let responseData = {
      [responseTag]: await this.closeOrderService.getAllBuyerOpenOrder()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/buyer_close_order')
  @UsePipes(ValidationPipe)
  async closeBuyerOrder(@Body() closeOrderDto:SaveCloseOrderDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = closeOrderDto[payloadTag];
    let responseData = {
      [responseTag]: await this.closeOrderService.closeBuyerorder(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/ach_order')
  async getOpenAchOrdersFromBuyer(@Response() res) {
    let responseData = {
      [responseTag]: await this.closeOrderService.retrieveOpenAchOrdersFromBuyer()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/buyer_close_ach_order')
  @UsePipes(ValidationPipe)
  async closeBuyerAchOrder(@Body() closeAchOrderDto:SaveAchCloseOrderDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = closeAchOrderDto[payloadTag];
    let responseData = {
      [responseTag]: await this.closeOrderService.closeBuyerAchOrder(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}