# README #

This README would normally document whatever steps are necessary to get your application up and running.

### What is this repository for? ###

* Quick summary
This is Node JS Project with nestjs framework
Purpose is to mainatain ref-data and return them on request

### How do I get set up? ###

* Summary of set up - 
> run "npm install"
> you will need 
1. ormconfig.json file in root folder
e.g. -
{
    "type": "mysql",
    "host": "host",
    "port": 3306,
    "username": "user",
    "password": "password",
    "database": "test",
    "entities": ["dist/src/**/entities/*.entity{.ts,.js}"],
    "synchronize": false
  }
2. /config/staging.env and config/develpment.env files
 check example.env file for format
3. add /src/BryzosConfig.ts file
	export class BryzosConfig {
    public static ORIGINAL_DB_CONNECTION = "DB_NAME";
    public static RAYGUN_API = "";
}

* Configuration
* Dependencies
* Database configuration

* How to run tests
> npm run dev (for local env , developemnt.env  file used)
> npm run production (for prod env , production.env file used)

* Deployment instructions
>npm run prebuild
>npm run build:prod
>make zip with below mentioned files and deploy
 1. dist folder
 2. .npmrc file
 3. ormconfig.json file
 4. package.json file
 > add /src/BryzosConfig.ts file
	export class BryzosConfig {
    public static ORIGINAL_DB_CONNECTION = "<db-name>";
    public static RAYGUN_API = "<key>";
}




### Contribution guidelines ###

* Writing tests
* Code review
* Other guidelines

### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact