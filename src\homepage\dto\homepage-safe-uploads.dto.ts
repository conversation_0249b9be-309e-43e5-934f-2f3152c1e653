import { BaseDto } from '@bryzos/base-library';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
    ArrayNotEmpty,
    IsArray,
  IsBoolean,
  IsDefined,
  IsNotEmpty,
  IsNotEmptyObject,
  IsString,
  IsOptional
} from 'class-validator';

export class HideHomePageCarouselDto {
  @ApiProperty() @IsNotEmpty() @IsString() id: string;
  @ApiProperty() @IsNotEmpty() @IsBoolean() value: boolean;
  @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() user_name: string;
  @ApiProperty() @IsOptional() @IsString() caption: string;
  @ApiProperty() @IsOptional() @IsString() company_name: string;
}

export class SaveHideHomePageCarouselDto extends BaseDto {
  @Type(() => HideHomePageCarouselDto)
  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  data: HideHomePageCarouselDto[];
}

export class SafeUploadCommentsDto {
  @ApiProperty() @IsNotEmpty() @IsString() comment_id: string;
  @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() comment: string;
  @ApiProperty() @IsNotEmpty() @IsString() user_name: string;
  @ApiProperty() @IsNotEmpty() @IsBoolean() show_comment: boolean;
}

export class UpdateSafeUploadComments extends BaseDto {
  @Type(() => SafeUploadCommentsDto)
  @ApiProperty() @IsNotEmpty() @IsArray() @ArrayNotEmpty() data: SafeUploadCommentsDto[];
}