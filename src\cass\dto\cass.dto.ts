import { BaseDto } from '@bryzos/base-library';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsBoolean, IsArray, ArrayNotEmpty, ValidateIf, ValidateNested, Min, IsDateString } from 'class-validator';

export class CassDto {
    @ApiProperty() @IsNotEmpty() @IsString() 'po_number': string;
    @ApiProperty() @IsNotEmpty() @IsNumber() 'amount': number;
    @ApiProperty() @IsNotEmpty() @IsString() 'payment_method': string;
    @ApiProperty() @IsString() @IsOptional() 'internal_note': string | null;
    @ApiProperty() @IsNotEmpty() @IsString() 'statement_descriptor': string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsOptional() 'seller_payment_method': string;
    @ApiProperty() @ValidateIf(obj => !obj.cc_email) @ValidateIf(obj => !obj.bcc_email) @IsString() @IsOptional({message:'Please Fill anyone of the field from To Email , CC Email & BCC Email'}) to_email: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() seller_invoice_number: string;
}

export class SellerSetupDto {
    @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
}

export class SaveCassDto extends BaseDto {
    @Type(() => CassDto)
    @ApiProperty() data: CassDto;
}

export class GetSellerSetupDto extends BaseDto {
    @Type(() => SellerSetupDto)
    @ApiProperty()
    data: SellerSetupDto;
}

export class CassTransactionDto {
    @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() amount: number;
    @ApiProperty() @IsNotEmpty() @IsString() statement_descriptor: string;
    @ApiProperty() @IsNotEmpty() @IsString() cass_unique_id: string;
    @ApiProperty() @IsNotEmpty() @IsBoolean() is_cass_default_buyer: boolean;
    @ApiProperty() @IsOptional() @IsString() internal_note: string;
    @ApiProperty() @ValidateIf(obj => !obj.cc_email) @ValidateIf(obj => !obj.bcc_email) @IsString() @IsOptional({message:'Please Fill anyone of the field from To Email , CC Email & BCC Email'}) to_email: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() seller_invoice_number: string;
  }
  
  export class SaveCassTransactionDto extends BaseDto {
    @Type(() => CassTransactionDto)
    @ApiProperty()
    data: CassTransactionDto;
  }

  export class CassAdhocSellerSetupDto {
    @ApiProperty() @IsNotEmpty() @IsString() cass_unique_id: string;
    @ApiProperty() @IsNotEmpty() @IsString() account_number: string;
    @ApiProperty() @IsNotEmpty() @IsString() routing_number: string;
    @ApiProperty() @IsNotEmpty() @IsString() seller_company: string;
    @ApiProperty() @IsNotEmpty() @IsString() truevault_document_id: string;
}
  
  export class SaveAdhocCassSellerSetupDto extends BaseDto {
    @Type(() => CassAdhocSellerSetupDto)
    @ApiProperty()
    data: CassAdhocSellerSetupDto;
  }
export class PoDataDto {
  @ApiProperty() @IsNotEmpty() @IsString() buyer_company_name : string;
  @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
  @ApiProperty() @IsNotEmpty() @IsString() seller_company_name : string;
  @ApiProperty() @IsNotEmpty() @IsNumber() buyer_total_purchase : number;
  @ApiProperty() @IsOptional() @IsString() buyer_name : string;
  @ApiProperty() @IsOptional() @IsString() seller_name : string;
  @ApiProperty() @IsNotEmpty() @IsNumber() total_amount_received: number;
}
export class SelectedOrdersDto {
  @ApiProperty() @IsNotEmpty() @IsBoolean() auto_close_order: boolean;
  @ApiProperty() @IsNotEmpty() @IsString() @IsOptional() user_purchase_order_id: string | null;
  @ApiProperty() @IsNotEmpty() @IsBoolean() is_checked: boolean;
  @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
  @ApiProperty() @IsNotEmpty() @IsNumber() buyer_po_total_purchase: number;
  @ApiProperty() @IsNotEmpty() @IsNumber() @Min(0.01) amount_received_from_buyer: number;
  @ApiProperty() @IsOptional() @ValidateNested({ each: true }) @Type(() => PoDataDto )po_data: PoDataDto;
  @ApiProperty() @IsNotEmpty() @IsBoolean() is_unmapped: boolean;
}
export class MapTransactionToPoDto {
  @ApiProperty() @IsNotEmpty() @IsNumber() total_amount_received_from_buyer: number;
  @ApiProperty() @IsNotEmpty() @IsNumber() processed_amount: number;
  @ApiProperty() @IsNotEmpty() @IsString() id: string;
  @ApiProperty() @IsNotEmpty() @IsArray() @ValidateNested({ each: true }) @Type(() => SelectedOrdersDto) selected_orders: SelectedOrdersDto[];
}
export class SaveMapTransactionToPoDto extends BaseDto {
  @Type(() => MapTransactionToPoDto)
  @ApiProperty() @IsNotEmpty() @IsArray() @ArrayNotEmpty() data: MapTransactionToPoDto[];
}

export class GetTransactionDto {
  @ApiProperty() @IsNotEmpty() @IsDateString() from_date: string; 
  @ApiProperty() @IsNotEmpty() @IsDateString() to_date: string; 
}

export class GetDateRangeTransactionDto {
  @ApiProperty() @IsNotEmpty() @ValidateNested() @Type(() => GetTransactionDto) data: GetTransactionDto;
}


