pipelines:
  custom:
    Deploying-EBS-Application:
      - variables:
          - name: version
          - name: serviceName
            default: prod-extended-widget-admin-dashboard-js
      - step: 
          name: Building Application
          image: node
          script:
            - apt-get update && apt-get install -y zip jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${START}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
            - echo $BRYZOS_CONFIG_PROD > ./src/BryzosConfig.ts
            - echo $ORM_CONFIG_PROD > ormconfig.json
            - npm install
            - npm run prebuild
            - npm run build:prod
            - currentDate=$(date +'%Y%m%d')
            - FILE_NAME="$currentDate-$version-$serviceName"
            - zip -r $FILE_NAME.zip dist .npmrc ormconfig.json package.json .platform
          after-script:
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then
                echo "Build failed"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
              fi
          artifacts:
            - '*.zip'
      - step:
          name: Uploading to S3 Buckets
          image: amazonlinux
          script:
            - yum update -y && yum install awscli jq -y
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_NEW && export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_NEW
            - currentDate=$(date +'%Y%m%d') && FILE_NAME="$currentDate-$version-$serviceName"
            - aws s3 cp $FILE_NAME.zip s3://bryzos-prod-server/prod-extended-widget/$(date +'%Y-%m-%d')/
            - mv $FILE_NAME.zip $serviceName.zip
            - aws s3 cp $serviceName.zip s3://bryzos-prod-server/prod-extended-widget/latest/
          after-script:
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then
                echo "Build failed"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
              fi
      - step:
          name: Updating Elastic Beanstalk Application
          image: amazonlinux
          script:
            - yum update -y && yum install awscli jq -y
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_NEW && export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_NEW
            - aws elasticbeanstalk create-application-version --application-name $APPLICATION_NAME_PROD --version-label $(date +'%Y%m%d')-$version-$serviceName --source-bundle S3Bucket=bryzos-prod-server,S3Key=prod-extended-widget/$(date +'%Y-%m-%d')/$(date +'%Y%m%d')-$version-$serviceName.zip --description "New version" --region us-east-1 --output table --color on
          after-script:
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then
                echo "Build failed"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
              fi
      - step:
          name: Updating Elastic Beanstalk Environment
          image: amazonlinux
          script:
            - yum update -y && yum install awscli jq -y
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_NEW && export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_NEW
            - aws elasticbeanstalk update-environment --environment-name $ENVIRONMENT_NAME_PROD --version-label $(date +'%Y%m%d')-$version-$serviceName --region us-east-1 --output table --color on
          after-script:
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then
                echo "Build failed"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
              fi
      - step:
          name: Checking Deployment
          image: amazonlinux
          script:
            - yum update -y && yum install awscli jq git -y
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_NEW && export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_NEW
            - aws elasticbeanstalk describe-environments --environment-names $ENVIRONMENT_NAME_PROD --query "Environments[*].Status" --region us-east-1 --output text
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - |
              while :
              do
                STATUS=$(aws elasticbeanstalk describe-environments --environment-names $ENVIRONMENT_NAME_PROD --query "Environments[*].Status" --region us-east-1 --output text)
                if [[ "$STATUS" == "Ready" ]]; then
                  echo "Deployment successful!"
                  curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
                  git tag PROD_RELEASE_$(date +'%Y%m%d')-$version $BITBUCKET_COMMIT
                  git push origin PROD_RELEASE_$(date +'%Y%m%d')-$version
                  break
                elif [[ "$STATUS" == "Terminated" ]]; then
                  echo "Deployment failed"
                  curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
                  break
                else 
                  echo "Deployment in progress..."
                  sleep 20
                fi
              done
          after-script:
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then
                echo "Build failed"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    Roll-Back:
      - variables:
          - name: version_label
            description: "Provide Application version name of EBS "
      - step:
          name: Rolling Back To Required Version
          image: amazonlinux
          script:
            - yum update -y && yum install awscli jq -y
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_NEW && export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_NEW
            - aws elasticbeanstalk update-environment --environment-name $ENVIRONMENT_NAME_PROD --version-label $version_label --region us-east-1 --output table --color on
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - |
              while :
              do
                STATUS=$(aws elasticbeanstalk describe-environments --environment-names $ENVIRONMENT_NAME_PROD --query "Environments[*].Status" --region us-east-1 --output text)
                if [[ "$STATUS" == "Ready" ]]; then
                  echo "Roll-Back successful!"
                  curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
                  break
                elif [[ "$STATUS" == "Terminated" ]]; then
                  echo "Roll-Back failed"
                  curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK
                  break
                else 
                  echo "Roll-Back in progress..."
                  sleep 20
                fi
              done
          after-script:
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then
                echo "Build failed"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${PROJECT}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_BE}\"}"  $BUILD_NOTIFICATION_LINK