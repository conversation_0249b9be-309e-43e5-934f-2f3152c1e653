import {
  Controller,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
  HttpCode,
  Response,
} from '@nestjs/common';
import { RemoveSalesTaxService } from './remove-sales-tax.service';
import {
  RemoveSalesTaxDto,
  updateRemoveSalesTaxDto,
} from './dto/remove-sales-tax.dto';
import { Constants } from '../Constants';

const responseTag = Constants.RESPONSE_TAG;
const payloadTag = Constants.PAYLOAD_TAG;

@Controller('remove-sales-tax')
@UsePipes(ValidationPipe)
export class RemoveSalesTaxController {

  constructor(private readonly removeSalesTaxService: RemoveSalesTaxService) { }

  @Post()
  async removeSalesTax(
    @Body() payload: updateRemoveSalesTaxDto,
    @Response() res,
  ) {
    const adminId = res.locals.authorizedUserId;
    const payloadData = payload[payloadTag]
    const responseData = {
      [responseTag]: await this.removeSalesTaxService.removeSalesTax(payloadData, adminId)
    };

    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
