import { <PERSON><PERSON><PERSON>sLog<PERSON>, DataBaseService, ReferenceDataSettings, UserBuyingPreference } from "@bryzos/base-library";
import { Injectable } from "@nestjs/common";
import { Constants } from "../Constants";
import * as uuid4 from 'uuid4';
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { AdminLogBryzosPayCreditLimit, BuyerInBalance, CompanyBuyNowPayLater, ExceptionService, LogBryzosCreditLimit, PaymentInfo } from "@bryzos/extended-widget-library";
import { BnplCreditLimitDto, BnplRequestDto } from "./dto/update-widget-admin-dashboard.dto";
import { AwsQueue } from "../AwsQueue";
import { HttpService } from "@nestjs/axios";

const responseErrorTag = Constants.ERROR_TAG;

@Injectable()
export class CompanyBuyNowPayLaterService {
    private dbServiceObj = new DataBaseService()

    constructor(
        private readonly awsQueue: AwsQueue,
        private readonly httpService : HttpService,

        @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
        @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
        @InjectRepository(LogBryzosCreditLimit) private readonly logBryzosCreditLimitRepository: Repository<LogBryzosCreditLimit>,
        @InjectRepository(AdminLogBryzosPayCreditLimit) private readonly adminLogBryzosPayCreditLimitRepository: Repository<AdminLogBryzosPayCreditLimit>,
        @InjectRepository(PaymentInfo) private readonly paymentInfoRepository: Repository<PaymentInfo>,
        @InjectRepository(BuyerInBalance) private readonly buyerInBalanceRepository: Repository<BuyerInBalance>,
        @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    ) { }

    async saveBnplCreditLimit(payloadData: BnplCreditLimitDto, adminId: number) {
        let reason = null;
        let bnplUpdateResult = null;
        let bryzosCreditAmount;
        let bryzosAvailableCreditLimit;

        const updateDto: any = {
            net_terms_days: payloadData.net_terms_days,
            charges_date: payloadData.charges_date,
            auth_amount_percentage: payloadData.auth_amount_percentage
        };

        const userId = payloadData.user_id;
        reason = payloadData.reason;

        const checkoutViaBalance = (await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSettingsRepository, { 'name': Constants.CHECKOUT_VIA_BALANCE, 'is_active': true }))?.value;

        if (checkoutViaBalance === Constants.OFF) {
            bryzosCreditAmount = payloadData.bryzos_credit_amount;

            const userBnplData = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository, { 'user_id': userId, 'is_active': true });
            if (!userBnplData) {
                return { [responseErrorTag]: "Net Terms 30 settings not found" };
            }

            if (userBnplData?.bryzos_available_credit_limit && userBnplData?.bryzos_credit_limit) {
                bryzosAvailableCreditLimit = bryzosCreditAmount - (Number(userBnplData.bryzos_credit_limit) - Number(userBnplData.bryzos_available_credit_limit));
            } else {
                bryzosAvailableCreditLimit = bryzosCreditAmount;
            }

            const outstandAmount = bryzosCreditAmount - bryzosAvailableCreditLimit;

            updateDto.bryzos_credit_limit = bryzosCreditAmount;
            updateDto.bryzos_available_credit_limit = bryzosAvailableCreditLimit;
            updateDto.bryzos_outstanding_credit_limit = outstandAmount;
        }

        if (Object.keys(updateDto).length) {
            bnplUpdateResult = await this.dbServiceObj.updateByMultipleWhere(updateDto, { user_id: userId, is_active: true }, this.companyBuyNowPayLaterRepository);
        }

        if (checkoutViaBalance == Constants.OFF) {
            this.logBryzosCreditLimit(userId, adminId, reason);
        }
        this.saveBryzosPayAdminLogs(userId);
        if (bnplUpdateResult) {
            return "Successfully Updated";
        }
        return { [responseErrorTag]: 'Nothing to update!' };
    }

    async logBryzosCreditLimit(userId: number, adminId: number, event: string) {
        const bnplData = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository, { 'user_id': userId, 'is_active': true });
        const logData = {
            admin_id: adminId,
            buyer_id: userId,
            credit_limit: bnplData.bryzos_credit_limit,
            available_credit_limit: bnplData.bryzos_available_credit_limit,
            outstanding_credit_limit: bnplData.bryzos_outstanding_credit_limit,
            reason: event
        };
        this.dbServiceObj.saveData(logData, this.logBryzosCreditLimitRepository);
    }

    async saveBryzosPayAdminLogs(userId: number) {
        let event = null;

        if (userId) {
            const bnplData = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository, { user_id: userId, is_active: true });
            if (!bnplData) {
                return;
            }

            if (bnplData.is_approved === 1) {
                event = Constants.APPROVE;
            } else if (bnplData.is_approved === 0) {
                event = Constants.REJECT;
            } else {
                event = Constants.MODIFY;
            }

            const logData: any = {
                user_id: bnplData.user_id,
                is_approved: Boolean(bnplData.is_approved),
                duns: bnplData.duns,
                auth_amount_percentage: bnplData.auth_amount_percentage,
                net_term_days: bnplData.net_terms_days,
                charges_date: bnplData.charges_date,
                is_active: !!bnplData.is_active,
                credit_amount: bnplData.bryzos_credit_limit,
                event: event,
            };
            this.dbServiceObj.saveData(logData, this.adminLogBryzosPayCreditLimitRepository);
        }
    }

    async saveBnplApprovalRequest(payloadData: BnplRequestDto, adminId: number) {
        const netTermsDays = payloadData.net_terms_days;
        const authAmountPercentage = payloadData.auth_amount_percentage;
        const chargesDate = payloadData.charges_date;
        const requestedCreditLimit = payloadData.requested_credit_limit;

        const isApproved = payloadData.is_approved;
        const userId = payloadData.user_id;
  
        const companyBnplData = await this.dbServiceObj.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository, { 'user_id': userId, 'is_active': true });
        if (!companyBnplData) {
            return { [responseErrorTag]: "Sorry user Net Terms 30 data not found!!" };
        }

        if (companyBnplData.is_approved !== null) {
            const status = companyBnplData.is_approved === 1 ? 'approved' : 'rejected';
            return { [responseErrorTag]: `The request has already been "${status}" and cannot be changed.` };
        }
        if (companyBnplData.is_approved !== isApproved) {
            const companyBnplId = companyBnplData.id;
            let id;

            if (isApproved === 1) {
                if (netTermsDays <= 0 || authAmountPercentage <= 0 || chargesDate <= 0 || requestedCreditLimit <= 0) {
                    return { [responseErrorTag]: "Net terms, Auth amount, Charges date are mandatory." };
                }

                id = await this.dbServiceObj.updateByMultipleWhere({ is_approved: !!isApproved }, { user_id: userId, is_active: true }, this.companyBuyNowPayLaterRepository);

                const checkoutViaBalance = (await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSettingsRepository, { name: Constants.CHECKOUT_VIA_BALANCE, is_active: true }))?.value
                if (checkoutViaBalance == Constants.OFF) {
                    this.logBryzosCreditLimit(userId, adminId, Constants.APPROVED_VIA_AD);
                }
                this.saveBryzosPayAdminLogs(userId);
                this.awsQueue.sendBryzosPayApprovalRequestNotification(companyBnplId);
            } else if (isApproved === 0) {
                id = await this.dbServiceObj.updateByMultipleWhere({ is_approved: isApproved }, { user_id: userId, is_active: true }, this.companyBuyNowPayLaterRepository);
                const paymentInfoRejectUpdate = await this.dbServiceObj.updateByMultipleWhere({ is_active: false }, { user_id: userId, is_active: 1, id: companyBnplData.payment_info_id }, this.paymentInfoRepository);
                const buyerInBalanceRejectUpdate = await this.dbServiceObj.updateByMultipleWhere({ is_active: false }, { user_id: userId, is_active: 1 }, this.buyerInBalanceRepository);
                const buyerDefaultPaymentMethodUpdate = await this.dbServiceObj.updateByMultipleWhere({ default_payment_method: null }, { user_id: userId, is_active: 1 }, this.userBuyingPreferenceRepository);
            }

            try {
                this.awsQueue.sendBryzosPayApprovalRequest(companyBnplId);
                const bnplData = (await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.companyBuyNowPayLaterRepository,
                    [{ table: 'user', joinColumn: 'id', mainTableColumn: 'user_id' }],
                    [{ column: 'user_id', operator: '=', value: userId }, { column: 'is_active', operator: '=', value: true }],
                    { selectFields: ['table1.*', 'user.email_id AS email_id'] }))?.[0];

                await this.sendBryzosApprovalStatus(bnplData);
            } catch (error) { 
                ExceptionService.log(error);
            }

            if (id) {
                return "Successfully updated";
            }
        }
    }

    async sendBryzosApprovalStatus(bnplData) {
        let url;
        let headers;
        url = `${process.env.GISS_WS_SERVER}/bryzosPayRequestStatus`;
        headers = {
            'Content-Type': 'application/json',
            'gissToken': process.env.GISS_UI_TOKEN,
        };

        try {
            const response = (await this.httpService.axiosRef.post(url, bnplData, { headers })).data;
            BryzosLogger.log(JSON.stringify({"WS response":response}), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
        } catch (error) {
            BryzosLogger.log(JSON.stringify({"WS error":error}), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
        }
    }
}