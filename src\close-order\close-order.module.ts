import { MiddlewareConsumer, Module } from '@nestjs/common';

import { CloseOrderContoller } from './close-order.controller';
// import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import { AdminPermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';


// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [CloseOrderContoller],
//   providers: OConstants.ServiceArray
// })

@Module({
  imports: [SharedModule],
  controllers: [CloseOrderContoller],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class CloseOrderModule {

  configure(consumer: MiddlewareConsumer) {
      consumer
      .apply(AdminPermissionMiddleware)
      .forRoutes('/close_order');
      consumer
      .apply(LoggerMiddleware)
      .exclude('/close_order/buyer_order','/close_order/ach_order') // exclude get api
      .forRoutes('/close_order');
		}
}