import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SaveDyspatchDto } from './dto/dyspatch.dto';
import { DataBaseService } from '@bryzos/base-library';
import { Repository } from 'typeorm';
import { ReferenceDyspatchTemplates, SecretManagerUtility } from '@bryzos/extended-widget-library';
import { Constants } from 'src/Constants';

@Injectable()
export class DyspatchService {
  private dbServiceObj = new DataBaseService();

  constructor(
    private readonly secretManager : SecretManagerUtility,
    @InjectRepository(ReferenceDyspatchTemplates) private readonly ReferenceDyspatchTemplatesRepository: Repository<ReferenceDyspatchTemplates>,
  ){}

  async findAll(){
    const templates = await this.dbServiceObj.findAll(this.ReferenceDyspatchTemplatesRepository,{created_date:"DESC"});
    let finallData = [];
    let i = 0;
    for(const template of await templates){
      const data = {};
      let subject = '';
      if(template.subject){
        subject = Buffer.from(template.subject, 'base64').toString('utf-8');
      }
      data["id"] = template.id;
      data["template_id"] = template.template_id;
      data["event"] = template.display_event;
      data["subject"] = subject;
      data["time_stamp"] = template.time_stamp;

      finallData[i] = data;
      i++
    }
    return finallData;
  }

  findOne(id: number) {
    return `This action returns a #${id} dyspatch`;
  }

  async updateDyspatch(updateDyspatchDto:SaveDyspatchDto) {
    const template_id = updateDyspatchDto["template_id"];
    const dyspatchApikey = await this.secretManager.getSecretValue(process.env.SM_ENV,Constants.DYSPATCH_VENDOR,Constants.DYSPATCH_KEY);    
    if(dyspatchApikey){
      var axios = require('axios');
      var config = {
        method: 'get',
        maxBodyLength: Infinity,
          url: process.env.DYSPATCH_URL+template_id+'?targetLanguage=liquid',
          headers: { 
            'Authorization': 'Bearer '+dyspatchApikey,
            'Accept': 'application/vnd.dyspatch.2023.03+json'
          },
      };
      try{
        const tempalte_data = await axios(config)
        if(tempalte_data){
          const template_html = tempalte_data.data.compiled.html;
          const template_subject = tempalte_data.data.compiled.subject;
  
          let update_data = {};
          update_data["template_encoded"] = template_html;
          update_data["subject"] = template_subject;
  
          const update_template = await this.dbServiceObj.updateByMultipleWhere(update_data,{"template_id":template_id},this.ReferenceDyspatchTemplatesRepository);
          if(update_template == 1){
            return "Template Updated Successfully";
          }else{
            return {"error_message":"Template Not Updated"};
          }
        }
      }catch{
        return {"error_message":"Template Not Updated"};
      }
    }
  }

}
