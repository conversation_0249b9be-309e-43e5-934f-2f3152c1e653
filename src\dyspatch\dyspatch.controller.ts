import { Controller, Get, Post, Body, Response, ValidationPipe, UsePipes } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { DyspatchService } from './dyspatch.service';
import { SaveDyspatchDto } from './dto/dyspatch.dto';
import { ReferenceDyspatchTemplates } from '@bryzos/extended-widget-library';
import { Constants } from 'src/Constants';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@ApiTags('Dyspatch')
@Controller('dyspatch')
export class DyspatchController {
  constructor(private readonly dyspatchService: DyspatchService) {}

  @Get('/get_templates')
  async findAll(@Response() res) {
    let responseData = {
      [responseTag]: await this.dyspatchService.findAll()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Update Dyspatch Template',
    type: ReferenceDyspatchTemplates,
    status: 200
  })
  @Post('/refresh')
  @UsePipes(ValidationPipe)
  async saveDyspatchTemplateData(@Body() saveDyspatchDto:SaveDyspatchDto, @Response() res){
    let payloadData = saveDyspatchDto[payloadTag];
    let responseData = {
      [responseTag]: await this.dyspatchService.updateDyspatch(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
