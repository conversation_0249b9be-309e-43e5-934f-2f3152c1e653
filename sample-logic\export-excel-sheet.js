#!/usr/bin/env node

// Import required libraries
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);

console.log("export-excel-sheet.js args: ", args, args.length);

// Show usage information if arguments are missing
if (args.length < 5) {
  console.log('Usage: node export-excel-sheet.js <input_excel_path> <sheet_name> <output_excel_filename> <column_mappings_json> <email> <uploadId>',);
  console.log('Example:');
  console.log('  node export-excel-sheet.js ./products.xlsx "tiers_per_state" tiers_per_state.xlsx \'[{"source":"state_abbreviation","target":"state_abbreviation"},{"source":"tiers","target":"tiers"}]\' "<EMAIL>" "upload_123"',);
  process.exit(1);
}


// Extract arguments
const inputFilePath = args[0];
const sheetName = args[1];
const outputFilePath = args[2];
const uploadId = args[4];

// Parse column mappings from JSON
let columnMappings;
try {
  columnMappings = JSON.parse(args[3]);
} catch (error) {
  console.log('Error parsing column mappings JSON:', error);
  process.exit(1);
}

// Extract source column names for lookup in the Excel file
const columnsToExtract = columnMappings.map((mapping) => mapping.source);

// Function to export Excel sheet to a new Excel file using streaming API
async function exportExcelSheetToExcel(
  inputFilePath,
  sheetName,
  outputFilePath,
  columnsToExtract,
  columnMappings,
) {
  try {
    // Check if input file exists
    if (!fs.existsSync(inputFilePath)) {
      console.error(`Error: Input file does not exist at path: ${inputFilePath}`,);
      process.exit(1);
    }

    console.log(`Reading input file: ${inputFilePath}`);
    console.log(`Looking for sheet: ${sheetName}`);
    console.log(`Columns to extract: ${columnsToExtract.join(', ')}`);

    // Display column mappings if any renaming is happening
    const hasRenames = columnMappings.some(
      (mapping) => mapping.source !== mapping.target,
    );
    if (hasRenames) {
      console.log('Column mappings:');
      columnMappings.forEach((mapping) => {
        if (mapping.source !== mapping.target) {
          console.log(`  ${mapping.source} → ${mapping.target}`);
        }
      });
    }

    // Create a workbook reader for input file
    const workbookReader = new ExcelJS.stream.xlsx.WorkbookReader(
      inputFilePath,
      {
        sharedStrings: 'cache',
        hyperlinks: 'ignore',
        worksheets: 'emit',
        formulas: 'shared',
        styles: 'ignore',
      },
    );

    // Create a new workbook for output
    const outputWorkbook = new ExcelJS.Workbook();
    const outputWorksheet = outputWorkbook.addWorksheet(sheetName);

    let foundSheet = false;
    let rowCount = 0;
    let headerIndices = {};
    let outputRowCount = 0;

    // Process each worksheet in the input file
    for await (const worksheetReader of workbookReader) {
      // Check if this is the sheet we're looking for
      if (worksheetReader.name === sheetName) {
        foundSheet = true;
        console.log(`\nFound sheet: ${sheetName}`);

        // Process each row
        for await (const row of worksheetReader) {
          rowCount++;

          // Process header row (first row)
          if (rowCount === 1) {
            // Map column names to indices
            row.values.forEach((value, index) => {
              if (value && columnsToExtract.includes(value)) {
                headerIndices[value] = index;
              }
            });

            // Check if all required columns exist
            const missingColumns = columnsToExtract.filter(
              (col) => !(col in headerIndices),
            );
            if (missingColumns.length > 0) {
              console.log(
                `Error: The following required columns are missing: ${missingColumns.join(
                  ', ',
                )}`,
              );
              console.log(
                'Available columns:',
                row.values.filter(Boolean).join(', '),
              );
              process.exit(1);
            }

            console.log(`Found all required columns. Processing data...`);

            // Add header row to output worksheet with target column names
            const targetColumnNames = columnMappings.map(
              (mapping) => mapping.target,
            );
            outputWorksheet.addRow(targetColumnNames);
            outputRowCount++;

            continue; // Skip to next row
          }

          // Extract values for each column
          const values = columnsToExtract.map((colName) => {
            const cellIndex = headerIndices[colName];
            if (cellIndex === undefined) return null;

            const cellValue = row.values[cellIndex];

            // Handle formula results - get the calculated value
            if (cellValue && typeof cellValue === 'object') {
              if (cellValue.formula) {
                return cellValue.result;
              } else if (cellValue.text) {
                return cellValue.text;
              } else if (cellValue.value !== undefined) {
                return cellValue.value;
              }
            }

            return cellValue;
          });

          // Add row to output worksheet
          outputWorksheet.addRow(values);
          outputRowCount++;

          // Log progress
          if (rowCount % 1000 === 0) {
            console.log(`Processed ${rowCount} rows...`);
          }
        }

        console.log(`\nProcessed ${rowCount} rows from input file.`);
        console.log( `Added ${outputRowCount} rows to output file (including header).`, );

        break; // Exit the worksheet loop once we've found and processed our target sheet
      }
    }

    if (!foundSheet) {
      console.log(`Error: Sheet "${sheetName}" not found in the workbook.`);
      process.exit(1);
    }

    // Create the output directory structure
    const exportDir = path.join( 'uploads', 'pricing', uploadId, 'exports', );

    if (!fs.existsSync(exportDir)) {
      console.log(`Creating directory: ${exportDir}`);
      fs.mkdirSync(exportDir, { recursive: true });
    }

    // Prepare the output path
    const outputFileName = path.basename(outputFilePath);
    const finalOutputPath = path.join(exportDir, outputFileName);

    // Save the output workbook
    console.log(`\nSaving output file to: ${finalOutputPath}`);
    try {
      // Use writeBuffer instead of writeFile to avoid JSZip string length issues
      const buffer = await outputWorkbook.xlsx.writeBuffer({
        useStyles: true,
        useSharedStrings: false, // Disable shared strings to reduce memory usage
      });
      fs.writeFileSync(finalOutputPath, buffer);
      console.log(`Successfully exported data to ${finalOutputPath}`);
    } catch (error) {
      if (error.message && error.message.includes('Invalid string length')) {
        console.log('Error: File too large to process in one operation.');
        console.log(
          'Try splitting the data into smaller chunks or reducing the number of columns.',
        );
        process.exit(1);
      }
      throw error;
    }
  } catch (error) {
    console.log('An error occurred while processing the Excel file:');
    console.log(error.message);
    process.exit(1);
  }
}

// Execute the function
(async () => { await exportExcelSheetToExcel(inputFilePath, sheetName, outputFilePath, columnsToExtract, columnMappings); })();
