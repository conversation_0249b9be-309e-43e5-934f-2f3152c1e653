{"name": "extended-widget-admin-dashboard", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "main": "main.js", "engines": {"node": "18.16.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/src/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "dev": "cross-env NODE_ENV=development nest start --watch", "demo": "cross-env NODE_ENV=demo nest start --watch", "prebuild": "<PERSON><PERSON><PERSON> dist", "build:prod": "set NODE_ENV=production&& nest build"}, "dependencies": {"@aws-sdk/client-elastic-beanstalk": "3.511.0", "@bryzos/base-library": "1.3.71", "@bryzos/extended-widget-library": "1.0.623", "@bryzos/giss-common-lib": "1.1.0", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.0.0", "@nestjs/mapped-types": "2.0.6", "@nestjs/platform-express": "^9.0.0", "@nestjs/swagger": "^6.2.1", "@nestjs/typeorm": "^9.0.1", "async-lock": "1.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "csv-stringify": "^6.4.0", "date-fns": "2.29.3", "date-fns-tz": "2.0.0", "dyspatch-client": "^6.0.0", "exceljs": "4.4.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "multer": "^1.4.5-lts.1", "mysql2": "^3.1.2", "nestjs-typeorm-paginate": "4.0.4", "node-loggly-bulk": "^3.0.1", "raygun": "^0.13.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "short-unique-id": "5.0.3", "ssh2-sftp-client": "^9.1.0", "taxjar": "^4.0.1", "typeorm": "^0.3.12", "uuid4": "^2.0.3", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "29.2.4", "@types/multer": "1.4.12", "@types/node": "18.11.18", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "cross-env": "7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.3.1", "prettier": "^2.3.2", "run-script-webpack-plugin": "^0.1.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.1", "typescript": "^4.7.4", "webpack": "^5.75.0", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}