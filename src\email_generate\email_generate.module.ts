import { MiddlewareConsumer,Module } from '@nestjs/common';
import { EmailGenerateController } from './email_generate.controller';
// import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import { AdminPermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule, DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [EmailGenerateController],
//   providers: OConstants.ServiceArray
// })

@Module({
  imports: [SharedModule],
  controllers: [EmailGenerateController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class EmailGenerateModule {
  configure(consumer: MiddlewareConsumer) {
		consumer
		  .apply(AdminPermissionMiddleware, LoggerMiddleware)
		  .forRoutes('/email-generate');
		}
}
