import { Module, MiddlewareConsumer } from '@nestjs/common';
import { UserController } from './user.controller';
// import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import { AdminPermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,HttpModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [UserController],
//   providers: OConstants.ServiceArray
// })

@Module({
  imports: [SharedModule],
  controllers: [UserController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class UserModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
    .apply(AdminPermissionMiddleware)
    .forRoutes('/user');
    consumer
    .apply(LoggerMiddleware)
    .exclude('/user/getPendingOnBoardUserRequest', '/user/getOnboardCompaniesRequests', '/user/list', '/user/getDiscountedUsers') // exclude get api
    .forRoutes('/user');
	}
}
