import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { configuration } from 'config/configuration';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { WidgetAdminDashboardModule } from './widget-admin-dashboard/widget-admin-dashboard.module';
import { CashInAdvanceDepositModule } from './cash-in-advance-deposit/cash-in-advance-deposit.module';
import { getConnectionOptions } from 'typeorm';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CancelOrderModule } from './cancel-order/cancel-order.module';
import { DyspatchModule } from './dyspatch/dyspatch.module';
import { CloseOrderModule } from './close-order/close-order.module';
import { UserModule } from './user/user.module';
import { CassModule } from './cass/cass.module';

import { EmailGenerateModule } from './email_generate/email_generate.module';
import { RemoveSalesTaxModule } from './remove-sales-tax/remove-sales-tax.module';
import { OrderModule } from './order/order.module';
import { ExceptionService } from '@bryzos/extended-widget-library';

@Module({
		
		imports: [ConfigModule.forRoot({ envFilePath: `${process.cwd()}/config/${process.env.NODE_ENV}.env`, isGlobal: true, load: [configuration] }),TypeOrmModule.forRootAsync({
		useFactory: async () =>
		Object.assign(await getConnectionOptions(), {
			autoLoadEntities: true,
		}),
	}), WidgetAdminDashboardModule, CashInAdvanceDepositModule, CancelOrderModule, DyspatchModule, CloseOrderModule, EmailGenerateModule, UserModule,
		RemoveSalesTaxModule, CassModule, OrderModule
	],

  controllers: [AppController],
  providers: [AppService, ExceptionService],
})

export class AppModule {}