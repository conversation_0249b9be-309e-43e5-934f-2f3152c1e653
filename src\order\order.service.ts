import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseLibraryService, Constants as LibConstants, DataBaseService } from '@bryzos/base-library';
import { CompanyBuyNowPayLater, CustomDepositDetails, GlobalDepositDetails, LogAuthAmount, LogBryzosCreditLimit, OrderLineSnapshotPricing, ReferenceDataOrderStatus, ReferenceDataProductsWidget, ReferenceDataSalesTax, ReferenceDataSellerMinPricing, ReferenceDataSettings, SellerPriceUpdateCalculationsLog, TaxExemptedPurchaseOrders, User, UserAchCredit, UserOnboradPendingRequests, UserOrderDepoistLogs, UserProductTagMapping, UserPurchaseOrder, UserPurchaseOrderLedger, UserPurchaseOrderLine, UserResaleCertificate, AdminLogUpdateOrderLineDetails, BryzosLogger, OrderUtilityLibrary } from '@bryzos/extended-widget-library';
import { HttpService } from '@nestjs/axios';
import { AddNewLinesDto, SalesTaxOrderDto, UpdateLinesDto } from './dto/order.dto';
import { Constants } from 'src/Constants';
import { Utils } from 'src/utils';

interface DepositDataInterface {
    deposit_percentage: number;
    deposit_amount: number;

}

@Injectable()
export class OrderService {
    private dbServiceObj = new DataBaseService()

    constructor(
        private readonly utils: Utils,
        private readonly libraryOrderUtility : OrderUtilityLibrary,
        @InjectRepository(User) private readonly userRepository: Repository<User>,
        @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
        @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
        @InjectRepository(UserResaleCertificate) private readonly userResaleCertificateRepository: Repository<UserResaleCertificate>,
        @InjectRepository(ReferenceDataOrderStatus) private readonly referenceDataOrderStatusRepository: Repository<ReferenceDataOrderStatus>,
        @InjectRepository(ReferenceDataProductsWidget) private readonly referenceDataProductsWidgetRepository: Repository<ReferenceDataProductsWidget>,
        @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
        @InjectRepository(UserAchCredit) private readonly userAchCreditRepository: Repository<UserAchCredit>,
        @InjectRepository(TaxExemptedPurchaseOrders) private readonly taxExemptedPurchaseOrdersRepository: Repository<TaxExemptedPurchaseOrders>,
        @InjectRepository(UserPurchaseOrderLedger) private readonly userPurchaseOrderLedgerRepository: Repository<UserPurchaseOrderLedger>,
        @InjectRepository(LogBryzosCreditLimit) private readonly logBryzosCreditLimitRepository: Repository<LogBryzosCreditLimit>,
        @InjectRepository(UserProductTagMapping) private readonly userProductTagMappingRepository: Repository<UserProductTagMapping>,
        @InjectRepository(OrderLineSnapshotPricing) private readonly orderLineSnapshotPricingRepository: Repository<OrderLineSnapshotPricing>,
        @Inject(BaseLibraryService) private readonly baseLibraryService: BaseLibraryService,
        @InjectRepository(AdminLogUpdateOrderLineDetails) private readonly adminLogUpdateOrderLineDetailsRepository: Repository<AdminLogUpdateOrderLineDetails>,

    ) { }

    async addNewOrderLines(addNewLinesDto: AddNewLinesDto, adminId:string) {
        let response: any = {}
        const purchaseOrderData = await this.getPurchaseOrderDataById(addNewLinesDto.purchase_order_id);
        if(!purchaseOrderData){
            response.error_message = "Order is already closed or not available"
            return response;
        }
        const purchaseOrderLineData = await this.getUserPurchaseOrderLineData(addNewLinesDto.purchase_order_id);
        const validateData  = await this.validateAddNewLinesData(addNewLinesDto, purchaseOrderLineData);
        if(validateData?.line_errors || validateData?.qty_errors || validateData?.unit_errors){
            response.error_message =  validateData.line_errors + ' ' + validateData.qty_errors + ' ' + validateData.unit_errors;
            return response;
        }
        const updatedFieldData = await this.getCheckoutOrderPrices(addNewLinesDto, purchaseOrderData);

        if (!updatedFieldData || updatedFieldData?.error_message || updatedFieldData?.price == 0 || updatedFieldData?.seller_price == 0) {
            response['error_message'] = updatedFieldData.error_message ?? "Something went wrong!";
            return response;
        }

        if (Object.keys(updatedFieldData.cart_items)?.length > 0) {
            const activeOrderLines = purchaseOrderLineData.filter(obj => obj.is_active);
            const updatedPurchaseOrderData = await this.updatePoData(updatedFieldData, purchaseOrderData, activeOrderLines, adminId);
            if (!updatedPurchaseOrderData || updatedPurchaseOrderData?.error_message) {
                response['error_message'] = updatedPurchaseOrderData.error_message ?? "Something went wrong!!";
                return response;
            }
            if (updatedPurchaseOrderData?.order_id && updatedPurchaseOrderData.buyer_po_difference) {
                if (updatedFieldData.payment_method === LibConstants.PAYMENT_METHOD_BRYZOS_PAY) {
                    this.updateBuyerCredits(purchaseOrderData.buyer_id, updatedPurchaseOrderData.buyer_po_difference, purchaseOrderData.buyer_po_number, 'ADD_NEW_LINES')
                }
            }
        }

        if(purchaseOrderData && purchaseOrderData.seller_id === null){
            await this.sendWebsocketEvent(purchaseOrderData.buyer_po_number);
        }

        response = "Data updated successfully";

        return response;
    }

    async getPurchaseOrderDataById(userPurchaseOrderId: string) {
        return await this.dbServiceObj.findOne(this.userPurchaseOrderRepository, 'id', userPurchaseOrderId);
    }

    async getUserPurchaseOrderLineData(userPurchaseOrderId: string) {
        return await this.dbServiceObj.findMany(this.userPurchaseOrderLineRepository, 'purchase_order_id', userPurchaseOrderId, '=', false);
    }

    async validateAddNewLinesData(addNewLinesDto: AddNewLinesDto, purchaseOrderLineData:any) {
        const response = {
            line_errors: "",
            unit_errors : "",
            qty_errors : ""
        };
        for(const data of addNewLinesDto.cart_items){
            const alreadyAvailableLineId = purchaseOrderLineData.find(item => item.line_id === data.line_id);
            if(alreadyAvailableLineId){
                response.line_errors = data.line_id + ",";
            }
            const productData = await this.dbServiceObj.findOne(this.referenceDataProductsWidgetRepository, 'Product_ID', String(data.product_id));
            const qtyUnitAvailable = productData.QUM_Dropdown_Options.split(',').find((qumDropdown) => qumDropdown.toLowerCase() === data.qty_unit.toLowerCase())
            const priceUnitAvailable = productData.PUM_Dropdown_Options.split(',').find((pumDropdown) => pumDropdown.toLowerCase() === data.price_unit.toLowerCase())
            if (!qtyUnitAvailable || !priceUnitAvailable) {
                response.unit_errors += data.line_id + ",";
            }
            
            let qtyUnit = this.getUnitPostScript(data.qty_unit)

            // const orderIncrementUnit = "Order_Increment_" + qtyUnit;
            // const validateQty = this.getFloatRemainder(data.qty, productData[orderIncrementUnit]);
            // if (validateQty === false) {
            //     response.qty_errors += data.line_id + ", ";
            // }
            
        }
        if(response.line_errors || response.qty_errors || response.unit_errors ){
            if (response.line_errors.trim() != "") {
                if (response.line_errors.includes(",")) {
                    response.line_errors = response.line_errors.replace(",", ' and')
                }
                response.line_errors = "This po line already exist " + response.line_errors;
            }

            if (response.unit_errors.trim() != "") {
                if (response.unit_errors.includes(",")) {
                    response.unit_errors = response.unit_errors.replace(",", ' and')
                }
                response.unit_errors = "There is an incorrect quantity unit or price unit entered for the respective products on line " + response.unit_errors;
            }
    
            // if (response.qty_errors.trim() != "") {
            //     if (response.qty_errors.includes(",")) {
            //         response.qty_errors = response.qty_errors.replace(",", ' and')
            //     }
            //     response.qty_errors = "There is an incorrect quantity entered for the respective products on line " + response.qty_errors;
            // }
            return response;
        }
        return response;
    }

    getUnitPostScript(unit: string) {
        if (unit.toLowerCase() === 'cwt') {
            return "CWT"
        } else if (unit.toLowerCase() === 'ea') {
            return "Ea"
        } else if (unit.toLowerCase() === 'ft') {
            return "Ft"
        } else if (unit.toLowerCase() === 'lb') {
            return "Lb"
        } else if (unit.replace('_', " ").toLowerCase() === 'net ton') {
            return "Net_Ton"
        }
    }

    getFloatRemainder(dividend: string | number | null, divisor: number) {
        if (dividend && +divisor) {
            dividend = +dividend;
            divisor = +divisor;

            const dividendDecimalCount = (dividend.toString().split('.')[1] ?? '').length;
            const divisorDecimalCount = (divisor.toString().split('.')[1] ?? '').length;
            const decimalCount = dividendDecimalCount > divisorDecimalCount ? dividendDecimalCount : divisorDecimalCount;

            const dividendInt = parseInt(dividend.toFixed(decimalCount).replace('.', ''));
            const divisorInt = parseInt(divisor.toFixed(decimalCount).replace('.', ''));

            const reminder = (dividendInt % divisorInt) / Math.pow(10, decimalCount);
            if (reminder === 0) {
                return true;
            }
        }
        return false;
    }

    async getCheckoutOrderPrices(addNewLinesDto: AddNewLinesDto , purchaseOrderData: any) {
        let response: any = {};
        const userData = await this.dbServiceObj.findOne(this.userRepository, 'id', purchaseOrderData.buyer_id);
        if(!userData){
            response.error_message = "Buyer User is not present or deactivate";
            return response;
        }
        let data: any = {};

        const cartItems = addNewLinesDto.cart_items;

        const productIdList = cartItems.map((item) => item.product_id)
        const referenceProductData = await this.dbServiceObj.findManyByWhereIn(this.referenceDataProductsWidgetRepository, 'Product_ID', productIdList)

        let totalBuyerPrice = 0;
        let totalSellerPrice = 0;
        const unitConversions= {
            'Lb' : 'LB',
            'Net Ton' : 'Net_Ton',
        }

        cartItems.forEach((cartItem) => {
            const productData = referenceProductData.find((referenceProduct) => referenceProduct.Product_ID === cartItem.product_id)
            
            let qtyUnit = this.getUnitPostScript(cartItem.qty_unit)
            let priceUnit = this.getUnitPostScript(cartItem.price_unit)
            let qty = cartItem.qty;
            
            let buyerCalculationPriceColumn: string | number = 'Buyer_Pricing_';
            let buyerPriceColumn: string | number = 'Buyer_Pricing_';

            qtyUnit = unitConversions?.[qtyUnit] ? unitConversions[qtyUnit] : qtyUnit;
            priceUnit = unitConversions?.[priceUnit] ? unitConversions[priceUnit] : priceUnit;

            const sellerCalculationPriceColumn = 'Seller_Pricing_' + qtyUnit ?? 0;
            let sellerCalculationPrice = productData[sellerCalculationPriceColumn] ?? 0;
            
            const sellerPriceColumn = 'Seller_Pricing_' + priceUnit ?? 0;
            const sellerPrice = productData[sellerPriceColumn] ?? 0;
            
            const formattedSellerPrice = this.sanatizeReferenceDataProductsAmount(sellerPrice);
            sellerCalculationPrice = this.sanatizeReferenceDataProductsAmount(sellerCalculationPrice);
            
            let sellerExtended: number = sellerCalculationPrice * qty;
            sellerExtended = Number(sellerExtended.toFixed(2));
            
            cartItem.seller_price = formattedSellerPrice;
            cartItem.seller_extended = Number(sellerExtended);
            cartItem["seller_calculation_price"] = sellerCalculationPrice;
            totalSellerPrice = +(totalSellerPrice + +sellerExtended).toFixed(2);
            
            let price: number = cartItem.buyer_price;
            let buyerExtended = cartItem.buyer_extended

            buyerCalculationPriceColumn = buyerCalculationPriceColumn + qtyUnit ?? 0;
            let buyerCalculationPrice = productData[buyerCalculationPriceColumn] ?? 0;
            buyerPriceColumn = buyerPriceColumn + priceUnit ?? 0;
            const buyerPrice = productData[buyerPriceColumn] ?? 0;

            let formattedBuyerPrice = this.sanatizeReferenceDataProductsAmount(buyerPrice);
            buyerCalculationPrice = this.sanatizeReferenceDataProductsAmount(buyerCalculationPrice);
            price = formattedBuyerPrice;

            buyerExtended = buyerCalculationPrice * qty;
            buyerExtended = +buyerExtended.toFixed(2);

            const buyerPricingLb = this.sanatizeReferenceDataProductsAmount(productData.Buyer_Pricing_LB);

            const shape = productData.Key2;
            const description = productData.UI_Description;
            const referenceProductId = productData.ID;

            cartItem["buyer_pricing_lb"] = buyerPricingLb;
            cartItem["buyer_calculation_price"] = buyerCalculationPrice;
            cartItem["shape"] = shape;
            cartItem["description"] = description;
            cartItem["reference_product_id"] = referenceProductId;
            cartItem["reference_product_data"] = productData;

            cartItem.buyer_price = priceUnit && priceUnit == 'LB' ? +(+price.toFixed(4)) : +(+price.toFixed(2));

            cartItem.buyer_extended = buyerExtended;
            totalBuyerPrice += +buyerExtended;
        })

        data.cart_items = cartItems;

        // update the total prices
        data.price = totalBuyerPrice;
        data.seller_price = totalSellerPrice;
        data.payment_method = purchaseOrderData.payment_method;
        data.buyer_id = purchaseOrderData.buyer_id;

        data.user_spread_data = userData;
        response = data;

        return response;
    }

    sanatizeReferenceDataProductsAmount(price: string) {
        return Number(price.trim().replace(/[\$,]/g, ''))
    }
    
    async validateUserPaymentData(purchaseData: any, orderPrice: number) {
        let response: any = {};
        if (purchaseData.payment_method === LibConstants.PAYMENT_METHOD_BRYZOS_PAY) {
            const getBnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository, 'user_id', purchaseData.buyer_id)

            if (Object.keys(getBnplData)?.length > 0 && getBnplData.is_approved) {
                if (getBnplData.bryzos_available_credit_limit < orderPrice) {
                    response.error_message = "Sorry, buyer has exceeded purchase limit.";
                    return response;
                }
            } else if (Object.keys(getBnplData)?.length > 0 && getBnplData.is_approved != true) {
                response.error_message = "The buyer's request for Net 30 terms has not yet been approved.";
                return response;
            } else {
                response.error_message = "The buyer has not completed the payment account setup.";
                return response;
            }
        } else if (purchaseData.payment_method === LibConstants.PAYMENT_METHOD_ACH_CREDIT) {
            const userData = await this.dbServiceObj.findOne(this.userAchCreditRepository, 'user_id', purchaseData.buyer_id);
            if (!userData || !userData.is_approved) {
                response.error_message = "The buyer has not completed the payment account setup.";
                response.mobile_error_message = "The buyer has not completed the payment account setup.";
                return response;
            }
            return response;
        }
    }

    async updatePoData(updatedFieldData:any, purchaseOrderData:any, orderLineData:any, adminId:string) {
        let response: any = {};

        try {
            let poNumberSequence = null;
            const createPurchaseOrderData = updatedFieldData;
            if (+createPurchaseOrderData.seller_price === 0) {
                response = null;
                return response;
            }
            let salesTaxRate = 0;
            let totalSalesTax = 0;
            let poTotalSalesTax = 0;
            let poDiff = 0;
            let closedLineSalesTax = 0;

            const existingLines = orderLineData;

            if (createPurchaseOrderData?.cart_items) {
                const createOrderLineData = [...orderLineData, ...createPurchaseOrderData.cart_items];
                if (createOrderLineData.length > 0) {
                    let sellerFid = null;
                    if (createPurchaseOrderData.payment_method === LibConstants.PAYMENT_METHOD_BRYZOS_PAY) {
                        sellerFid = orderLineData[0].seller_funding_date;
                    }

                    const resaleCertificateConditions = {
                        "state_id": purchaseOrderData.state_id,
                        "user_id": purchaseOrderData.buyer_id,
                        "status": LibConstants.SALES_TAX_EXEMPTION_APPROVED
                    }
            
                    const resaleCertData = await this.dbServiceObj.findOneByMultipleWhere(this.userResaleCertificateRepository, resaleCertificateConditions);

                    // get sales tax state rate
                    const salesTaxData = await this.baseLibraryService.calculateSalesTaxRate(purchaseOrderData.state_id, purchaseOrderData.zip, resaleCertData, this.userPurchaseOrderRepository);
                    if (salesTaxData.sales_tax_rate) {
                        salesTaxRate = salesTaxData['sales_tax_rate'];
                    }

                    let addLinesPrice = +createPurchaseOrderData.price;
                    totalSalesTax = +(createPurchaseOrderData.price * salesTaxRate).toFixed(2);
                    const olderPrice = purchaseOrderData?.actual_buyer_po_price ?? purchaseOrderData.buyer_po_price;
                    const depositAmount =  purchaseOrderData?.deposit_amount != null ? +purchaseOrderData.deposit_amount : 0
                    const olderBuyerPoPrice = +olderPrice + +purchaseOrderData.sales_tax + depositAmount;
                    createPurchaseOrderData.price = String(+createPurchaseOrderData.price + +olderPrice);
                    const olderSellerPrice = purchaseOrderData?.actual_seller_po_price ?? purchaseOrderData.seller_po_price;
                    createPurchaseOrderData.seller_price = String(+createPurchaseOrderData.seller_price + +olderSellerPrice);

                    createPurchaseOrderData.total_buyer_po_price = (+createPurchaseOrderData.price + +purchaseOrderData.sales_tax + +totalSalesTax);
                    
                    let oldSalesTax = 0;
                    let openLineSalesTaxToTal = 0;
                    for(const existingLine of existingLines){
                        if(existingLine?.is_buyer_order_open == false){ continue; };
                        openLineSalesTaxToTal = +openLineSalesTaxToTal + +existingLine.sales_tax;
                        let salesTaxOfExistingLine = existingLine.actual_buyer_line_total * +salesTaxRate;
                        salesTaxOfExistingLine = +salesTaxOfExistingLine.toFixed(2);
                        oldSalesTax = oldSalesTax + salesTaxOfExistingLine;
                    }

                    let addSalesTax = 0;

                    if(+openLineSalesTaxToTal === 0 && +oldSalesTax > 0){
                        addSalesTax = +oldSalesTax;
                    }else if(+openLineSalesTaxToTal > 0 && +oldSalesTax === 0){
                        addSalesTax = +openLineSalesTaxToTal * -1;
                    }

                    poDiff = Math.abs((totalSalesTax + addLinesPrice + addSalesTax ));

                    createPurchaseOrderData.payment_method = purchaseOrderData.payment_method;
                    createPurchaseOrderData.buyer_id = purchaseOrderData.buyer_id;

                    if(poDiff === 0){ return { error_message : "Something went wrong!!" } }
      
                    const verifyUserPaymentData = await this.validateUserPaymentData(purchaseOrderData, poDiff)

                    if (verifyUserPaymentData?.error_message) {
                        response['error_message'] = verifyUserPaymentData.error_message;
                        return response;
                    }

                    let newSellerPoPrice = 0;
                    let totalWeight = 0;
                    let poLinesArr = [];
                    let updateExistingLineArray = [];
                    let logArrayofUpdateExistingLine = [];
                    let ledgerArrayofUpdateExistingLine = [];
                    for (const lineData of createOrderLineData) {
                        if (lineData?.po_number_sequence) {
                            poNumberSequence = lineData.po_number_sequence;
                            purchaseOrderData.poNumberSequence = poNumberSequence;
                        }
                        
                        const calculateWeight = lineData?.actual_total_weight ? +lineData.actual_total_weight : this.getLineWeight(lineData)
                        totalWeight += calculateWeight;
                        lineData.purchase_order_id = purchaseOrderData.id;
                        lineData.total_line_weight = calculateWeight;
                        lineData.seller_funding_date = sellerFid;
                        lineData.seller_extended = lineData?.actual_seller_line_total ?? lineData.seller_extended;
                        lineData.seller_calculation_price_per_unit = lineData?.seller_calculation_price_per_unit  ?? lineData.seller_calculation_price;
                        newSellerPoPrice += +lineData.seller_extended;

                        const buyerExtended = lineData?.actual_buyer_line_total ?? lineData.buyer_extended;

                        let salesTax = 0;
                        if(lineData?.is_buyer_order_open == true || !lineData?.po_line){
                        const unformattedSalesTax = salesTaxRate * buyerExtended;
                        salesTax = +unformattedSalesTax.toFixed(2);
                        }else if(lineData?.is_buyer_order_open == false){
                            closedLineSalesTax = +closedLineSalesTax + +lineData?.sales_tax;
                            continue;
                        }
                        poTotalSalesTax = poTotalSalesTax + salesTax;
                        lineData.newSalesTax = Number(totalSalesTax) === 0 ? 0 : salesTax ; 

                        if(!lineData?.po_line){
                            lineData["sales_tax"] = salesTax;
                            const poInsert = await this.savePurchaseOrderLine(lineData, purchaseOrderData, adminId) ;
                            if(!poInsert){
                                response.error_message = "Error in Order Line Creation";
                                return response;
                            }
                            if (poInsert) {
                                poLinesArr.push(poInsert);
                            }
                         
                            if (!lineData?.buyer_po_number) {
                                const snapshoPricing = await this.productPricingSnapshot(lineData, createPurchaseOrderData, purchaseOrderData.buyer_po_number);
                                if (!snapshoPricing) {
                                    response.error_message = "Something went Wrong!!";
                                    return response;
                                }
                            }
                        }else if(lineData?.id && lineData?.po_line && Number(lineData.sales_tax) != Number(lineData.newSalesTax)){
                            const newSalesTax = Number(lineData.newSalesTax);
                            const oldSalesTax = Number(lineData.sales_tax);

                            updateExistingLineArray.push({
                                id : lineData.id,
                                sales_tax : newSalesTax
                            });

                            logArrayofUpdateExistingLine.push({
                                adminId : adminId,
                                buyer_id : purchaseOrderData.buyer_id,
                                po_number : purchaseOrderData.buyer_po_number,
                                po_line : lineData.po_line,
                                purchase_order_line_id : lineData.id,
                                old_order_line_sales_tax : oldSalesTax,
                                updated_order_line_sales_tax : newSalesTax,
                            });
    
                            let ledgerSalesTax : number;
                            if(oldSalesTax > newSalesTax){
                                ledgerSalesTax = (oldSalesTax - newSalesTax) * -1;
                            }else{
                                ledgerSalesTax = newSalesTax - oldSalesTax;
                            }

                            ledgerArrayofUpdateExistingLine.push({
                                user_id : purchaseOrderData.buyer_id,
                                purchase_order_line_id : lineData.id,
                                transaction_type : Constants.ADD_NEW_LINES_CREATED,
                                sales_tax : ledgerSalesTax
                            })
                        }
                        
                    }

                    if (poTotalSalesTax === 0 && salesTaxData.annual_transaction_amount && salesTaxData.annual_transaction) {
                        let userResaleCertificateUrl= null;
                        let userResaleCertificateId= null;

                        if (salesTaxData.is_threshold_met_cert_approved) {
                            if (resaleCertData && resaleCertData.cerificate_url_s3) {
                                userResaleCertificateUrl = resaleCertData.cerificate_url_s3;
                                userResaleCertificateId = resaleCertData.id;
                            }
                        }

                        const logTaxExemptData = {
                            'annual_transaction_amount': salesTaxData.annual_transaction_amount,
                            'annual_transaction': salesTaxData.annual_transaction,
                            'user_resale_certificate_url': userResaleCertificateUrl,
                            'user_resale_certificate_id': userResaleCertificateId,
                            'economic_nexus_threshold_number_transactions': salesTaxData.ref_economic_nexus_threshold_number_transactions,
                            'economic_nexus_threshold': salesTaxData.ref_economic_nexus_threshold,
                            'combined_rate_number': salesTaxData.ref_combined_rate_number
                        };

                        this.saveTaxExemptedPurchaseOrders(purchaseOrderData.buyer_po_number, poLinesArr, logTaxExemptData)
                    }
                    createPurchaseOrderData.seller_price = newSellerPoPrice;
                    createPurchaseOrderData.total_weight = totalWeight;
                    createPurchaseOrderData.id = purchaseOrderData.id;
                    createPurchaseOrderData.total_buyer_sales_tax = +poTotalSalesTax + +closedLineSalesTax;

                    this.dbServiceObj.saveData(updateExistingLineArray,this.userPurchaseOrderLineRepository);
                    this.dbServiceObj.saveData(ledgerArrayofUpdateExistingLine,this.userPurchaseOrderLedgerRepository);
                    this.dbServiceObj.saveData(logArrayofUpdateExistingLine,this.adminLogUpdateOrderLineDetailsRepository);

                    this.updateUserPurchaseData(createPurchaseOrderData)
                }

                response.order_id = purchaseOrderData.id;
                response.po_number = purchaseOrderData.buyer_po_number;
                response.buyer_po_difference = poDiff ;
                return response;
            }
        } catch (error) {
            console.log(error);
            response.error_message = "Something went Wrong!!";
            return response;
        }
    }

    getLineWeight(orderLine) {
        let totalPounds = 0;
        if (orderLine?.buyer_pricing_lb !== 0) {
            const buyerExtended = orderLine.buyer_extended;
            const buyerPricingLb = orderLine.buyer_pricing_lb;
            totalPounds = buyerExtended / buyerPricingLb;
        }
        return totalPounds;
    }

    async savePurchaseOrderLine(lineData:any, purchaseOrderData:any, adminId:string) {
        try {
            const poLineInsertData = {
                po_line: null,
                buyer_po_number: purchaseOrderData.buyer_po_number,
                buyer_id: purchaseOrderData.buyer_id,
                seller_id: purchaseOrderData.seller_id,
                purchase_order_id: lineData.purchase_order_id,
                line_id: lineData.line_id,
                description: lineData.description,
                qty: lineData.qty,
                qty_unit: lineData.qty_unit,
                buyer_price_per_unit: lineData?.buyer_price,
                price_unit: lineData.price_unit,
                buyer_line_total: lineData?.buyer_extended,
                product_id: lineData.product_id,
                reference_product_id: lineData?.reference_product_id ?? null,
                sales_tax: lineData.sales_tax,
                shape: lineData.shape,
                seller_calculation_price_per_unit: lineData.seller_calculation_price_per_unit,
                buyer_calculation_price_per_unit: lineData.buyer_calculation_price,
                seller_price_per_unit: lineData.seller_price,
                seller_line_total: lineData.seller_extended,
                actual_qty: lineData.qty,
                actual_buyer_line_total: lineData?.buyer_extended,
                actual_seller_line_total: lineData.seller_extended,
                domestic_material_only: lineData?.domestic_material_only ?? false,
                total_weight: lineData.total_line_weight,
                actual_total_weight: lineData.total_line_weight,
                product_tag: lineData?.product_tag ?? null,
                seller_funding_date: lineData.seller_funding_date,
                buyer_pricing_lb: lineData.buyer_pricing_lb,
                order_status_id: lineData.order_status_id ?? null,
                quote_number: lineData.quote_number ?? null,
                po_number_sequence: lineData.po_number_sequence ?? null,    
            };
            let existingPoLine = null;
            let buyerPricePerUnit = null;
            let buyerPurchase = null;
            let escrow = null;
            let depositAmount = null;
    

            const selectCustomFields = ['id', ' MAX(po_line) as po_line'];
            existingPoLine = await this.dbServiceObj.selectCustomFields(this.userPurchaseOrderLineRepository, 'purchase_order_id', lineData.purchase_order_id, selectCustomFields,false);
            poLineInsertData.po_line = existingPoLine[0].po_line + 1;
            poLineInsertData.quote_number = poLineInsertData.buyer_po_number.replace(LibConstants.BUYER_PURCHASE_ORDER_INITIAL, LibConstants.QUOTE_NUMBER_INITIAL) + '-' + poLineInsertData.po_line;
            poLineInsertData.order_status_id = await this.getOrderStatusId(LibConstants.ORDER_STATUS_ACTIVE);
            poLineInsertData.po_number_sequence = purchaseOrderData.poNumberSequence;
            const productTag = await this.dbServiceObj.findOneByMultipleWhere(this.userProductTagMappingRepository, { 'product_id': lineData.product_id, 'user_id': purchaseOrderData.buyer_id })
            if (productTag?.tag) {
                poLineInsertData.product_tag = productTag.tag;
            }
            buyerPricePerUnit = poLineInsertData.buyer_price_per_unit;
            buyerPurchase = poLineInsertData.buyer_line_total;
            escrow = poLineInsertData.seller_line_total
            const poLineId = (await this.dbServiceObj.saveData(poLineInsertData, this.userPurchaseOrderLineRepository)).id;
            lineData.id = poLineId;

            if(buyerPurchase) {
                const orderLedger = {
                    'user_id': purchaseOrderData.buyer_id,
                    'purchase_order_line_id': lineData.id,
                    'buyer_price_per_unit': buyerPricePerUnit,
                    'extended': buyerPurchase,
                    'escrow': escrow,
                    'sales_tax': lineData.sales_tax,
                    'buyer_purchase': buyerPurchase,
                    'transaction_type': LibConstants.ADD_NEW_LINES_CREATED,
                    'deposit_amount': depositAmount
                };
                await this.dbServiceObj.saveData(orderLedger, this.userPurchaseOrderLedgerRepository);

                const insertAdminLog = {
                    po_number: purchaseOrderData.buyer_po_number,
                    purchase_order_line_id: lineData.id,
                    po_line: poLineInsertData.po_line,
                    buyer_id: purchaseOrderData.buyer_id,
                    admin_id: adminId,
                    updated_qty: lineData.qty,
                    updated_qty_unit: lineData.qty_unit,
                    updated_price_unit: lineData.price_unit,
                    updated_product_id: lineData.product_id,
                    updated_reference_product_id:  lineData?.reference_product_id ?? null,
                    updated_description:  lineData.description,
                    updated_buyer_price_per_unit: lineData?.buyer_price,
                    updated_seller_price_per_unit: lineData.seller_price,
                    updated_buyer_pricing_lb: lineData.buyer_pricing_lb,
                    updated_product_tag: poLineInsertData?.product_tag ?? null,
                    updated_buyer_calculation_price_per_unit: lineData.buyer_calculation_price,
                    updated_seller_calculation_price_per_unit: lineData.seller_calculation_price_per_unit,
                    updated_actual_total_weight: lineData.total_line_weight,
                    updated_actual_buyer_line_total: lineData?.buyer_extended,
                    updated_actual_seller_line_total: lineData.seller_extended,
                    updated_order_line_sales_tax: lineData.sales_tax,
                    updated_domestic_material_only:  lineData?.domestic_material_only ?? false,
                };

                if(insertAdminLog && Object.keys(insertAdminLog).length > 0){
                    await this.dbServiceObj.saveData(insertAdminLog,this.adminLogUpdateOrderLineDetailsRepository);
                }
            }

            return poLineInsertData.po_line;
        } catch (error) {
            console.log(error)
            return false;
         }

    }

    async getOrderStatusId(orderStatus) {
        let orderStatusId = null;
        const orderStatusData = await this.dbServiceObj.findOne(this.referenceDataOrderStatusRepository, 'value', orderStatus)
        if (orderStatusData)
            orderStatusId = orderStatusData.id;

        return orderStatusId;
    }

    async saveTaxExemptedPurchaseOrders(poNumber: string, poLinesArr: Array<number>, logTaxExemptData) {

        if (poNumber && poLinesArr.length > 0 && logTaxExemptData && Object.keys(logTaxExemptData).length > 0) {
            const insert = {
                'po_number': poNumber,
                'po_line': JSON.stringify(poLinesArr),
                'resale_certificate_id': logTaxExemptData.user_resale_certificate_id ?? null,
                'resale_certificate_url': logTaxExemptData.user_resale_certificate_url ?? null,
                'annual_total_transaction_amount': logTaxExemptData.annual_transaction_amount ?? 0,
                'annual_total_transaction': logTaxExemptData.annual_transaction ?? 0,
                'economic_nexus_threshold_number_transactions': logTaxExemptData.economic_nexus_threshold_number_transactions ?? 0,
                'economic_nexus_threshold': logTaxExemptData.economic_nexus_threshold ?? 0,
                'state_rate_percent': logTaxExemptData.sales_tax_rate_percent ?? null,
                'event': LibConstants.TAX_EXEMPTED_EVENT_ADD_NEW_LINES
            };
            await this.dbServiceObj.saveData(insert, this.taxExemptedPurchaseOrdersRepository)
        }

    }

    async updateUserPurchaseData(purchaseOrderData) {
        const updatedPuchaseData = {
            actual_total_weight: purchaseOrderData.total_weight,
            sales_tax: purchaseOrderData.total_buyer_sales_tax,
            actual_buyer_po_price: purchaseOrderData.price,
            actual_seller_po_price: purchaseOrderData.seller_price,

        };
        
        const conditions = {
            buyer_id: purchaseOrderData.buyer_id,
            id: purchaseOrderData.id
        }
        await this.dbServiceObj.updateByMultipleWhere(updatedPuchaseData, conditions, this.userPurchaseOrderRepository);
    }

    async productPricingSnapshot(poLineData:any, poData:any, poNumber: string) {
        try {
            //send data to snapshot pricing queue
            const insertSnapshotData = {
                'product_id': poLineData.reference_product_data.Product_ID,
                'purchase_order_line_id': poLineData.id,
                'po_number': poNumber,
                'Buyer_Pricing_Ft': poLineData.reference_product_data.Buyer_Pricing_Ft.replace(/[$,]/g, ''),
                'Buyer_Pricing_Ea': poLineData.reference_product_data.Buyer_Pricing_Ea.replace(/[$,]/g, ''),
                'Buyer_Pricing_LB': poLineData.reference_product_data.Buyer_Pricing_LB.replace(/[$,]/g, ''),
                'Buyer_Pricing_CWT': poLineData.reference_product_data.Buyer_Pricing_CWT.replace(/[$,]/g, ''),
                'Buyer_Pricing_Net_Ton': poLineData.reference_product_data.Buyer_Pricing_Net_Ton.replace(/[$,]/g, ''),
                'Seller_Pricing_Ft': poLineData.reference_product_data.Seller_Pricing_Ft.replace(/[$,]/g, ''),
                'Seller_Pricing_Ea': poLineData.reference_product_data.Seller_Pricing_Ea.replace(/[$,]/g, ''),
                'Seller_Pricing_LB': poLineData.reference_product_data.Seller_Pricing_LB.replace(/[$,]/g, ''),
                'Seller_Pricing_CWT': poLineData.reference_product_data.Seller_Pricing_CWT.replace(/[$,]/g, ''),
                'Seller_Pricing_Net_Ton': poLineData.reference_product_data.Seller_Pricing_Net_Ton.replace(/[$,]/g, ''),
                'Neutral_Pricing_Ft': poLineData.reference_product_data.Neutral_Pricing_Ft.replace(/[$,]/g, ''),
                'Neutral_Pricing_Ea': poLineData.reference_product_data.Neutral_Pricing_Ea.replace(/[$,]/g, ''),
                'Neutral_Pricing_LB': poLineData.reference_product_data.Neutral_Pricing_LB.replace(/[$,]/g, ''),
                'Neutral_Pricing_CWT': poLineData.reference_product_data.Neutral_Pricing_CWT.replace(/[$,]/g, ''),
                'Neutral_Pricing_Net_Ton': poLineData.reference_product_data.Neutral_Pricing_Net_Ton.replace(/[$,]/g, ''),
                'is_buyer_spread': false, // As we are not applying spread on add line, set its default value to false
                'buyer_spread_rate': poData.user_spread_data.buyer_spread_rate,
                // 'disc_discount_period': poData.user_spread_data.disc_discount_period,
                // 'disc_discount_phaseout_startdate': poData.user_spread_data.disc_discount_phaseout_startdate,
                // 'disc_discount_phaseout_period': poData.user_spread_data.disc_discount_phaseout_period,
                'base_pricing_column': poData.user_spread_data.base_pricing_column,
                'is_buyer_spread_overriden': poData.user_spread_data.is_buyer_spread_overriden === 1 ? true : false
            }
            await this.dbServiceObj.saveData(insertSnapshotData, this.orderLineSnapshotPricingRepository)
            return true;
        } catch (error) {
            console.log(error);
            return false;
        }

    }

    async updateBuyerCredits(userId, totalPoPrice, poNumber, reason) {
        if (totalPoPrice) {
            const insertCreditLogs = {
                buyer_id: userId,
                reason: reason,
                credit_limit: null,
                available_credit_limit: null,
                outstanding_credit_limit: null,
                po_number: null
            }
            const getBnplData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository, 'user_id', userId)
            if (getBnplData && Object.keys(getBnplData).length > 0) {
                let updatedAvailableLimit = getBnplData.bryzos_available_credit_limit - totalPoPrice;
                const updatedBnplData = {
                    bryzos_available_credit_limit: updatedAvailableLimit,
                    bryzos_outstanding_credit_limit: getBnplData.bryzos_credit_limit - updatedAvailableLimit
                }
                this.dbServiceObj.updateByMultipleWhere(updatedBnplData, { user_id: userId }, this.companyBuyNowPayLaterRepository)
                // Fetch the updated data
                // const updatedData = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository, 'user_id', userId);

                // Access the updated values
                insertCreditLogs.credit_limit = getBnplData.bryzos_credit_limit;
                insertCreditLogs.available_credit_limit = updatedBnplData.bryzos_available_credit_limit;
                insertCreditLogs.outstanding_credit_limit = updatedBnplData.bryzos_outstanding_credit_limit;
                insertCreditLogs.po_number = poNumber;

                await this.dbServiceObj.saveData(insertCreditLogs, this.logBryzosCreditLimitRepository);
            }
        }
    }

    async updateOrderLineData(updateLineDto : UpdateLinesDto, adminId)
    {
        let response = {};
        if(updateLineDto && updateLineDto.hasOwnProperty('cart_items')){

            let salesTaxRate = 0
            let updatedLineNewBuyerPrice = 0;
            let updatedLineNewSalesTax = 0;
            let updatedLineOldSalesTax = 0;
            let updatedLineOldBuyerPrice = 0;
            let updatedLineNewTotalBuyerPrice = 0;
            let updatedLineOldTotalBuyerPrice = 0;
            let reCalculateSalesTax = false;

            const purchaseOrderId = updateLineDto.purchase_order_id;

            const cartItems = updateLineDto.cart_items;
            //lines sent by UI. UI sends us all lines present in PO active/inactive/cancelled/completed
            const purchaseOrderLines = cartItems.map((item) => item.purchase_order_line_id);

            //From DB we get active and completed
            const purchaseOrderLineData = await this.dbServiceObj.findManyByWhereAndWhereIn(this.userPurchaseOrderLineRepository, {'purchase_order_id': purchaseOrderId }, 'id', purchaseOrderLines );

            if(!purchaseOrderLineData){
                response['error_message'] = "Order line not found!";
                return response;
            }

            //PO level data
            const purchaseOrderData = await this.getPurchaseOrderDataById(purchaseOrderId);

            if(!purchaseOrderData){
                response['error_message'] = "Order not found!";
                return response;
            }

            //Product ids sent by UI via payload. UI sends us all lines present in PO active/inactive/cancelled/completed
            const productIdList = cartItems.map((item) => item.product_id);

            const buyerId = purchaseOrderData.buyer_id;
            const poNumber = purchaseOrderData.buyer_po_number;
            const paymentMethod = purchaseOrderData.payment_method;
            //const buyerOrderPrice = purchaseOrderData.actual_buyer_po_price || purchaseOrderData.buyer_po_price;
            //const poSalesTax = purchaseOrderData.sales_tax || 0;

            const orderStatusResolved = await this.getOrderStatusId(LibConstants.ORDER_STATUS_RESOLVED); //10
            const orderStatusActive = await this.getOrderStatusId(LibConstants.ORDER_STATUS_ACTIVE); //1
            const orderStatusIds = [orderStatusActive, orderStatusResolved];

            const resaleCertData = await this.dbServiceObj.findOneByMultipleWhere(this.userResaleCertificateRepository,{user_id:buyerId,state_id:purchaseOrderData.state_id});
    
            //get sales tax state rate
            const salesTaxData = await this.baseLibraryService.calculateSalesTaxRate(purchaseOrderData.state_id, purchaseOrderData.zip, resaleCertData, this.userPurchaseOrderRepository);
            salesTaxRate = salesTaxData.sales_tax_rate;
                               
            const selectFields = ["IFNULL(SUM(escrow),0) as total_seller_price","IFNULL(SUM(buyer_purchase),0) as total_buyer_price","IFNULL(SUM(sales_tax),0) as total_sales_tax", "IFNULL(SUM(buyer_price_per_unit),0) as buyer_price_per_unit", "purchase_order_line_id"];
            const conditions = { purchase_order_line_id: { operator: "IN", value: purchaseOrderLines } };
            const purchaseOrderLedgerData = await this.dbServiceObj.FindByMultipleWhereComparisonANDLeftJoinById(this.userPurchaseOrderLedgerRepository,[],{conditions},'purchase_order_line_id',selectFields);
         
            let updatePoLineData = [];
            let updateLedgerData = [];
            let insertAdminLogData = [];

            const userProductTagMapping = await this.dbServiceObj.findManyByWhereAndWhereIn(this.userProductTagMappingRepository,{"user_id":buyerId},"product_id",productIdList);
            let isUserProductTagMapped = false;
            if(userProductTagMapping){ isUserProductTagMapped = true; }
            //Products sent by UI via payload. UI sends us all lines present in PO active/inactive/cancelled/completed
            for (const item of cartItems) {

                let updateLines = {};
                let updateLedger = {};
                let insertAdminLog = {};

                let purchaseOrderLineId = item.purchase_order_line_id;
                let productId = item.product_id;
                let qty = item.qty;

                //TODO : we need to remove this logic and implement check if edited line is present in reference data products

                const purchaseOrderLineDetail = purchaseOrderLineData.find(orderLineData => orderLineData.id === purchaseOrderLineId);
                
                if(!purchaseOrderLineDetail){ continue; }
                
                if(!orderStatusIds.includes(purchaseOrderLineDetail.order_status_id)){
                    updatedLineNewSalesTax = Number(updatedLineNewSalesTax) + Number(purchaseOrderLineDetail.sales_tax);
                    // response['error_message'] = `Po line ${item.line_id} is closed`;
                    // return response;
                    continue;
                }

                const purchaseOrderLedgerDetail = purchaseOrderLedgerData.find(orderLegerData => orderLegerData.purchase_order_line_id === purchaseOrderLineId);

                let orderLineDomesticMaterial = purchaseOrderLineDetail?.domestic_material_only === 0 ? false : true;
                let itemDomesticMaterial = item?.domestic_material_only ?? false;

                if(Number(purchaseOrderLineDetail.product_id) !== item.product_id){
                    // reCalculateSalesTax = true;

                    updateLines['product_id'] = item.product_id;
                    updateLines['reference_product_id'] = item.reference_product_id;
                    updateLines['shape'] = item.shape;
                    updateLines['description'] = item.description;

                    updateLines['buyer_pricing_lb'] = item.buyer_pricing_lb;

                    if(isUserProductTagMapped){
                        const productTagData = userProductTagMapping.find(obj => obj.product_id === productId);
                        if( productTagData && (productTagData?.tag !== purchaseOrderLineDetail.product_tag)){
                            updateLines['product_tag'] = productTagData.tag;
                            insertAdminLog['old_product_tag'] = purchaseOrderLineDetail.product_tag;
                            insertAdminLog['new_product_tag'] = productTagData.tag;
                        }else{
                            updateLines['product_tag'] = null;
                            insertAdminLog['old_product_tag'] = purchaseOrderLineDetail.product_tag;
                            insertAdminLog['new_product_tag'] = null;
                        }
                    }else if(!purchaseOrderLineDetail.product_tag){
                        updateLines['product_tag'] = null;
                        insertAdminLog['old_product_tag'] = purchaseOrderLineDetail.product_tag;
                        insertAdminLog['new_product_tag'] = null;
                    }

                    insertAdminLog['old_product_id'] = purchaseOrderLineDetail.product_id;
                    insertAdminLog['updated_product_id'] = item.product_id;
                    insertAdminLog['old_buyer_pricing_lb'] = purchaseOrderLineDetail.buyer_pricing_lb;
                    insertAdminLog['updated_buyer_pricing_lb'] = updateLines['buyer_pricing_lb'];
                    insertAdminLog['old_description'] = purchaseOrderLineDetail.description;
                    insertAdminLog['updated_description'] = item.description;
                }

                if(Number(purchaseOrderLineDetail.reference_product_id) !== item.reference_product_id){
                    updateLines['reference_product_id'] = item.reference_product_id;
                    insertAdminLog['old_reference_product_id'] = purchaseOrderLineDetail.reference_product_id;
                    insertAdminLog['updated_reference_product_id'] = item.reference_product_id;
                }

                if(purchaseOrderLineDetail.qty_unit !== item.qty_unit){
                    updateLines['qty_unit'] = item.qty_unit;
                    insertAdminLog['old_qty_unit'] = purchaseOrderLineDetail.qty_unit;
                    insertAdminLog['updated_qty_unit'] = item.qty_unit;
                }
                
                if(Number(purchaseOrderLineDetail.buyer_price_per_unit) !== item.buyer_price){
                    updateLines['buyer_price_per_unit'] = item.buyer_price;
                    insertAdminLog['old_buyer_price_per_unit'] = purchaseOrderLineDetail.buyer_price_per_unit;
                    insertAdminLog['updated_buyer_price_per_unit'] = item.buyer_price;
                    updateLedger['buyer_price_per_unit'] =   item.buyer_price - Number(purchaseOrderLedgerDetail.buyer_price_per_unit);
                }  

                if(Number(purchaseOrderLineDetail.seller_price_per_unit) !== item.seller_price){
                    updateLines['seller_price_per_unit'] = item.seller_price;
                    insertAdminLog['old_seller_price_per_unit'] = purchaseOrderLineDetail.seller_price_per_unit;
                    insertAdminLog['updated_seller_price_per_unit'] = item.seller_price;
                } 

                if(purchaseOrderLineDetail.price_unit !== item.price_unit){
                    updateLines['price_unit'] = item.price_unit;
                    insertAdminLog['old_price_unit'] = purchaseOrderLineDetail.price_unit;
                    insertAdminLog['updated_price_unit'] = item.price_unit;
                }
                
                if(Number(purchaseOrderLineDetail.actual_qty) !== item.qty){
                    reCalculateSalesTax = true;
                    updateLines['actual_qty'] = item.qty;
                    insertAdminLog['old_qty'] = purchaseOrderLineDetail.actual_qty;
                    insertAdminLog['updated_qty'] = item.qty;
                }

                if(Number(purchaseOrderLineDetail.actual_total_weight) !== item.total_weight){
                    updateLines['actual_total_weight'] = item.total_weight;
                    insertAdminLog['old_actual_total_weight'] = purchaseOrderLineDetail.actual_total_weight;
                    insertAdminLog['updated_actual_total_weight'] = item.total_weight;
                }
                
                if(Number(purchaseOrderLineDetail.actual_buyer_line_total) !== item.buyer_extended) {
                    // reCalculateSalesTax = true;

                    updateLines['actual_buyer_line_total'] = item.buyer_extended;

                    let oldLineSalesTax = purchaseOrderLineDetail?.sales_tax || 0;
                         
                    updateLedger['extended'] = updateLedger['buyer_purchase'] = Number(updateLines['actual_buyer_line_total']) - Number(purchaseOrderLedgerDetail.total_buyer_price);
                    // updateLedger['buyer_purchase'] = Number(updateLines['actual_buyer_line_total']) - Number(purchaseOrderLedgerDetail.total_buyer_price);

                    insertAdminLog['old_actual_buyer_line_total'] = Number(purchaseOrderLineDetail.actual_buyer_line_total);
                    insertAdminLog['updated_actual_buyer_line_total'] = Number(item.buyer_extended);

                    if(purchaseOrderLineDetail?.is_buyer_order_open == true){
                        updateLines['sales_tax'] = item.buyer_extended * salesTaxRate;
                        updateLedger['sales_tax'] = Number(updateLines['sales_tax']) - Number(purchaseOrderLedgerDetail.total_sales_tax);
                        updatedLineNewSalesTax = +(Number(updatedLineNewSalesTax) + Number(updateLines['sales_tax'])).toFixed(2);
                        insertAdminLog['old_order_line_sales_tax'] = Number(oldLineSalesTax);
                        insertAdminLog['updated_order_line_sales_tax'] = Number(updateLines['sales_tax']);
    
                    }

                    updatedLineOldSalesTax = Number(updatedLineOldSalesTax) + Number(oldLineSalesTax);

                    updatedLineNewBuyerPrice = Number(updatedLineNewBuyerPrice) + Number(updateLines['actual_buyer_line_total']);
                    updatedLineOldBuyerPrice = Number(updatedLineOldBuyerPrice) + Number(purchaseOrderLineDetail.actual_buyer_line_total);

                    updatedLineOldTotalBuyerPrice = Number(updatedLineOldBuyerPrice) + Number(updatedLineOldSalesTax);
                    updatedLineNewTotalBuyerPrice = Number(updatedLineNewBuyerPrice) + Number(updatedLineNewSalesTax);

                } else if(purchaseOrderLineDetail?.is_buyer_order_open == true){
                    let oldLineSalesTax = purchaseOrderLineDetail?.sales_tax || 0;
                    // if(salesTaxRate){
                        
                        updateLines['sales_tax'] = purchaseOrderLineDetail.actual_buyer_line_total * salesTaxRate;
                        if(Number(updateLines['sales_tax'].toFixed(2)) !== Number(oldLineSalesTax)) {
                            updateLedger['sales_tax'] = Number(updateLines['sales_tax']) - Number(purchaseOrderLedgerDetail.total_sales_tax);
                            updatedLineNewSalesTax = +(Number(updatedLineNewSalesTax) + Number(updateLines['sales_tax'])).toFixed(2);

                            insertAdminLog['old_order_line_sales_tax'] = Number(oldLineSalesTax);
                            insertAdminLog['updated_order_line_sales_tax'] = Number(updateLines['sales_tax']);

                            updatedLineOldSalesTax = Number(updatedLineOldSalesTax) + Number(oldLineSalesTax);

                            updatedLineNewBuyerPrice = Number(updatedLineNewBuyerPrice) + Number(item.buyer_extended);
                            updatedLineOldBuyerPrice = Number(updatedLineOldBuyerPrice) + Number(purchaseOrderLineDetail.actual_buyer_line_total);

                            updatedLineOldTotalBuyerPrice = Number(updatedLineOldBuyerPrice) + Number(updatedLineOldSalesTax);
                            updatedLineNewTotalBuyerPrice = Number(updatedLineNewBuyerPrice) + Number(updatedLineNewSalesTax);
                        }
                    // }
                }

                if(Number(purchaseOrderLineDetail.actual_seller_line_total) !== item.seller_extended){
                    updateLines['actual_seller_line_total'] = item.seller_extended;
                    updateLedger['escrow'] = Number(updateLines['actual_seller_line_total']) - Number(purchaseOrderLedgerDetail.total_seller_price);
                    insertAdminLog['old_actual_seller_line_total'] = Number(purchaseOrderLineDetail.actual_seller_line_total);
                    insertAdminLog['updated_actual_seller_line_total'] = Number(item.seller_extended);
                }

                if(orderLineDomesticMaterial !== itemDomesticMaterial){
                    updateLines['domestic_material_only'] = itemDomesticMaterial;
                    insertAdminLog['old_domestic_material_only'] = purchaseOrderLineDetail.domestic_material_only;
                    insertAdminLog['updated_domestic_material_only'] = itemDomesticMaterial;
                }

                if(Number(purchaseOrderLineDetail.seller_calculation_price_per_unit) !== item.seller_calculation_price_per_unit){
                    updateLines['seller_calculation_price_per_unit'] = item.seller_calculation_price_per_unit;
                    insertAdminLog['old_seller_calculation_price_per_unit'] = purchaseOrderLineDetail.seller_calculation_price_per_unit;
                    insertAdminLog['updated_seller_calculation_price_per_unit'] = item.seller_calculation_price_per_unit;
                }  

                if(Number(purchaseOrderLineDetail.buyer_calculation_price_per_unit) !== item.buyer_calculation_price_per_unit){
                    updateLines['buyer_calculation_price_per_unit'] = item.buyer_calculation_price_per_unit;
                    insertAdminLog['old_buyer_calculation_price_per_unit'] = purchaseOrderLineDetail.buyer_calculation_price_per_unit;
                    insertAdminLog['updated_buyer_calculation_price_per_unit'] = item.buyer_calculation_price_per_unit;
                }  
                
                if(updateLines && Object.keys(updateLines).length > 0 ){
                    updateLines['id'] = purchaseOrderLineId;
                    updatePoLineData = updatePoLineData.concat(updateLines);
                }

                if(updateLedger && Object.keys(updateLedger).length > 0 ){
                    updateLedger['purchase_order_line_id'] = purchaseOrderLineId;
                    updateLedger['user_id'] = buyerId;
                    updateLedger['transaction_type'] = Constants.TRANSACTION_TYPE_BA_RCDC;
                    updateLedgerData = updateLedgerData.concat(updateLedger);
                }

                  
                if(insertAdminLog && Object.keys(insertAdminLog).length > 0){
                    insertAdminLog['purchase_order_line_id'] = purchaseOrderLineId;
                    insertAdminLog['po_line'] = purchaseOrderLineDetail.po_line;
                    insertAdminLog['po_number'] = poNumber;
                    insertAdminLog['buyer_id'] = buyerId;
                    insertAdminLog['admin_id'] = adminId;
                    insertAdminLogData = insertAdminLogData.concat(insertAdminLog);
                }
            }
            //for loop end
            let updateLineTotalBuyerPriceDiff = 0;

            let checkCreditLimit = null;
            if(updatePoLineData && updatePoLineData.length > 0){
                if(paymentMethod === LibConstants.PAYMENT_METHOD_BRYZOS_PAY){
                    if(Number(updatedLineNewTotalBuyerPrice) > 0 && Number(updatedLineOldTotalBuyerPrice) > 0){
                        updateLineTotalBuyerPriceDiff = Number(updatedLineNewTotalBuyerPrice) - Number(updatedLineOldTotalBuyerPrice);
                        checkCreditLimit = await this.utils.checkBuyerCreditLimit(Number(updateLineTotalBuyerPriceDiff), buyerId);
                        if(checkCreditLimit.error_message){
                            response = {error_message : checkCreditLimit.error_message};
                            return response;
                        }
                    }
                    // await this.logBryzosCreditLimit(buyerId,adminId,'AD_UPDATE_LINE', poNumber);
                }
                await this.dbServiceObj.saveData(updatePoLineData,this.userPurchaseOrderLineRepository);
            }

            if(updateLedgerData && updateLedgerData.length > 0){
                await this.dbServiceObj.saveData(updateLedgerData,this.userPurchaseOrderLedgerRepository);
            }
      
            const totalPoPrices = await this.get_total_extended(poNumber);
            if(totalPoPrices){
    
                const updateConditions = {buyer_po_number:poNumber, buyer_id:buyerId};
                const updateValues = {actual_buyer_po_price:totalPoPrices.buyer_extended, actual_seller_po_price:totalPoPrices.seller_extended, actual_total_weight: totalPoPrices.actual_total_weight, sales_tax : totalPoPrices.total_sales_tax};
                    
                await this.dbServiceObj.updateByMultipleWhere(updateValues,updateConditions,this.userPurchaseOrderRepository);

                if(totalPoPrices.total_sales_tax && Number(totalPoPrices.total_sales_tax) == 0)
                    {
                      const taxExemptLog: any = {};
      
                      if(salesTaxData){
                        if(salesTaxData.is_threshold_met_cert_approved){
                          taxExemptLog.resale_certificate_url = resaleCertData.cerificate_url_s3;
                          taxExemptLog.resale_certificate_id = resaleCertData.id;
                        }
                        taxExemptLog.po_number = poNumber;
                        taxExemptLog.event = 'AD_UPDATE_LINE';
                        taxExemptLog.annual_total_transaction_amount = salesTaxData.annual_transaction_amount;
                        taxExemptLog.annual_total_transaction = salesTaxData.annual_transaction;
                        taxExemptLog.combined_rate_number = salesTaxData.ref_combined_rate_number;
                        taxExemptLog.economic_nexus_threshold = salesTaxData.ref_economic_nexus_threshold;
                        taxExemptLog.economic_nexus_threshold_number_transactions = salesTaxData.ref_economic_nexus_threshold_number_transactions;
                      }
      
                      await this.dbServiceObj.saveData(taxExemptLog,this.taxExemptedPurchaseOrdersRepository);    
                    }
            }
          
            if(insertAdminLogData && insertAdminLogData.length > 0){
                await this.dbServiceObj.saveData(insertAdminLogData,this.adminLogUpdateOrderLineDetailsRepository);
            }

            //Update buyer credit
            if(checkCreditLimit) {
                await this.utils.updateBryzosCreditLimit(checkCreditLimit.bryzos_available_credit_limit, updateLineTotalBuyerPriceDiff, checkCreditLimit.bryzos_credit_limit, buyerId);
                await this.logBryzosCreditLimit(buyerId,adminId,'AD_UPDATE_LINE', poNumber);
            }
         
            
            if(purchaseOrderData && purchaseOrderData.seller_id === null){
                await this.sendWebsocketEvent(poNumber);
            }

            response = "Data updated Sucessfully";
        }

        return response;
    }    

    async get_total_extended(po_number:string){

        const orderStatusResolved = await this.getOrderStatusId(LibConstants.ORDER_STATUS_RESOLVED); //10
        const orderStatusActive = await this.getOrderStatusId(LibConstants.ORDER_STATUS_ACTIVE); //1
        const orderStatusCompleted = await this.getOrderStatusId(LibConstants.ORDER_STATUS_COMPLETED); //1

        const selectFields = ['IFNULL(SUM(actual_buyer_line_total),0) as buyer_extended','IFNULL(SUM(actual_seller_line_total),0) as seller_extended','IFNULL(SUM(actual_total_weight),0) as actual_total_weight','IFNULL(SUM(sales_tax),0) as total_sales_tax'];
    
        const conditions = {};
    
        const condition1_attributes = {"operator":"=","value":po_number};
        conditions["buyer_po_number"] = condition1_attributes;

        const condition2_attributes = {"operator":"IN","value":[orderStatusResolved,orderStatusActive,orderStatusCompleted]};
        conditions["order_status_id"] = condition2_attributes;
    
        const combineAndConditions = {conditions};
    
        const data = await this.dbServiceObj.FindByMultipleWhereComparisonANDLeftJoinById(this.userPurchaseOrderLineRepository,[],combineAndConditions,null,selectFields);
        
        if(data && data.length > 0){
          return data[0];
        }
        return null;
    }

    
  async logBryzosCreditLimit(buyerId,adminId,event, poNumber){
    const companyBuyNowPayLater = await this.dbServiceObj.findOne(this.companyBuyNowPayLaterRepository,'user_id',buyerId);
    if(companyBuyNowPayLater){
      let logData = {};
      logData["admin_id"]=adminId
      logData["buyer_id"]=buyerId
      logData["credit_limit"]=parseFloat(companyBuyNowPayLater.bryzos_credit_limit)
      logData["available_credit_limit"]=parseFloat(companyBuyNowPayLater.bryzos_available_credit_limit)
      logData["outstanding_credit_limit"]=parseFloat(companyBuyNowPayLater.bryzos_outstanding_credit_limit)
      logData["reason"]=event
      logData["po_number"]= poNumber

      await this.dbServiceObj.saveData(logData,this.logBryzosCreditLimitRepository);
    }
  }

  sendWebsocketEvent = (poNumber:string)  => new Promise(async function (resolve, reject) 
  {
    let curlResponse = null;
    const updatePoListUrl = process.env.GISS_WS_SERVER+'/updatePurchaseOrder';

    const axios = require('axios');

    let updateData = {
      method: 'post',
      maxBodyLength: Infinity,
      url: updatePoListUrl,
      headers: { 
        'accept': 'application/json', 
        'content-type': 'application/json', 
        'gissToken':  process.env.GISS_UI_TOKEN
      },
      data: {
        buyer_po_number: poNumber
      }
    };

    axios.request(updateData)
    .then((response) => {
      curlResponse = response.data;
      BryzosLogger.log(JSON.stringify({"webSocketResponse":curlResponse}), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
      resolve(curlResponse);
    })
    .catch((error) => {
      BryzosLogger.log(JSON.stringify({"Error":curlResponse}), process.env.LOGGLY_ERROR_TAG);
    console.error(error.response.data);
    resolve(curlResponse);
    });
  });

  async salesTaxCalculate(payload:SalesTaxOrderDto)
  {
    let salesTaxCounter = null;
    if(payload.salesTaxCounter){
      salesTaxCounter = payload.salesTaxCounter
    }
    let salesTax = 0;

    const buyerId = payload.buyer_id;
    const freightTerm = payload.freight_term;
    const delivery_state_id = payload.shipping_details.state_id;
    const zipCode = payload.shipping_details.zip;
 
    
    if (freightTerm === Constants.DELIVERED && delivery_state_id && zipCode) { //Frieght Term is always Delivered in GISS
      
      const resaleCertData = await this.dbServiceObj.findOneByMultipleWhere(this.userResaleCertificateRepository,{user_id:buyerId,state_id:delivery_state_id});

      const salesTaxData = await this.baseLibraryService.calculateSalesTaxRate(delivery_state_id, zipCode, resaleCertData, this.userPurchaseOrderRepository);
      let salesTaxRate = 0;
      if(salesTaxData){
        salesTaxRate = salesTaxData.sales_tax_rate;
      }
      const cart_items = payload.cart_items;
      
      if(cart_items.length > 0 && salesTaxRate){
        for(const cart_item of cart_items){
          const calculateSalesTax = Number(cart_item.extended) * Number(salesTaxRate);
          salesTax += parseFloat(calculateSalesTax.toFixed(2));
        }
      }
    }
    return {"tax" : salesTax,"salesTaxCounter" : salesTaxCounter};
  }

  async deliveryDate(userId:string,currentTimestamp:string){
    let response;
    const user = await this.dbServiceObj.findOneByMultipleWhere(this.userRepository,{"id": userId, "type" : Constants.BUYERROLE });
    if(!user){
        return {error_message : "User not found !"};
    }
    
    try{
      response = await this.libraryOrderUtility.deliveryDate(userId,currentTimestamp);
    }catch(error){
      response = {error_message : "Delievery dates not found. Please try again"};
    }
    return response;
}
}
