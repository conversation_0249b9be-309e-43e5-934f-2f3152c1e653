import { Controller, Get, Post, Body, Patch, Param, Delete, UsePipes, ValidationPipe,Response } from '@nestjs/common';
import { EmailGenerateService } from './email_generate.service';
import { SaveSendEmailDataDto } from './dto/email_generate.dto';
import { Constants } from 'src/Constants';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@Controller('email-generate')
export class EmailGenerateController {
  constructor(private readonly emailGenerateService: EmailGenerateService) {}

  @Get('getPODetails/:poNumber')
  async getPODetails(@Param('poNumber') poNumber: string,@Response() res) {
    let responseData = {
      [responseTag]: await this.emailGenerateService.orderDetails(poNumber)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('sendEmail')
  @UsePipes(ValidationPipe)
  async sendEmail(@Body() saveDyspatchDto:SaveSendEmailDataDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = saveDyspatchDto[payloadTag];
    let responseData = {
      [responseTag]: await this.emailGenerateService.sendEmailForPonumber(payloadData,adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

}
