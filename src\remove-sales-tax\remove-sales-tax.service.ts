import { BaseLibraryService, DataBaseService } from '@bryzos/base-library';
import { AdminLogRemoveSalesTax, CompanyBuyNowPayLater, LogBryzosCreditLimit, ReferenceDataSettings, TaxExemptedPurchaseOrders, UserPurchaseOrder, UserPurchaseOrderLedger, UserPurchaseOrderLine, UserResaleCertificate, } from '@bryzos/extended-widget-library';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Constants } from '../Constants';
import { RemoveSalesTaxDto, updateRemoveSalesTaxDto } from './dto/remove-sales-tax.dto';

const responseErrorTag = Constants.ERROR_TAG;

@Injectable()
export class RemoveSalesTaxService {
  private dbObjService = new DataBaseService();

  constructor(
    private readonly baseLibraryService : BaseLibraryService,

    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(UserPurchaseOrderLedger) private readonly userPurchaseOrderLedgerRepository: Repository<UserPurchaseOrderLedger>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(UserResaleCertificate) private readonly userResaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(LogBryzosCreditLimit) private readonly logBryzosCreditLimitRepository: Repository<LogBryzosCreditLimit>,
    @InjectRepository(AdminLogRemoveSalesTax) private readonly adminLogRemoveSalesTaxRepository: Repository<AdminLogRemoveSalesTax>,
    @InjectRepository(TaxExemptedPurchaseOrders) private readonly taxExemptedPoRepository: Repository<TaxExemptedPurchaseOrders>,
    
  ) { }

  async removeSalesTax(payload: RemoveSalesTaxDto, adminId: number) {
    let response = null;
    const poNumber = payload.po_number;
    let userResaleCertificateUrl=null;
    let userResaleCertificateId=null;

    const userPurchaseOrderResult = await this.getUserPurchaseOrderTableData(poNumber);

    if (!userPurchaseOrderResult) {
      response = { [responseErrorTag]: 'Something went wrong!' };
      return response;
    }

    const userPurchaseOrderId = userPurchaseOrderResult.id;
    const _salesTax = userPurchaseOrderResult.sales_tax;
    if (_salesTax === null || _salesTax === undefined || Number(_salesTax) <= 0) {
      response = { [responseErrorTag]: 'No sales tax found' };
      return response;
    }

    const salesTax = Number(_salesTax);
    const buyerId = userPurchaseOrderResult.buyer_id;
    const paymentMethod = userPurchaseOrderResult.payment_method;
    const stateId = userPurchaseOrderResult.state_id;
    const zipCode = userPurchaseOrderResult.zip;


    const userResaleCertificateResult = await this.validateResaleCertificate(buyerId, stateId);

    if (!userResaleCertificateResult) {
      response = { [responseErrorTag]: 'Please approve resale certificate, then try again!' };
      return response;
    }

    const userPurchaseOrderLineResult = await this.getUserPurchaseOrderLinesData(userPurchaseOrderId);

    if (!userPurchaseOrderLineResult.length) {
      response = { [responseErrorTag]: 'Something went wrong!' };
      return response;
    }

    if(userResaleCertificateResult && userResaleCertificateResult.cerificate_url_s3){
      userResaleCertificateUrl=userResaleCertificateResult.cerificate_url_s3;
      userResaleCertificateId=userResaleCertificateResult.id;
    }

    const userPurchaseOrderUpdateResult = await this.setUserPurchaseOrderTableData({ sales_tax: 0 }, poNumber);

    if (!userPurchaseOrderUpdateResult) {
      response = { [responseErrorTag]: 'Something went wrong!' };
      return response;
    }

    const salesTaxData = await this.baseLibraryService.calculateSalesTaxRate(stateId, zipCode, userResaleCertificateResult, this.userPurchaseOrderRepository);
    
    const taxExemptLog: any = {};

    if(salesTaxData){
      if(salesTaxData.is_threshold_met_cert_approved){
        taxExemptLog.resale_certificate_url = userResaleCertificateUrl;
        taxExemptLog.resale_certificate_id = userResaleCertificateId;
      }
      taxExemptLog.po_number = poNumber;
      taxExemptLog.event = Constants.TAX_EXEMPTED_EVENT_REMOVE_SALES_TAX;
      taxExemptLog.annual_total_transaction_amount = salesTaxData.annual_transaction_amount;
      taxExemptLog.annual_total_transaction = salesTaxData.annual_transaction;
      taxExemptLog.combined_rate_number = salesTaxData.ref_combined_rate_number;
      taxExemptLog.economic_nexus_threshold = salesTaxData.ref_economic_nexus_threshold;
      taxExemptLog.economic_nexus_threshold_number_transactions = salesTaxData.ref_economic_nexus_threshold_number_transactions;
    }

    const TaxExemptedPurchaseOrders = await this.setTaxExemptedPurchaseOrders(taxExemptLog);

    const purchaseOrderLineIds = [];
    const userPurchaseOrderLedgers = [];

    //  set sales tax null in user_purchase_order_line table with the help of purchase_order_id
    userPurchaseOrderLineResult.forEach(orderLine => {
      purchaseOrderLineIds.push(orderLine.id);

      userPurchaseOrderLedgers.push({
        user_id: buyerId,
        purchase_order_line_id: orderLine.id,
        sales_tax: -Number(orderLine.sales_tax > 0 ? orderLine.sales_tax : 0),
        transaction_type: Constants.TRANSACTION_TYPE_BA_RCDC,
      });
    });

    const userPurchaseOrderLineUpdateResult = await this.setUserPurchaseOrderLinesData(purchaseOrderLineIds);

    if (!userPurchaseOrderLineUpdateResult) {
      response = { [responseErrorTag]: 'Something went wrong!' };
      return response;
    }

    const userPurchaseOrderLedgerUpdateResult = await this.setUserPurchaseOrderLedgerData(userPurchaseOrderLedgers);

    if (!userPurchaseOrderLedgerUpdateResult) {
      response = { [responseErrorTag]: 'Something went wrong!' };
      return response;
    }

    // if payment method is bryzos pay then find company_buy_now_pay_later table data with the help of user_id
    if (paymentMethod === Constants.PAYMENT_METHOD_BRYZOS_PAY) {
      const companyBuyNowPayLaterConditions = { user_id: buyerId, is_approved: true };
      let companyBuyNowPayLaterResult = await this.dbObjService.findOneByMultipleWhere(this.companyBuyNowPayLaterRepository, companyBuyNowPayLaterConditions);

      if (!companyBuyNowPayLaterResult) {
        response = { [responseErrorTag]: 'Something went wrong!' };
        return response;
      }

      const referenceDataSettingsResult = await this.dbObjService.findOne(this.referenceDataSettingsRepository, 'name', Constants.CHECKOUT_VIA_BALANCE);

      if (!referenceDataSettingsResult) {
        response = { [responseErrorTag]: 'Something went wrong!' };
        return response;
      }

      const isCheckoutViaBalance = referenceDataSettingsResult.value;

      const logBryzosCreditLimitCreateDTO = { admin_id: adminId, buyer_id: buyerId, reason: 'Removed sales Tax via A.D.', po_number: poNumber };
      await this.setBryzosCreditLimitData(companyBuyNowPayLaterResult, isCheckoutViaBalance, logBryzosCreditLimitCreateDTO, buyerId, salesTax);
    }

    const adminLogRemoveSalesTaxCreateDTO = { admin_id: adminId, po_number: poNumber, sales_tax: salesTax };
    await this.setAdminLogRemoveSalesTaxData(adminLogRemoveSalesTaxCreateDTO, buyerId);

    response = 'Sales Tax removed successfully';
    return response;
  }

  async getUserPurchaseOrderTableData(poNumber: string) {
    let result = null;
    // find buyer data in user_purchase_order table with the help of po_number
    result = await this.dbObjService.findOne(this.userPurchaseOrderRepository, 'buyer_po_number', poNumber);
    return result;
  }

  async setUserPurchaseOrderTableData(data: any, poNumber: string) {
    let result = null;
    // set sales tax null in user_purchase_order table with the help of buyer_po_number
    result = await this.dbObjService.updateWithoutMapper(data, 'buyer_po_number', poNumber, this.userPurchaseOrderRepository);
    return result;
  }

  async validateResaleCertificate(buyerId: string, stateId: string) {
    let result = null;
    // check resale certificate is approved and certificate state is same as purchase order state in user_resale_certificate table with the help of user_id
    const userResaleCertificateConditions = { user_id: buyerId, status: Constants.USER_RESALE_CERTIFICATE_STATUS, state_id: stateId };
    result = await this.dbObjService.findOneByMultipleWhere(this.userResaleCertificateRepository, userResaleCertificateConditions);
    return result;
  }

  async getUserPurchaseOrderLinesData(userPurchaseOrderId: string) {
    let result = null;
    //  find all po lines in user_purchase_order_line table with the help of purchase_order_id
    result = await this.dbObjService.findMany(this.userPurchaseOrderLineRepository, 'purchase_order_id', userPurchaseOrderId);
    return result;
  }

  async setUserPurchaseOrderLinesData(purchaseOrderLineIds: string[]) {
    let result = null;
    const userPurchaseOrderUpdateConditions = { "id": purchaseOrderLineIds };
    result = await this.dbObjService.updateByMultipleWhereIn({ sales_tax: 0 }, userPurchaseOrderUpdateConditions, this.userPurchaseOrderLineRepository);
    return result;
  }

  async getUserPurchaseOrderLedgerData(purchaseOrderLineIds: string[]) {
    let result = null;
    // find order ledger in user_purchase_order_ledger table with the help of purchase_order_line_id
    result = await this.dbObjService.findManyByWhereIn(
      this.userPurchaseOrderLedgerRepository,
      'purchase_order_line_id',
      purchaseOrderLineIds,
    );
    return result;
  }

  async setUserPurchaseOrderLedgerData(userPurchaseOrderLedgers: any[]) {
    let result = null;
    // set sales tax null in order ledger in user_purchase_order_ledger table with the help of purchase_order_line_id
    result = await this.dbObjService.saveData(userPurchaseOrderLedgers, this.userPurchaseOrderLedgerRepository);
    return result;
  }

  async setBryzosCreditLimitData(companyBuyNowPayLaterResult: any, isCheckoutViaBalance: string, data: any, buyerId: string, salesTax: number) {
    let result = null;
    let creditLimit: number;
    let availableCreditLimit: number;
    let outstandingCreditLimit: number;

    if (isCheckoutViaBalance === Constants.ON) {
      /*  creditLimit = +companyBuyNowPayLaterResult.balance_credit_limit;
       availableCreditLimit = +companyBuyNowPayLaterResult.balance_available_credit_limit + salesTax;
       outstandingCreditLimit = +companyBuyNowPayLaterResult.balance_outstanding_credit_limit - salesTax;

       response = this.dbObjService.updateWithoutMapper({ balance_available_credit_limit: availableCreditLimit, balance_outstanding_credit_limit: outstandingCreditLimit }, 'id', companyBuyNowPayLaterResult.id, this.companyBuyNowPayLaterRepository);

       if (!response) {
         response = { [responseErrorTag]: 'Something went wrong!' };
       } */
    } else if (isCheckoutViaBalance === Constants.OFF) {
      creditLimit = Number(companyBuyNowPayLaterResult.bryzos_credit_limit);
      availableCreditLimit = Number(companyBuyNowPayLaterResult.bryzos_available_credit_limit) + salesTax;
      outstandingCreditLimit = Number(companyBuyNowPayLaterResult.bryzos_outstanding_credit_limit) - salesTax;

      const companyBuyNowPayLaterUpdateDTO = { bryzos_available_credit_limit: availableCreditLimit, bryzos_outstanding_credit_limit: outstandingCreditLimit };
      const companyBuyNowPayLaterUpdateResult = await this.dbObjService.updateWithoutMapper(companyBuyNowPayLaterUpdateDTO, 'id', companyBuyNowPayLaterResult.id, this.companyBuyNowPayLaterRepository);

      if (!companyBuyNowPayLaterUpdateResult) {
        return result;
      }

      data.credit_limit = creditLimit;
      data.available_credit_limit = availableCreditLimit;
      data.outstanding_credit_limit = outstandingCreditLimit;

      result = await this.dbObjService.saveWithOutMapper(data, buyerId, this.logBryzosCreditLimitRepository);
      return result;
    }
  }

  async setAdminLogRemoveSalesTaxData(data: any, buyerId: string) {
    let result = null;
    result = await this.dbObjService.saveWithOutMapper(data, buyerId, this.adminLogRemoveSalesTaxRepository);
    return result;
  }

  async setTaxExemptedPurchaseOrders(data: any) {
    let result = null;
    // set sales tax null in user_purchase_order table with the help of buyer_po_number
    result = await this.dbObjService.saveData(data, this.taxExemptedPoRepository);
    return result;
  }
}
