import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, ValidateNested, IsBoolean, IsNumber, IsArray } from "class-validator";
import { BaseDto } from "src/base.dto";

export class CloseOrderDto {
    @ApiProperty() @IsNotEmpty()  @IsString() po_number: string;
    @ApiProperty() @IsNotEmpty()  purchase_order_line_id: Record<string, string>;
}

export class SaveCloseOrderDto extends BaseDto {
    @Type(() => CloseOrderDto)
    @ApiProperty() data: CloseOrderDto;
}
export class PurchaseOrderLineDto {
  @ApiProperty() @IsNotEmpty() @IsString() id: string;

  @ApiProperty() @IsNotEmpty() @IsNumber() po_line: number;

  @ApiProperty() @IsNotEmpty() @IsNumber() buyer_paid_amt: number;
}

export class CloseAchOrderDto {
  @ApiProperty() @IsNotEmpty() @IsString() po_number: string;

  @ApiProperty() @IsNotEmpty() @IsArray()
  @Type(() => PurchaseOrderLineDto)
  purchase_order_line: PurchaseOrderLineDto[];
}

export class SaveAchCloseOrderDto extends BaseDto {
  @Type(() => CloseAchOrderDto)
  @ApiProperty() data: CloseAchOrderDto;
}

