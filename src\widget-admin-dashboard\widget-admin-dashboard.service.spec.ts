import { Test, TestingModule } from '@nestjs/testing';
import { WidgetAdminDashboardService } from './widget-admin-dashboard.service';

describe('WidgetAdminDashboardService', () => {
  let service: WidgetAdminDashboardService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [WidgetAdminDashboardService],
    }).compile();

    service = module.get<WidgetAdminDashboardService>(WidgetAdminDashboardService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
