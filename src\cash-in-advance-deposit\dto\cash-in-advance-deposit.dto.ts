import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsOptional } from "class-validator";
import { BaseDto } from "src/base.dto";

export class GlobalDepositDto {
    @ApiProperty() @IsNotEmpty() deposit_percentage: number;
    @ApiProperty() @IsNotEmpty() @IsString() note: string;
}

export class SaveGobalDepositDto extends BaseDto {
    @Type(() => GlobalDepositDto)
    @ApiProperty() data: GlobalDepositDto;
}

export class CustomDepositDto {
    @ApiProperty() @IsNotEmpty() @IsString() user_id: string;
    @ApiProperty() @IsNotEmpty() deposit_percentage: number;
    @ApiProperty() @IsOptional() @IsString() admin_id: string;    
}


export class SaveCustomDepositDto extends BaseDto {
    @Type(() => CustomDepositDto)
    @ApiProperty() data: CustomDepositDto;
}