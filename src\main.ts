import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { GlobalExceptionFilter, ReferenceDataSettings } from '@bryzos/extended-widget-library';
import { SecurityMiddleware } from '@bryzos/base-library';
import { json, urlencoded } from 'express';
import { getRepositoryToken } from '@nestjs/typeorm';

async function bootstrap() {

	const app = await NestFactory.create(AppModule, { bodyParser: false });
	app.enableCors();

	// Get the ReferenceDataSettings repository from the dependency injection container
	const referenceDataSettingsRepository = app.get(getRepositoryToken(ReferenceDataSettings));
  
	// Instantiate SecurityMiddleware with the required repository
	const securityMiddleware = new SecurityMiddleware(referenceDataSettingsRepository);
  
	// Apply SecurityMiddleware first
	app.use(securityMiddleware.use.bind(securityMiddleware));
	
	// Then apply body parsing
	app.use(json({ limit: process.env.REQUEST_JSON_SIZE + "mb" }));
	app.use(urlencoded({ limit: process.env.REQUEST_JSON_SIZE + "mb", extended: true }));

	// Apply ValidationPipe after body parsing
	app.useGlobalPipes(new ValidationPipe());

	const configService = app.get(ConfigService);
  
	app.useGlobalFilters(new GlobalExceptionFilter());

	app.listen(configService.get<string>('PORT'), () => console.log(`Listening on port `, configService.get<string>('PORT')));
}
bootstrap();
