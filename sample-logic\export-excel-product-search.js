#!/usr/bin/env node

// Import required libraries
const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);

// Show usage information if arguments are missing
if (args.length < 3) {
  console.log(
    'Usage: node export-excel-product-search.js <input_excel_path> <sheet_name> <output_excel_filename>'
  );
  console.log(
    'Example: node export-excel-product-search.js ./V107.xlsx "Sheet1" product_search.xlsx'
  );
  console.log(
    'Note: Output files will be saved in the excel-exports directory'
  );
  process.exit(1);
}

// Extract arguments
const inputFilePath = args[0];
const sheetName = args[1];
const outputFilePath = args[2];

// Hardcoded column mappings for product search data
const columnMappings = [
  { source: 'Shape ID', target: 'Shape_ID' },
  { source: 'Product ID', target: 'Product_ID' },
  { source: 'Order_Increment_(Ft)', target: 'Order_Increment_Ft' },
  { source: 'Order_Increment_(Ea)', target: 'Order_Increment_Ea' },
  { source: 'Order_Increment_(Lb)', target: 'Order_Increment_Lb' },
  { source: 'Order_Increment_(CWT)', target: 'Order_Increment_CWT' },
  { source: 'Order_Increment_(Net_Ton)', target: 'Order_Increment_Net_Ton' },
  { source: 'Key_1', target: 'Key1' },
  { source: 'Key_2', target: 'Key2' },
  { source: 'Key_3', target: 'Key3' },
  { source: 'Key_4', target: 'Key4' },
  { source: 'Key_5', target: 'Key5' },
  { source: 'Key_6', target: 'Key6' },
  { source: 'Key_7', target: 'Key7' },
  { source: 'Key_8', target: 'Key8' },
  { source: 'Key_9', target: 'Key9' },
  { source: 'Key_10', target: 'Key10' },
  { source: 'Key_11', target: 'Key11' },
  { source: 'Key_12', target: 'Key12' },
  { source: 'Key_13', target: 'Key13' },
  { source: 'Key_14', target: 'Key14' },
  { source: 'Key_15', target: 'Key15' },
  { source: 'Key_16', target: 'Key16' },
  { source: 'Key_17', target: 'Key17' },
  { source: 'Key_18', target: 'Key18' },
  { source: 'Key_19', target: 'Key19' },
  { source: 'Key_20', target: 'Key20' },
  { source: 'Key_21', target: 'Key21' },
  { source: 'Key_22', target: 'Key22' },
  { source: 'Key_23', target: 'Key23' },
  { source: 'Key_24', target: 'Key24' },
  { source: 'UI_Description', target: 'UI_Description' },
  { source: 'QUM_Dropdown_Options', target: 'QUM_Dropdown_Options' },
  { source: 'PUM_Dropdown_Options', target: 'PUM_Dropdown_Options' },
  { source: 'Domestic Enabled', target: 'domestic_material_only' },
];

// Extract source column names for lookup in the Excel file
const columnsToExtract = columnMappings.map((mapping) => mapping.source);

// Function to export Excel sheet to a new Excel file using streaming API
async function exportExcelSheetToExcel( inputFilePath, sheetName, outputFilePath, columnsToExtract, columnMappings ) {
  try {
    // Check if input file exists
    if (!fs.existsSync(inputFilePath)) {
      console.error(
        `Error: Input file does not exist at path: ${inputFilePath}`
      );
      process.exit(1);
    }

    console.log(`Reading input file: ${inputFilePath}`);
    console.log(`Looking for sheet: ${sheetName}`);
    console.log(`Columns to extract: ${columnsToExtract.join(', ')}`);
    console.log(
      `Adding new column: is_safe_product_code (1 for Product_ID 421020001 or 422020001, 0 otherwise)`
    );

    // Display column mappings
    console.log('Column mappings:');
    columnMappings.forEach((mapping) => {
      if (mapping.source !== mapping.target) {
        console.log(`  ${mapping.source} → ${mapping.target}`);
      }
    });

    // Create a workbook reader for input file
    const workbookReader = new ExcelJS.stream.xlsx.WorkbookReader(
      inputFilePath,
      {
        sharedStrings: 'cache',
        hyperlinks: 'ignore',
        worksheets: 'emit',
        formulas: 'shared',
        styles: 'ignore',
      }
    );

    // Create a new workbook for output
    const outputWorkbook = new ExcelJS.Workbook();
    const outputWorksheet = outputWorkbook.addWorksheet(sheetName);

    let foundSheet = false;
    let rowCount = 0;
    let headerIndices = {};
    let outputRowCount = 0;

    // Process each worksheet in the input file
    for await (const worksheetReader of workbookReader) {
      // Check if this is the sheet we're looking for
      if (worksheetReader.name === sheetName) {
        foundSheet = true;
        console.log(`\nFound sheet: ${sheetName}`);

        // Process each row
        for await (const row of worksheetReader) {
          rowCount++;

          // Process header row (first row)
          if (rowCount === 1) {
            // Map column names to indices
            row.values.forEach((value, index) => {
              if (value) {
                // Normalize the column name for comparison
                const normalizedValue = String(value).trim();

                // Check if this column is one we want to extract
                for (const colName of columnsToExtract) {
                  // Normalize the column name for comparison
                  const normalizedColName = String(colName).trim();

                  if (normalizedValue === normalizedColName) {
                    headerIndices[colName] = index;
                    break;
                  }
                }
              }
            });

            // Check if all required columns exist
            const missingColumns = columnsToExtract.filter(
              (col) => !(col in headerIndices)
            );
            if (missingColumns.length > 0) {
              console.error(
                `Error: The following required columns are missing: ${missingColumns.join(
                  ', '
                )}`
              );
              console.log(
                'Available columns:',
                row.values.filter(Boolean).join(', ')
              );
              process.exit(1);
            }

            console.log(`Found all required columns. Processing data...`);

            // Add header row to output worksheet with target column names
            const targetColumnNames = columnMappings.map(
              (mapping) => mapping.target
            );
            // Add the new is_safe_product_code column
            targetColumnNames.push('is_safe_product_code');
            outputWorksheet.addRow(targetColumnNames);
            outputRowCount++;

            continue; // Skip to next row
          }

          // Extract values for each column
          const values = columnsToExtract.map((colName) => {
            const cellIndex = headerIndices[colName];
            if (cellIndex === undefined) return null;

            const cellValue = row.values[cellIndex];

            // Handle formula results - get the calculated value
            if (cellValue && typeof cellValue === 'object') {
              if (cellValue.formula) {
                return cellValue.result;
              } else if (cellValue.text) {
                return cellValue.text;
              } else if (cellValue.value !== undefined) {
                return cellValue.value;
              }
            }

            return cellValue;
          });

          // Find the Product_ID value to determine is_safe_product_code
          const productIdIndex = columnsToExtract.findIndex(
            (col) => col === 'Product ID'
          );
          let productId = values[productIdIndex];

          // Handle formula results or objects for Product ID
          if (productId && typeof productId === 'object') {
            if (productId.formula) {
              productId = productId.result;
            } else if (productId.text) {
              productId = productId.text;
            } else if (productId.value !== undefined) {
              productId = productId.value;
            }
          }

          // Convert to string for comparison
          productId = String(productId || '');

          // Set is_safe_product_code to 1 if Product_ID is 421020001 or 422020001, otherwise 0
          const isSafeProductCode =
            productId === '421020001' || productId === '422020001' ? 1 : 0;

          // Add the is_safe_product_code value to the row
          values.push(isSafeProductCode);

          // Add row to output worksheet
          outputWorksheet.addRow(values);
          outputRowCount++;

          // Log progress
          if (rowCount % 1000 === 0) {
            console.log(`Processed ${rowCount} rows...`);
          }
        }

        console.log(`\nProcessed ${rowCount} rows from input file.`);
        console.log(
          `Added ${outputRowCount} rows to output file (including header).`
        );

        break; // Exit the worksheet loop once we've found and processed our target sheet
      }
    }

    if (!foundSheet) {
      console.error(`Error: Sheet "${sheetName}" not found in the workbook.`);
      process.exit(1);
    }

    // Ensure the excel-exports directory exists
    const exportDir = 'excel-exports';
    if (!fs.existsSync(exportDir)) {
      console.log(`Creating directory: ${exportDir}`);
      fs.mkdirSync(exportDir);
    }

    // Prepare the output path in the excel-exports directory
    const outputFileName = path.basename(outputFilePath);
    const finalOutputPath = path.join(exportDir, outputFileName);

    // Save the output workbook
    console.log(`\nSaving output file to: ${finalOutputPath}`);
    await outputWorkbook.xlsx.writeFile(finalOutputPath);
    console.log(`Successfully exported data to ${finalOutputPath}`);
  } catch (error) {
    console.error('An error occurred while processing the Excel file:');
    console.error(error.message);
    process.exit(1);
  }
}

// Execute the function
(async () => {
  await exportExcelSheetToExcel(
    inputFilePath,
    sheetName,
    outputFilePath,
    columnsToExtract,
    columnMappings
  );
})();
