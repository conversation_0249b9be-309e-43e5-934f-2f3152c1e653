import { Controller, Get, Response, Post, Body, Patch, Param, Delete, UsePipes, ValidationPipe, Request, Query } from '@nestjs/common';
import { Constants } from 'src/Constants';
import { OrderService } from './order.service';
import { SaveNewOrderLinesDto, SaveSalesTaxOrderDto, UpdateOrderLinesDto } from './dto/order.dto';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@Controller('order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}
  
  @Post('addNewOrderLines')
  @UsePipes(ValidationPipe)
  async addNewOrderLines(@Body() saveNewOrderLines:SaveNewOrderLinesDto, @Response() res){
    let payloadData = saveNewOrderLines[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.orderService.addNewOrderLines(payloadData, adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('updateOrderLine')
  @UsePipes(ValidationPipe)
  async updateOrderLine(@Body() updateOrderLines: UpdateOrderLinesDto, @Response() res){
    let payloadData = updateOrderLines[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.orderService.updateOrderLineData(payloadData, adminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('salesTaxCalculate')
  @UsePipes(ValidationPipe)
  async salesTax(@Body() referencedata:SaveSalesTaxOrderDto, @Response() res){
    let payloadData = referencedata[payloadTag];
    let adminId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.orderService.salesTaxCalculate(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
  
  @Get('get-delivery-dates')
    async deliveryDate(@Query('id') id: string, @Query('timestamp') timestamp: string, @Response() res) {
      let responseData = {
        [responseTag]: await this.orderService.deliveryDate(id,timestamp)
      };
      res.locals.responseData = responseData;
      return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
    }
}
