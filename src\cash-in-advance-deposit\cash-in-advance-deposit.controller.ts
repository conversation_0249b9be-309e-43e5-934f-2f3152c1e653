import { Controller, Get, Post, Body, Response, ValidationPipe, UsePipes} from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { Constants } from 'src/Constants';
import { CashInAdvanceDepositService } from './cash-in-advance-deposit.service';
import { GlobalDepositDetails, CustomDepositDetails, GlobalDepositDetailsSwaggerEntity, CustomDepositDetailsSwaggerEntity} from '@bryzos/extended-widget-library';
import { SaveGobalDepositDto, SaveCustomDepositDto } from './dto/cash-in-advance-deposit.dto';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@ApiTags('Cash In Advance Deposit')
@Controller('cash_in_advance')
export class CashInAdvanceDepositController {
  constructor(private cashInAdvanceDepositService: CashInAdvanceDepositService) {}

  @ApiResponse({
    description: 'This is global deposit data',
    type: GlobalDepositDetailsSwaggerEntity,
    status: 200
  })
  @Get('/global_deposit')
  async findAll(@Response() res) {

    let responseData = {
      [responseTag]: await this.cashInAdvanceDepositService.findAll()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Save global deposit data',
    type: GlobalDepositDetailsSwaggerEntity,
    status: 200
  })
  @Post('global_deposit')
  @UsePipes(ValidationPipe)
  async saveGlobalDepositData(@Body() globalDepositDto:SaveGobalDepositDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = globalDepositDto[payloadTag];

    let responseData = {
      [responseTag]: await this.cashInAdvanceDepositService.saveGlobalDepositData(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'This is custom deposit data',
    type: CustomDepositDetailsSwaggerEntity,
    status: 200
  })

  @Get('/custom_deposit')
  async findAllCustomDeposit(@Response() res) {
    let responseData = {
      [responseTag]: await this.cashInAdvanceDepositService.findAllCustomDepositData()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @ApiResponse({
    description: 'Save custom deposit data',
    type: CustomDepositDetailsSwaggerEntity,
    status: 200
  })
  @Post('custom_deposit')
  @UsePipes(ValidationPipe)
  async saveCustomDepositData(@Body() customDepositDto:SaveCustomDepositDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = customDepositDto[payloadTag];
    let responseData = {
      [responseTag]: await this.cashInAdvanceDepositService.saveCustomDepositData(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
