import { Module, MiddlewareConsumer } from '@nestjs/common';
import { OrderController } from './order.controller';
// import { LoggerMiddleware } from 'src/middleware/logger.middleware';
import {  AdminPermissionMiddleware } from '@bryzos/base-library';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,HttpModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [OrderController],
//   providers: OConstants.ServiceArray
// })
@Module({
  imports: [SharedModule],
  controllers: [OrderController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class OrderModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
    .apply(AdminPermissionMiddleware, LoggerMiddleware)
    .forRoutes('/order');
	}
}
