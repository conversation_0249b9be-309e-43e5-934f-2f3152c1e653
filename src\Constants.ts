export class Constants {
    public static PAYLOAD_TAG = "data";
    public static RESPONSE_TAG = "data";
    public static PAGINATION_RESPONSE_TAG = "meta"
    public static RESPONSE_CODE_OK = 200;
    public static ORDER_CANCELLATION_REMINDER_HOURS = "ORDER_CANCELLATION_REMINDER_HOURS";
    public static ORDER_CANCELLATION_HOURS = "ORDER_CANCELLATION_HOURS";
    public static ORDERCANCELLED = "OrderCancelled";
    public static TRANSACTION_TYPE_BA_RCDC = "BA_RCDC";
    public static TRANSACTION_TYPE_USER_RCDC = "RCDC";
    public static CLAMIED_BY_PENDING_STATUS = 'PENDING';
    public static CLAMIED_BY_READY_TO_CLAIM = 'READY_TO_CLAIM';
    public static BUYER_ORDER_CANCEL = 'BUYER_ORDER_CANCEL';
    public static SELLER_ORDER_CANCEL = 'SELLER_ORDER_CANCEL';
    public static PAYMENT_METHOD_BRYZOS_PAY = 'bryzos_pay';
    public static VENDOR_BALANCE = 'BALANCE';
    public static BALANCE_SECRET_KEY = 'SECRET_KEY';
    public static ERROR_TAG='error_message';
    public static SUPER_ADMIN_USER = 'ADMIN';
    public static USER = 'USER';
    public static USER_DELETED = 'USER_DELETED';
    public static DYSPATCH_VENDOR = 'DYSPATCH';
    public static DYSPATCH_KEY = 'API_KEY';
    public static ACTIVE = "Active";
    public static RESOLVED = "Resolved";
    public static PAYMENT_METHOD_ACH_CREDIT = 'ach_credit';
    public static IN_PROGRESS = 'IN_PROGRESS';
    public static ORDER_STATUS_COMPLETED = 'Completed';
    public static ORDER_CANCEL_SUCCESS = 'Order canceled successfully';
    public static ORDER_COMPLETED = "Completed";


    public static FUNDS_ON_WAY_SELLER = 'FUNDS_ON_WAY_SELLER';
    public static ADMIN_DASHBOARD_EMAIL_GENERATE = 'ADMIN_DASHBOARD_EMAIL_GENERATE';
    public static IN_DISPUTE = 'In-dispute';
    public static PAYMENT_PAID = 'PAYMENT_PAID';
    public static CHECKOUT_VIA_BALANCE = 'CHECKOUT_VIA_BALANCE';
    public static OFF = 'OFF';
    public static ON = 'ON';
    public static UPDATE_QTY_VIA_AD = 'UPDATE_QTY_VIA_AD';

    public static INVOICE_FOR_ACH_ORDER="INVOICE_FOR_ACH_ORDER";
    public static INVOICE_FOR_ACH_ORDER_YES="YES";
    public static ADMIN_DASHBOARD_BUYER_INVOICE = 'WIDGET_SERVICE_BUYER_INVOICE_VIA_AD';
    public static AWS_VENDOR = 'AWS';
    public static AWS_KEY = 'CREDENTIAL';
    public static USER_RESALE_CERTIFICATE_STATUS = 'Approved';

    public static WIDGET_SERVICE_BUYER_PO = 'WIDGET_SERVICE_BUYER_PO';
    public static WIDGET_SERVICE_SELLER_PO = 'WIDGET_SERVICE_SELLER_PO';
    public static NOTIFICATION_SELLER_ORDER_CANCEL = 'NOTIFICATION_SELLER_ORDER_CANCEL';
    public static NOTIFICATION_SELLER_ORDER_LINE_CANCEL = 'NOTIFICATION_SELLER_ORDER_LINE_CANCEL';

    public static NOTIFICATION_BUYER_ORDER_CANCEL = 'NOTIFICATION_BUYER_ORDER_CANCEL';
    public static NOTIFICATION_BUYER_ORDER_LINE_CANCEL = 'NOTIFICATION_BUYER_ORDER_LINE_CANCEL';
    public static BALANCE_BUYER_ID = 'balance_buyer_id'; 
    public static COMMUNICATION_CONFIG_TO_EMAIL = 'COMMUNICATION_CONFIG_TO_EMAIL';
    public static COMMUNICATION_CONFIG_BCC_EMAIL = 'COMMUNICATION_CONFIG_BCC_EMAIL';
    public static BALANCE_PLAN_TYPE = 'BALANCE_PLAN_TYPE';
    public static BALANCE_LINE_ITEM_TYPE = 'PHYSICAL';
    public static BALANCE_AUTO_PAYOUT = 'BALANCE_AUTO_PAYOUT';
    public static VENDOR_NAME_BALANCE = 'BALANCE';
    public static DEFAULT_VENDOR = 'DEFAULT_VENDOR_ID';
    public static BALANCE_ACCOUNT_APPROVED = 'APPROVED'
    public static BALANCE_ACCOUNT_REJECTED = 'REJECTED'
    public static BALANCE_ACCOUNT_PENDING = 'PENDING'
    public static LOGGLY_LOG = 'LOGGLY_LOG'
    public static LOGGING_INFO ='LOGGING_INFO';
    public static ADD_COLUMN_VALUE_FOR_SELLER_FID = "ADD_COLUMN_VALUE_FOR_SELLER_FID";
    public static PG_GATEWAY_BNPL_SELLER_FID_ADD_DAYS = "PG_GATEWAY_BNPL_SELLER_FID_ADD_DAYS";
    public static CUSTOM_NOTIFICATION = "CUSTOM_NOTIFICATION";
    public static CUSTOM_NOTIFICATION_PRIORTY = ["LOW", "MEDIUM", "HIGH"];
    public static CUSTOM_NOTIFICATION_ACTION = ["REFRESH", "CLOSE"];
    public static CUSTOM_NOTIFICATION_SEND_TO = ["ALL", "BUYERS", "SELLERS", "USERS",];
    public static TAX_EXEMPTED_EVENT_REMOVE_SALES_TAX = 'REMOVE_SALES_TAX';
    public static TAX_EXEMPTED_EVENT_UPDATE_QTY = 'UPDATE_QTY';
    public static USER_UPDATE_SPREAD_DATA ='AD_UPDATE_USER_SPREAD_DATA';
    public static COMPANY_UPDATE_SPREAD_DATA ='AD_UPDATE_COMPANY_SPREAD_DATA';
    public static COHORT_UPDATE_SPREAD_DATA ='AD_UPDATE_COHORT_SPREAD_DATA';
    public static BUYER_PRICING ='Buyer_Pricing';
    public static NEUTRAL_PRICING ='Neutral_Pricing';
    public static SELLER_PRICING ='Seller_Pricing';
    public static NOTIFICATION_USER_SPREAD_PRICE_CHANGE = "USER_SPREAD_PRICE_CHANGE"; 
    public static DEFAULT_DISCOUNT_PERCENTAGE = 'DEFAULT_DISCOUNT_PERCENTAGE';
    public static DEFAULT_DISCOUNT_TIME_PERIOD = 'DEFAULT_DISCOUNT_TIME_PERIOD';
    public static DISCOUNT_PHASEOUT_PERIOD = 'DISCOUNT_PHASEOUT_PERIOD';
    public static COMPLETED = 'Completed';
    public static IS_DISCOUNT = 'IS_DISCOUNT';
    public static PRICING_COLUMN = 'PRICING_COLUMN';
    public static UPDATE_BUYER_EXT_VIA_ADMIN = "UPDATE_BUYER_EXT_VIA_ADMIN";
    public static DEFAULT_DISCOUNT_UPDATE_DATA ='AD_UPDATE_DEFAULT_DISCOUNT_DATA';
    public static ORDER_STATUS_RESOLVED="Resolved";
    public static ORDER_STATUS_ACTIVE="Active";
    public static EXPIRED='Expired';
    public static BOX_4_BNPL = 'bnpl';
    public static CHECK_PAYMENT_METHOD = 'CHECK';
    public static CASS_FILE_RECORD_TYPE = '0000';
    public static CASS_TRANSACTION_RECORD_TYPE = '0001';
    public static CASS_PAYMENT_RECORD_TYPE = '2201';
    public static CASS_BNPL_PAYMENT = 'CASS_BNPL_PAYMENT';
    public static WIRE_DISBURSEMENT_RECORD_TYPE = '2200';
    public static ENCRYPTION_KEY = 'ENCRYPTION';
    public static ENCRYPTION_TYPE = 'ENCRYPTION_TYPE';
    public static VENDOR_NAME_CASS_SFTP = 'CASS_SFTP';
    public static PASSWORD = 'PASSWORD';
    public static LOCAL_ENV = 'local';
    public static CASS_TEST_DIR = 'to_cass_test';
    public static PROD_ENV = 'prod';
    public static CASS_PROD_DIR = 'to_cass';
    public static NOT_UPLOADED = 'NOT_UPLOADED';
    public static UPLOADED = 'UPLOADED';
    public static TARGET_DIR_NOT_FOUND = 'Target Directory Not Found!';
    public static FILE_UPLOAD_EXCEPTION = 'FILE UPLOAD EXCEPTION';
    public static LOGIN_FAILED = 'LOGIN FAILED';
    public static BOX_1_SELLER_PAYMENT = 'seller_payout';
    public static CASS_SELLER_PAYOUT = 'CASS_SELLER_PAYOUT';
    public static BOX_2_BRYZOS_HOLDINGS = 'bryzos_holdings';
    public static CASS_BRYZOS_FEE_PAYOUT = 'CASS_BRYZOS_FEE_PAYOUT';
    public static BOX_3_SALES_TAX = 'sales_tax';
    public static CASS_SALES_TAX_PAYOUT = 'CASS_SALES_TAX_PAYOUT';
    public static VENDOR_NAME_CASS = 'CASS';
    public static BUYER = "BUYER";
    public static CASS_BUYER_UNIQUE_IDENTIFIER_PREFIX = 'CASS_BUYER_UNIQUE_IDENTIFIER_PREFIX';
    public static CASS_UNIQUE_IDENTIFIER_TAG = 'CASS_UNIQUE_IDENTIFIER_TAG';
    public static SELLER = "SELLER";
    public static DAN = 'DEFAULT_ACCOUNT_NUMBER';
    public static DRN = 'DEFAULT_ROUTING_NUMBER';
    public static CASS_BUYER_MASTER_DATA_TYPE = 'Buyer';
    public static CASS_BUYER_DRAWDOWN_TRANSACTION_CODE = '27';
    public static SK = 'SECRET_KEY';
    public static SECRET_MANAGER = 'SECRET_MANAGER';
    public static CASS_TRANSFER_SALES_TAX_ID = 'SALES_TAX_ID';
    public static CASS_TRANSFER_BNPL = 'BNPL';
    public static CASS_TRANSFER_BRYZOS_HOLDINGS = 'BRYZOS_HOLDINGS';
    public static CASS_SELLER_UNIQUE_IDENTIFIER_PREFIX = 'CASS_SELLER_UNIQUE_IDENTIFIER_PREFIX';

    public static WIDGET_BUY_NOW_PAY_LATER = 'BUY_NOW_PAY_LATER';
    public static WIDGET_BALANCE = 'BALANCE';
    public static REQUESTED_CREDIT_PENDING_INCREASE = 'Pending Increase';
    public static PENDING = 'Pending';
    public static BRYZOS_PAY_ADMIN_EMAIL = 'BRYZOS_PAY_ADMIN_EMAIL';

    public static RESALES_APPROVED = "Approved";
    public static RESALES_REJECTED = "Rejected";
    public static RESALES_PENDING = "Pending";
    public static APPROVE="APPROVE";
    public static UNAPPROVE="UNAPPROVE";
    public static CASS_BUYER_ID = "BUYER_ID";
    public static SALES_TAX_EXEMPTION_APPROVED = "Approved";
    public static SALES_TAX_MARKET_PLACE_LEGISLATION_YES = "Yes";
    public static DELIVERED = "Delivered";
    public static AVAILABLE_CREDIT_LIMIT_EXCEED = 'AVAILABLE_CREDIT_LIMIT_EXCEED';
    public static BNPL_NOT_SET_UP = 'BNPL_NOT_SET_UP';
    public static BNPL_REQUEST_NOT_APPROVED = 'BNPL_REQUEST_NOT_APPROVED';
    public static BUYER_PURCHASE_ORDER_INITIAL = 'S';
    public static QUOTE_NUMBER_INITIAL = 'Q';
    public static TAX_EXEMPTED_EVENT_ADD_NEW_LINES = 'ADD_NEW_LINES';
    public static ADD_NEW_LINES_CREATED = 'ADD_NEW_LINES_CREATION';
    public static DEFAULT_SELLER_SPREAD_RATE = 'DEFAULT_SELLER_SPREAD_RATE';
    public static FILE_TYPE_ACH_FUNDING_STATUS='0520';
    public static FILE_TYPE_WIRE_FUNDING_STATUS='0530';
    public static CASS_PAYMENT_SUCCESS = 'SUCCESS';
    public static CASS_TRANSACTION_FUNDS_ON_WAY = 'CASS_TRANSACTION_FUNDS_ON_WAY';
    public static BOX_5_ADHOC_PAYMENT = 'adhoc_seller_payout';
    public static APPROVED = 'Approved';
    public static YES = 'Yes';
    public static CLOSE_PO_FROM_CASS_MAPPING = 'CLOSE_PO_FROM_CASS_MAPPING';

    public static WIDGET_SERVICE_REFERENCE_DATA = "WIDGET_SERVICE_REFERENCE_DATA";
    public static HOMEPAGE_WIDGET_SERVICE_REFERENCE_DATA = "HOMEPAGE_WIDGET_SERVICE_REFERENCE_DATA";
    public static MODIFY_VIA_AD = 'MODIFY_VIA_AD';
    public static REJECT="REJECT";
    public static MODIFY="MODIFY";
    public static APPROVED_VIA_AD = 'APPROVED_VIA_AD';
    public static ACH_CREDIT_APPROVE_REJECT = 'ACH_CREDIT_APPROVE_REJECT';
    public static GET_CREDIT_LIMIT_ERROR_TAG = 'GET_CREDIT_LIMIT_ERROR_TAG';
    public static SECURITY_SECRET_KEY = 'SECURITY_SECRET_KEY';

    public static ONBOARD_TYPE_PENDING_REQUEST = 'PENDING_REQUEST';
    public static ONBOARD_TYPE_AD_CREATE_USER = 'ADMIN_CREATE_USER';
    public static BUYERROLE = 'BUYER';
    public static SELLERROLE = 'SELLER';
    public static AWS_USER_ATTRIBUTE_EMAIL = 'email';
    public static AWS_USER_ATTRIBUTE_EMAIL_VERIFIED = 'email_verified';

    public static Beta1_OnBoardScreen = 'Beta1_OnBoardScreen';
    public static Beta1_CreateUser = 'Beta1_CreateUser';
    public static NOTIFICATION_SELLER_NEW_BUYER_ADDED = 'NOTIFICATION_SELLER_NEW_BUYER_ADDED';
    public static NEW_USER_SIGNUP = 'NEW_USER_SIGNUP';
   
    public static BUYER_INVOICE_GENERATE_PDF_ONLY = "BUYER_INVOICE_GENERATE_PDF_ONLY";
    public static PROD_ENVIRONMENT = 'production';
    public static INTERNAL_NOTIFICATION='INTERNAL_NOTIFICATION';
    public static SEARCH_FILE_TYPE = 'search';
    public static PRICING_FILE_TYPE = 'pricing';

}