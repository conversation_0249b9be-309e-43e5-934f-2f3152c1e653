import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserPurchaseOrder, UserPurchaseOrderLine, UserPurchaseOrderLedger, ReferenceDataOrderStatus, ReferenceEmailEvent, AdminLogEmailGenerate } from '@bryzos/extended-widget-library';
import { DataBaseService, Constants } from '@bryzos/base-library';
import { AwsQueue } from 'src/AwsQueue';
import { WidgetAdminDashboardService } from 'src/widget-admin-dashboard/widget-admin-dashboard.service';
import { SendEmailDataDto } from './dto/email_generate.dto';

@Injectable()
export class EmailGenerateService {
  private dbObjService = new DataBaseService();

  constructor(
    private readonly aws:AwsQueue,
    private readonly widgetAdminDashboardService: WidgetAdminDashboardService,
    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(UserPurchaseOrderLedger) private readonly userPurchaseOrderLedgerRepository: Repository<UserPurchaseOrderLedger>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly referenceDataOrderStatusRepository: Repository<ReferenceDataOrderStatus>,
    @InjectRepository(ReferenceEmailEvent) private readonly referenceEmailEventRepository: Repository<ReferenceEmailEvent>,
    @InjectRepository(AdminLogEmailGenerate) private readonly adminLogEmailGenerateRepository: Repository<AdminLogEmailGenerate>,
  ){}

    async getEmailEvents(){
        const emailEvents = await this.dbObjService.findAll(this.referenceEmailEventRepository);
        if(emailEvents){
            return emailEvents;
        }else{
            return null;
        }
    }

    async orderDetails(poNumber:string) {
        const payloadPonumber = poNumber;
        poNumber = await this.sanatizePoNumber(poNumber);

        const get_order_cancel_status = await this.dbObjService.findOne(this.referenceDataOrderStatusRepository,"value",Constants.ORDER_STATUS_CANCELLED);
        const get_order_dispute_status = await this.dbObjService.findOne(this.referenceDataOrderStatusRepository,"value",Constants.ORDER_STATUS_IN_DISPUTE);
        
        const order_status = [get_order_cancel_status.id,get_order_dispute_status.id];

        const selectedFields = ['table2.id as purchase_order_line_id','table2.po_line as po_line'];

        let conditions = {};

        const condition1_attributes = {"operator":"=","value":poNumber};
        conditions["buyer_po_number"] = condition1_attributes;

        const condition2_attributes = {"operator":"NOT IN","value":order_status,"table":"user_purchase_order_line"};
        conditions["order_status_id"] = condition2_attributes;

        const combineAndConditions = {conditions};

        const leftJoin = { "user_purchase_order_line" : "purchase_order_id" };

        const orderBy = "po_line";

        const poDetails = await this.dbObjService.FindByMultipleWhereComparisonANDLeftJoinById(this.userPurchaseOrderRepository,leftJoin,combineAndConditions,null,selectedFields,orderBy);
        if(poDetails.length > 0){
            return poDetails;
        }else{
            return {"error_message":payloadPonumber+" is not found in our list of orders. It is possible that the order has been canceled or is currently under dispute."}
        }
    }

    async sendEmailForPonumber(payload:SendEmailDataDto,adminId:string){
        const poNumber = await this.sanatizePoNumber(payload.po_number);
       
        const toEmail = payload.to_email.split(";").map(item => item.trim()).join(";"); 
        const ccEmail = payload.cc_email.split(";").map(item => item.trim()).join(";");
        const bccEmail = payload.bcc_email.split(";").map(item => item.trim()).join(";");
        let emailObject = {}
        emailObject['to_email'] = await this.emailValidation(toEmail);
        emailObject['cc_email'] = await this.emailValidation(ccEmail);
        emailObject['bcc_email'] = await this.emailValidation(bccEmail);
        
        if(emailObject['to_email'] === "" && emailObject['cc_email'] === "" && emailObject['bcc_email'] === ""){
            return {error_message : "Some of the entered email's are not valid please check"};
        }
        let insertResponse = null;

        let emailEvent = '';
        if(payload.email_type){
            emailEvent = await this.getEmailEvent(payload.email_type);
        }
        if(emailEvent == ''){return insertResponse}
        payload['po_number'] = payload.po_number.toUpperCase();

        let poLines = [];
        for(const poLine in payload.po_lines){
            poLines.push(payload.po_lines[poLine]);
        }
        const poLineString = JSON.stringify(poLines);

        const adminEmailgenerateData = {
            to_email : emailObject['to_email'],
            cc_email : emailObject['cc_email'],
            bcc_email : emailObject['bcc_email'],
            po_number : poNumber,
            event : emailEvent,
            po_lines : poLineString,
            admin_user_id : adminId
        };

        let messageAttributes = null;
        let sqsQueue = null;
        let userPurchaseOrder = await this.dbObjService.findOne(this.userPurchaseOrderRepository,"buyer_po_number",poNumber);
        
        if(emailEvent === Constants.FUNDS_ON_WAY_SELLER){
            if(userPurchaseOrder.payment_method != Constants.PAYMENT_METHOD_BRYZOS_PAY){
                return {error_message : payload['po_number']+" is not Net 30 Terms order"};
            }else if(!userPurchaseOrder.seller_id){
                return {error_message : payload['po_number']+" has not yet been claimed by any seller."};
            }else{
                messageAttributes= {
                    reference_id: {
                        DataType: 'String',
                        StringValue: poNumber,
                    },
                    email_type: {
                        DataType: 'String',
                        StringValue: Constants.ADMIN_DASHBOARD_EMAIL_GENERATE,
                    },
                    event: {
                        DataType: 'String',
                        StringValue: emailEvent,
                    },
                };
                sqsQueue = process.env.MESSAGE_NODE_EMAIL_QUEUE;
            }
        }else if(emailEvent === Constants.WIDGET_SERVICE_BUYER_PO){
            messageAttributes= {
                po_number: {
                    DataType: 'String',
                    StringValue: poNumber,
                },
                po_lines: {
                    DataType: 'String',
                    StringValue: poLineString,
                },
                event: {
                    DataType: 'String',
                    StringValue: "AD_"+emailEvent,
                },
            };
            sqsQueue = process.env.AWS_WIDGET_GENERATE_PDF_DATA_QUEUE_URL;
        }else if(emailEvent === Constants.WIDGET_SERVICE_SELLER_PO){
            if(userPurchaseOrder.payment_method === Constants.PAYMENT_METHOD_ACH_CREDIT && userPurchaseOrder.is_ach_po_approved != true){
                return {error_message : payload['po_number']+" is Cash in Advance Order and not approved by admin yet."};
            }else if(!userPurchaseOrder.seller_id){
                return {error_message : payload['po_number']+" has not yet been claimed by any seller."};
            }else{
                messageAttributes= {
                    po_number: {
                        DataType: 'String',
                        StringValue: poNumber,
                    },
                    po_lines: {
                        DataType: 'String',
                        StringValue: poLineString,
                    },
                    event: {
                        DataType: 'String',
                        StringValue: "AD_"+emailEvent,
                    },
                };
                sqsQueue = process.env.AWS_WIDGET_GENERATE_PDF_DATA_QUEUE_URL;
            }
        }else if(emailEvent === Constants.WIDGET_SERVICE_BUYER_INVOICE_VIA_AD){
        
            const response = await this.widgetAdminDashboardService.recalculateSalesTax(userPurchaseOrder, adminId);
            if(response && response.hasOwnProperty('error_message')){
              return {error_message : response.error_message};
            }
            
            messageAttributes= {
                po_number: {
                    DataType: 'String',
                    StringValue: poNumber,
                },
                po_lines: {
                    DataType: 'String',
                    StringValue: poLineString,
                },
                event: {
                    DataType: 'String',
                    StringValue: emailEvent,
                },
            };
            sqsQueue = process.env.AWS_WIDGET_GENERATE_PDF_DATA_QUEUE_URL;
        }
        
        if(messageAttributes){
            const insertAdminLog = await this.dbObjService.saveData(adminEmailgenerateData,this.adminLogEmailGenerateRepository);
            if(insertAdminLog){
                messageAttributes['adminLogId'] = {
                    DataType: 'String',
                    StringValue: insertAdminLog.id,
                }
                let messageBody= 'MessageBody via AD';
                await this.aws.sendEmailGeneratedByAdmin(sqsQueue,messageAttributes, messageBody);
            }
            insertResponse = "Email Sent";
        }else{
            return {error_message : "Please select a valid email type"};
        }
        return insertResponse
    }

    async emailValidation(emailString:string){
        let validated_email = "";
            if(emailString) {
                const emails = emailString.split(';');
                for(const email of emails){
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if(emailRegex.test(email)){
                        validated_email += email+";";
                    }else{
                        return "";
                        break;
                    }
                }
                validated_email = validated_email.replace(/;+$/, '');
            }else{
                validated_email = "";
            }
        return validated_email;
    }

    async getEmailEvent(emailTypeId:number){
        let emailEventName = '';
        const emailEvent = await this.dbObjService.findOne(this.referenceEmailEventRepository,"id",emailTypeId.toString());
        if(emailEvent)
            emailEventName = emailEvent.email_event;
        return emailEventName;
    }

    async sanatizePoNumber(poNumber:string){
        poNumber = poNumber.toUpperCase().trim();
        if(poNumber[0] === Constants.SELLER_PURCHASE_ORDER_INITIAL){
            poNumber = poNumber.replace(Constants.SELLER_PURCHASE_ORDER_INITIAL,Constants.BUYER_PURCHASE_ORDER_INITIAL)
        }
        return poNumber;
    }
}
