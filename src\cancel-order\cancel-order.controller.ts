import { Controller, Get, Post, Body, Response, ValidationPipe, UsePipes, Query, DefaultValuePipe, ParseIntPipe} from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { Constants } from 'src/Constants';
import { CancelOrderService } from './cancel-order.service';
import { SaveCancelOrderSettingDto, SaveCancelOrderDto } from './dto/cancel-order.dto';

const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;
const PAGINATION_RESPONSE_TAG = "meta";

@ApiTags('Cancel Order')
@Controller('cancel_order')
export class CancelOrderContoller {
  constructor(private cancelOrderService: CancelOrderService) {}

  @Get('/settings')
  async getCancelOrderSettings(@Response() res) {
    let responseData = {
      [responseTag]: await this.cancelOrderService.getCancelOrderSettings()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('settings')
  @UsePipes(ValidationPipe)
  async saveCancelOrderSetting(@Body() cancelOrderSettingDto:SaveCancelOrderSettingDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = cancelOrderSettingDto[payloadTag];

    let responseData = {
      [responseTag]: await this.cancelOrderService.saveCancelOrderSetting(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('/orders')
  async getOrderToCancel(@Response() res, @Query('page',new DefaultValuePipe(0), ParseIntPipe) page: number, @Query('limit',new DefaultValuePipe(0), ParseIntPipe) limit: number, @Query('search') search: string = "") { 
    let data = await this.cancelOrderService.getOrderToCancel( { page, limit, search })
    let meta;

    if (data?.items) {
      meta = data.meta;
      data = data.items;
    }
    let responseData = {
      [responseTag]: data,
      [PAGINATION_RESPONSE_TAG]: meta 
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  //This API is called through Admin Dashboard
  @Post('order')
  @UsePipes(ValidationPipe)
  async cancelOrderData(@Body() cancelOrderDto:SaveCancelOrderDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let payloadData = cancelOrderDto[payloadTag];
    let responseData = {
      [responseTag]: await this.cancelOrderService.cancelOrderDataByAdmin(adminId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  //This API is called through UI
  @Post('/user')
  @UsePipes(ValidationPipe)
  async cancelOrderByUser(@Body() cancelOrderDto:SaveCancelOrderDto, @Response() res){
    let adminId = res.locals.authorizedUserId;
    let superAdminId = res.locals.superAdminUserId;
    let payloadData = cancelOrderDto[payloadTag];
    let responseData = {
      [responseTag]: await this.cancelOrderService.cancelOrderByUser(adminId,payloadData, superAdminId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}