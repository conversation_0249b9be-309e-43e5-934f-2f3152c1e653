import { MiddlewareConsumer, Module } from '@nestjs/common';
import { RemoveSalesTaxController } from './remove-sales-tax.controller';
import { AdminPermissionMiddleware } from '@bryzos/base-library';
// import { LoggerMiddleware } from '../middleware/logger.middleware';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports: [ TypeOrmModule.forFeature([ UserPurchaseOrder, UserPurchaseOrderLine, UserPurchaseOrderLedger, CompanyBuyNowPayLater, ReferenceDataSettings, ReferenceDataStates, ReferenceDataSalesTax, ReferenceDataOrderStatus, ReferenceDataKeys, UserResaleCertificate, LogBryzosCreditLimit, User, AdminLogRemoveSalesTax, TaxExemptedPurchaseOrders ]), ],
//   controllers: [RemoveSalesTaxController],
//   providers: [RemoveSalesTaxService, AuthService, BaseLibraryService],
// })

@Module({
  imports: [SharedModule],
  controllers: [RemoveSalesTaxController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class RemoveSalesTaxModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AdminPermissionMiddleware, LoggerMiddleware)
      .forRoutes('remove-sales-tax');
  }
}
