import { DataBaseService } from '@bryzos/base-library';
import { AdminLogResalesCertificate, ReferenceDataResaleCertExpiration, ReferenceDataStates, User, UserResaleCertificate, CompanyResaleCertificate } from '@bryzos/extended-widget-library';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Constants } from '../../Constants';
import { AwsQueue } from '../../AwsQueue';
import { Constants as libConstants,AwsUtilityV3 } from '@bryzos/base-library';
import { CompanyResaleCertDeleteDto, CompanyResaleCertUploadDto, GetSignedUrl } from '../dto/update-widget-admin-dashboard.dto';

const responseErrorTag = Constants.ERROR_TAG;

@Injectable()
export class ResaleCertificateService {
    private dbServiceObj = new DataBaseService()

    constructor(@InjectRepository(UserResaleCertificate) private readonly userReasaleCertificateRepository: Repository<UserResaleCertificate>,
        @InjectRepository(ReferenceDataStates) private readonly referenceDataStatesRepository: Repository<ReferenceDataStates>,
        @InjectRepository(ReferenceDataResaleCertExpiration) private readonly referenceDataResaleCertExpirationRepository: Repository<ReferenceDataResaleCertExpiration>,
        @InjectRepository(User) private readonly userRepository: Repository<User>,
        @InjectRepository(AdminLogResalesCertificate) private readonly adminLogResalesCertificateRepository: Repository<AdminLogResalesCertificate>,
        @InjectRepository(CompanyResaleCertificate) private readonly companyResaleCertificateRepository: Repository<CompanyResaleCertificate>,

        private readonly awsQueue: AwsQueue,
        private readonly awsUtility: AwsUtilityV3
    ) {

    }

    async getResaleCertificate(params: { page: number, limit: number, search: string }) {
        let response = null;
        let meta;
        const paginationApplied = (+params.limit > 0 && +params.page > 0);

        const result = await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(
            this.userReasaleCertificateRepository,
            [
                { table: 'user', joinColumn: 'id', mainTableColumn: 'user_id', },
            ],
            [
                { column: 'is_active', operator: '=', value: true, table: 'user', },
                { column: 'status', operator: 'NOT IN', value: [Constants.EXPIRED], },
                { column: 'is_active', operator: '=', value: true, },
            ],
            {
                selectFields: [
                    'table1.*',
                    'table1.id AS user_resale_certificate_id',
                    'user.first_name AS first_name',
                    'user.last_name AS last_name',
                    'user.email_id',
                    'DATE_FORMAT(CONVERT_TZ(table1.created_date,"UTC","America/Chicago"), "%m/%d/%y %h:%i %p") as added_date',
                    'table1.added_by AS added_by'
                ],
            },
            { 'table1.created_date': 'DESC' },
            null,
            params
        );

        if (result === null) {
            return null;
        }
        if (paginationApplied) {
            if (Array.isArray(result.items) && result.items.length < 1) {
                return { items: [], meta: undefined };
            } else {
                response = result.items;
                meta = result.meta;
            }
        } else {
            if (Array.isArray(result) && result.length < 1) {
                return [];
            } else {
                response = result;
            }
        }

        const referenceDataStates = await this.dbServiceObj.findAll(this.referenceDataStatesRepository);
        // const users = await this.dbServiceObj.findAll(this.userRepository);
        const referenceDataResaleCerts = await this.dbServiceObj.findAll(this.referenceDataResaleCertExpirationRepository);

        response = response.map((data: any) =>
        ({
            'buyer_name': `${data.first_name} ${data.last_name}`,
            'state': this.getWidgetReferenceStateName(referenceDataStates, data.state_id),
            'certificate_status': data.status,
            'cerificate_url_s3': data.cerificate_url_s3,
            'resales_cert_id': data.user_resale_certificate_id,
            'expiry_limit': this.getExpiryStringValue(referenceDataResaleCerts, data.expiration_date),
            'approval_status': true,
            'added_date': data.added_date,
            'added_by': data.added_by
        })
        );

        if (paginationApplied) {
            return { items: response, meta: meta };
        } else {
            return response;
        }
    }

    getWidgetReferenceStateName(referenceDataStates, id) {
        let name = null;
        if (id) {
            name = referenceDataStates.find(data => data.id === id)?.value;
        }
        return name;
    }

    async getEmailAddressOfUser(users, userId) {
        let emailId = null;
        if (userId) {
            emailId = users.find(data => data.id === userId)?.email_id;
        }
        return emailId;
    }

    getExpiryStringValue(referenceDataResaleCerts, value) {
        let expiryString = null;
        if (value) {
            expiryString = referenceDataResaleCerts.find(data => data.expiration_value === value && data.is_active === 1)?.expiration_display_string;
        }
        return expiryString;
    }

    async updateWidgetResaleCertificate(adminId: string, id: string, approve: boolean) {
        const insert: any = {
            resales_certificate_id: id,
            event: approve ? libConstants.APPROVE : libConstants.UNAPPROVE,
            admin_id: adminId,
        };

        const updateResult = await this.dbServiceObj.updateByColumnId(this.userReasaleCertificateRepository, { status: approve ? libConstants.RESALES_APPROVED : libConstants.RESALES_REJECTED }, "id", id);
        if (!updateResult) {
            return { [responseErrorTag]: 'Something went wrong!' }
        }

        const saveLogResult = await this.setWidgetResaleLogs(insert);
        if (!saveLogResult) {
            return { [responseErrorTag]: 'Something went wrong!' }
        }

        await this.awsQueue.widgetResalesExemptionApprovalEmailUser(id);
        return updateResult;
    }

    async setWidgetResaleLogs(logData: { resales_certificate_id: string, event: string, admin_id: string }) {
        const updateDto: any = logData;

        const leftJoin = [
            { "table": "user", "joinColumn": "id", "mainTableColumn": "user_id" }
        ];
        const condition = [
            { "column": "is_active", "operator": "=", "value": true },
            { "column": "is_active", "operator": "=", "value": true, "table": "user" },
            { "column": "id", "operator": "=", "value": logData.resales_certificate_id },
        ]
        const certificateData = (await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userReasaleCertificateRepository, leftJoin, condition))?.[0];

        if(!certificateData) {
            return { [responseErrorTag]: 'Something went wrong!' }
        }

        updateDto.expiry_date= certificateData.expiration_date;

        if (certificateData?.user_id) {
            updateDto.user_id = certificateData.user_id;
        }
        const result = await this.dbServiceObj.saveData(updateDto, this.adminLogResalesCertificateRepository);
        if(!result) {
            return 
        }
        return result;
    }

    async getWidgetCertificateData(id: string) {
        const leftJoin = [
            { table: 'user', joinColumn: 'id', mainTableColumn: 'user_id' },
        ];
        const condition = [
            { column: 'is_active', operator: '=', value: true },
            { column: 'is_active', operator: '=', value: true, table: 'user' },
            { column: 'id', operator: '=', value: id },
        ];
        const mapperFields = {
            selectFields: [
                'table1.*',
                'table1.id AS user_resale_certificate_id',
                'user.first_name AS first_name',
                'user.last_name AS last_name',
            ]
        };
        const certificateData = (await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userReasaleCertificateRepository, leftJoin, condition, mapperFields))?.[0];
        if (!certificateData) {
            return { [responseErrorTag]: 'Something went wrong!' }
        }

        const referenceDataStates = await this.dbServiceObj.findAll(this.referenceDataStatesRepository);
        certificateData.state = await this.getWidgetReferenceStateName(referenceDataStates, certificateData.state_id);
        certificateData.admin_name = `${certificateData.first_name} ${certificateData.last_name}`
        certificateData.expiry_date = certificateData.expiration_date;

        return certificateData;
    }

    async getSignedS3Url(dto:GetSignedUrl) {
        let data = await this.awsUtility.getSignedS3Url(dto.object_key,dto.bucket_name,dto.expire_time);
        return data;
    }

    async uploadCompanyResaleCertificate(payload:CompanyResaleCertUploadDto,adminId:string){
        const companyId = payload.company_id;
        const resaleCertificates = payload.resale_certificate;
        let companyResaleCertDto = [];
        let stateIds = [];
        for(const resaleCertificate of resaleCertificates){
            
            resaleCertificate["admin_id"] = adminId;
            resaleCertificate["company_id"] = companyId;
            resaleCertificate["status"] = libConstants.APPROVED;
            resaleCertificate["added_by"] = libConstants.ADMIN;

            companyResaleCertDto = companyResaleCertDto.filter(obj => obj.state_id !== resaleCertificate.state_id);
            companyResaleCertDto.push(resaleCertificate);
            
            if(!stateIds.includes(resaleCertificate.state_id)){
                stateIds.push(resaleCertificate.state_id);
            }

        }

        const checkExistingCompanyResaleCert = await this.dbServiceObj.findManyByWhereAndWhereIn(this.companyResaleCertificateRepository,{company_id:companyId},"state_id",stateIds);
        let softDeleteCompanyResaleCert = []
        if(checkExistingCompanyResaleCert && checkExistingCompanyResaleCert.length > 0){
            softDeleteCompanyResaleCert = checkExistingCompanyResaleCert.map(obj => {
                return {
                    id : obj.id,
                    is_active : false,
                    deleted_by : adminId
                }
            })
        }

        const existingCertColumnContditions = [
            { columnName: 'company_id', operator: '=', value: companyId },
            { columnName: 'state_id', operator: 'IN', value: stateIds },
            { columnName: 'is_active', operator: '=', value: true },
        ]
        const existingResaleCertsOfUsers = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.userReasaleCertificateRepository,existingCertColumnContditions);

        const existingCertIds = existingResaleCertsOfUsers.map(obj => { return { id: obj.id, is_active: false }; });

        const userColumnContditions = [
            { columnName: 'company_id', operator: '=', value: companyId },
            { columnName: 'type', operator: '=', value: libConstants.BUYER },
            { columnName: 'is_active', operator: '=', value: true },
        ]
        const usersUnderCompany = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.userRepository,userColumnContditions);

        const userResaleCertDto = [];
        for(const user of usersUnderCompany){
            for(const resaleCertData of companyResaleCertDto){
                const newData = Object.assign({}, resaleCertData);
                delete newData["admin_id"];
                newData["user_id"] = user.id;
                newData["is_deletable"] = false;
                userResaleCertDto.push(newData);
            }
        }

        try{
            await this.dbServiceObj.saveData(softDeleteCompanyResaleCert,this.companyResaleCertificateRepository);
            await this.dbServiceObj.saveData(existingCertIds,this.userReasaleCertificateRepository);
            await this.dbServiceObj.saveData(companyResaleCertDto,this.companyResaleCertificateRepository);
            await this.dbServiceObj.saveData(userResaleCertDto,this.userReasaleCertificateRepository);
            return "Sales Tax Certificate Uploaded Successfully";
        }catch(er){
            return { error_message : "Something went wrong!!" };
        }
    }

    async getCompanyResaleCertificate(){
        const companyResaleCertConditions = [
            { columnName: 'is_active', operator: '=', value: true },
        ]
        const companyResaleCert = await this.dbServiceObj.findManyWithDynamiConditionsAndOperators(this.companyResaleCertificateRepository,companyResaleCertConditions);
        return companyResaleCert;
    }

    async deleteCompanyResaleCertificate(payload:CompanyResaleCertDeleteDto[],adminId:string){
        const deleteCompanyLevelDto = [];
        const certIds = [];
        for(const cert of payload){
            const companyDto = {
                id : cert.cert_id,
                is_active : false,
                deleted_by : adminId
            }
            deleteCompanyLevelDto.push(companyDto);
            certIds.push(cert.cert_id);
        }

        const getCompanyCertData = await this.dbServiceObj.findManyByWhereIn(this.companyResaleCertificateRepository,"id",certIds);
        
        if(getCompanyCertData.length === 0 ){ return { "error_message":"No certificates found for deletion." } }

        const companyId = getCompanyCertData[0].company_id;

        const stateIds = getCompanyCertData.map( obj => obj.state_id );

        const userCertDetails = await this.dbServiceObj.findManyByWhereAndWhereIn(this.userReasaleCertificateRepository,{company_id:companyId},'state_id',stateIds);
        
        let deleteUserResaleCert = [];
        if(userCertDetails.length > 0){
            deleteUserResaleCert = userCertDetails.map(obj => {return {
                    id : obj.id,
                    is_active : false
                }
            })
        }

        try{
            deleteCompanyLevelDto.length > 0 ? await this.dbServiceObj.saveData(deleteCompanyLevelDto,this.companyResaleCertificateRepository) : null;
            deleteUserResaleCert.length > 0 ? await this.dbServiceObj.saveData(deleteUserResaleCert,this.userReasaleCertificateRepository) : null;
            return "Certificate deleted successfully!";
        }catch(er){
            return { error_message : "Something went wrong" };
        }

    }
}
